import {repository} from '@loopback/repository';
import {
  SellContentItemRepository,
  SellContentSectionRepository,
} from '../repositories';
import {
  SellContentSectionDto,
  SellContentSection,
  SellContentItem,
  SellContentSectionWithRelations,
} from '../models';

export class SellContentSectionService {
  constructor(
    @repository(SellContentSectionRepository)
    private sectionRepo: SellContentSectionRepository,
    @repository(SellContentItemRepository)
    private itemRepo: SellContentItemRepository,
  ) {}

  async createSection(dto: SellContentSectionDto): Promise<SellContentSection> {
    const {items = [], id, ...sectionData} = dto;

    const transaction = await this.sectionRepo.dataSource.beginTransaction();
    try {
      let section: SellContentSection | null = null;

      if (id) {
        // Check if section with ID exists
        section = await this.sectionRepo.findOne(
          {
            where: {
              id,
            },
          },
          {transaction},
        );
      }

      if (!section) {
        // New section
        section = await this.sectionRepo.create(sectionData, {transaction});
      } else {
        // Update section and remove old items
        await this.sectionRepo.updateById(section.id, sectionData, {
          transaction,
        });

        await this.itemRepo.deleteAllHard(
          {sectionId: section.id},
          {transaction},
        );
      }

      // Re-create items with proper ordering
      const itemsWithOrder: Partial<SellContentItem>[] = items.map(
        (item, index) => ({
          ...item,
          sectionId: section.id,
          // Use existing displayOrder if provided, otherwise use array index + 1
          displayOrder: item.displayOrder ?? index + 1,
        }),
      );

      await this.itemRepo.createAll(itemsWithOrder, {transaction});
      await transaction.commit();

      return section;
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }

  async createBulkSections(
    sections: SellContentSectionDto[],
  ): Promise<SellContentSection[]> {
    const results: SellContentSection[] = [];

    for (const section of sections) {
      const created = await this.createSection(section); // handles create or update
      results.push(created);
    }

    return results;
  }
}
