{"openapi": "3.0.0", "info": {"title": "authentication", "version": "1.0.0", "description": "authentication", "contact": {}}, "paths": {"/.well-known/openid-configuration": {"get": {"x-controller-name": "IdentityServerController", "x-operation-name": "getConfig", "tags": ["IdentityServerController"], "security": [{"HTTPBearer": []}], "description": "To get the openid configuration", "responses": {"200": {"description": "OpenId Configuration", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "operationId": "IdentityServerController.getConfig"}}, "/active-users/{range}": {"get": {"x-controller-name": "LoginActivityController", "x-operation-name": "getActiveUsers", "tags": ["LoginActivityController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "LoginActivity model instance", "content": {"application/json": {"schema": {}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewLoginActivity   |\n", "parameters": [{"name": "range", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "LoginActivityController.getActiveUsers"}}, "/admins/count": {"get": {"x-controller-name": "AdminController", "x-operation-name": "count", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSubAdmin   |\n", "responses": {"200": {"description": "Admin model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Admin.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Admin>"}}}}], "operationId": "AdminController.count"}}, "/admins/{id}/status": {"patch": {"x-controller-name": "AdminController", "x-operation-name": "updateAdminStatus", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSubAdmin   |\n", "responses": {"200": {"description": "Return value of AdminController.updateAdminStatus", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Admin status update success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "INACTIVE"]}}}}}, "x-parameter-index": 1}, "operationId": "AdminController.updateAdminStatus"}}, "/admins/{id}": {"put": {"x-controller-name": "AdminController", "x-operation-name": "replaceById", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSubAdmin   |\n", "responses": {"200": {"description": "Return value of AdminController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Admin PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin"}}}, "x-parameter-index": 1}, "operationId": "AdminController.replaceById"}, "patch": {"x-controller-name": "AdminController", "x-operation-name": "updateById", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSubAdmin   |\n", "responses": {"200": {"description": "Return value of AdminController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Admin PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "AdminController.updateById"}, "get": {"x-controller-name": "AdminController", "x-operation-name": "findById", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSubAdmin   |\n", "responses": {"200": {"description": "Admin model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Filter"}}}}], "operationId": "AdminController.findById"}, "delete": {"x-controller-name": "AdminController", "x-operation-name": "deleteById", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteSubAdmin   |\n", "responses": {"200": {"description": "Return value of AdminController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Admin DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "AdminController.deleteById"}}, "/admins": {"post": {"x-controller-name": "AdminController", "x-operation-name": "create", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateSubAdmin   |\n", "responses": {"200": {"description": "Admin model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewAdmin"}}}}, "operationId": "AdminController.create"}, "patch": {"x-controller-name": "AdminController", "x-operation-name": "updateAll", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSubAdmin   |\n", "responses": {"200": {"description": "Admin PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Admin.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Admin>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminPartial"}}}}, "operationId": "AdminController.updateAll"}, "get": {"x-controller-name": "AdminController", "x-operation-name": "find", "tags": ["AdminController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSubAdmin   |\n", "responses": {"200": {"description": "Array of Admin model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdminWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Filter1"}}}}], "operationId": "AdminController.find"}}, "/auth/apple-oauth-redirect": {"get": {"x-controller-name": "AppleLoginController", "x-operation-name": "appleCallback", "tags": ["AppleLoginController"], "responses": {"200": {"description": "Apple Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "AppleLoginController.appleCallback"}}, "/auth/auth0": {"post": {"x-controller-name": "Auth0LoginController", "x-operation-name": "postLoginViaAuth0", "tags": ["Auth0LoginController"], "responses": {"200": {"description": "POST Call for auth0 based login", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "Auth0LoginController.postLoginViaAuth0"}, "get": {"x-controller-name": "Auth0LoginController", "x-operation-name": "loginViaAuth0", "tags": ["Auth0LoginController"], "responses": {"200": {"description": "POST Call for auth0 based login", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "deprecated": true, "parameters": [{"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "client_secret", "in": "query", "schema": {"type": "string"}}], "operationId": "Auth0LoginController.loginViaAuth0"}}, "/auth/auth0-auth-redirect": {"get": {"x-controller-name": "Auth0LoginController", "x-operation-name": "auth0Callback", "tags": ["Auth0LoginController"], "responses": {"200": {"description": "Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "Auth0LoginController.auth0Callback"}}, "/auth/azure": {"post": {"x-controller-name": "AzureLoginController", "x-operation-name": "postLoginViaAzure", "tags": ["AzureLoginController"], "description": "POST Call for azure based login", "responses": {"200": {"description": "Azure Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "AzureLoginController.postLoginViaAzure"}, "get": {"x-controller-name": "AzureLoginController", "x-operation-name": "getLoginViaAzure", "tags": ["AzureLoginController"], "description": "POST Call for azure based login", "responses": {"200": {"description": "Azure Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "deprecated": true, "parameters": [{"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "client_secret", "in": "query", "schema": {"type": "string"}}], "operationId": "AzureLoginController.getLoginViaAzure"}}, "/auth/azure-oauth-redirect": {"get": {"x-controller-name": "AzureLoginController", "x-operation-name": "azureCallback", "tags": ["AzureLoginController"], "responses": {"200": {"description": "Azure Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}, {"name": "session_state", "in": "query", "schema": {"type": "string"}}], "operationId": "AzureLoginController.azureCallback"}}, "/auth/change-password": {"patch": {"x-controller-name": "LoginController", "x-operation-name": "resetPassword", "tags": ["LoginController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "If User password successfully changed."}}, "description": "", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordPartial"}}}}, "operationId": "LoginController.resetPassword"}}, "/auth/check-qr-code": {"get": {"x-controller-name": "OtpController", "x-operation-name": "checkQr", "tags": ["OtpController"], "description": "Returns isGenerated:true if secret_key already exist", "responses": {"200": {"description": "secret_key already exists", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "code", "in": "header", "schema": {"type": "string"}}, {"name": "clientId", "in": "header", "schema": {"type": "string"}}], "operationId": "OtpController.checkQr"}}, "/auth/cognito": {"post": {"x-controller-name": "CognitoLoginController", "x-operation-name": "postLoginViaCognito", "tags": ["CognitoLoginController"], "responses": {"200": {"description": "POST Call for Cognito based login", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "CognitoLoginController.postLoginViaCognito"}, "get": {"x-controller-name": "CognitoLoginController", "x-operation-name": "loginViaCognito", "tags": ["CognitoLoginController"], "responses": {"200": {"description": "Cognito Token Response (Deprecated: Possible security issue if secret is passed via query params, please use the post endpoint)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "deprecated": true, "parameters": [{"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "client_secret", "in": "query", "schema": {"type": "string"}}], "operationId": "CognitoLoginController.loginViaCognito"}}, "/auth/cognito-auth-redirect": {"get": {"x-controller-name": "CognitoLoginController", "x-operation-name": "cognitoCallback", "tags": ["CognitoLoginController"], "responses": {"200": {"description": "Cognito Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "CognitoLoginController.cognitoCallback"}}, "/auth/create-qr-code": {"post": {"x-controller-name": "OtpController", "x-operation-name": "createQr", "tags": ["OtpController"], "description": "Generates a new qrCode for Authenticator App", "responses": {"200": {"description": "qrCode that you can use to generate codes in Authenticator App", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthTokenRequest"}}}}, "operationId": "OtpController.createQr"}}, "/auth/facebook": {"post": {"x-controller-name": "FacebookLoginController", "x-operation-name": "postLoginViaFacebook", "tags": ["FacebookLoginController"], "responses": {"200": {"description": "POST Call for Facebook based login", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "FacebookLoginController.postLoginViaFacebook"}}, "/auth/facebook-auth-redirect": {"get": {"x-controller-name": "FacebookLoginController", "x-operation-name": "facebookCallback", "tags": ["FacebookLoginController"], "responses": {"200": {"description": "Facebook Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "FacebookLoginController.facebookCallback"}}, "/auth/forget-password": {"post": {"x-controller-name": "ForgetPasswordController", "x-operation-name": "forgetPassword", "tags": ["ForgetPasswordController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Success Response."}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordDto"}}}}, "operationId": "ForgetPasswordController.forgetPassword"}}, "/auth/google": {"post": {"x-controller-name": "GoogleLoginController", "x-operation-name": "postLogin<PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": ["GoogleLoginController"], "responses": {"200": {"description": "POST Call for Google based login", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "GoogleLoginController.postLoginViaGoogle"}, "get": {"x-controller-name": "GoogleLoginController", "x-operation-name": "loginViaGoogle", "tags": ["GoogleLoginController"], "responses": {"200": {"description": "Google Token Response,\n         (Deprecated: Possible security issue if secret is passed via query params,\n          please use the post endpoint)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "deprecated": true, "parameters": [{"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "client_secret", "in": "query", "schema": {"type": "string"}}], "operationId": "GoogleLoginController.loginViaGoogle"}}, "/auth/google-auth-redirect": {"get": {"x-controller-name": "GoogleLoginController", "x-operation-name": "googleCallback", "tags": ["GoogleLoginController"], "responses": {"200": {"description": "Google Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "GoogleLoginController.googleCallback"}}, "/auth/instagram": {"post": {"x-controller-name": "InstagramLoginController", "x-operation-name": "postLoginViaInstagram", "tags": ["InstagramLoginController"], "responses": {"200": {"description": "POST Call for Instagram based login", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "InstagramLoginController.postLoginViaInstagram"}}, "/auth/instagram-auth-redirect": {"get": {"x-controller-name": "InstagramLoginController", "x-operation-name": "instagramCallback", "tags": ["InstagramLoginController"], "responses": {"200": {"description": "Instagram Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "InstagramLoginController.instagramCallback"}}, "/auth/keycloak": {"post": {"x-controller-name": "KeycloakLoginController", "x-operation-name": "postLoginViaKeycloak", "tags": ["KeycloakLoginController"], "description": "POST Call for keycloak based login", "responses": {"200": {"description": "Keycloak Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "KeycloakLoginController.postLoginViaKeycloak"}, "get": {"x-controller-name": "KeycloakLoginController", "x-operation-name": "loginViaKeycloak", "tags": ["KeycloakLoginController"], "responses": {"200": {"description": "Keycloak Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "deprecated": true, "parameters": [{"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "client_secret", "in": "query", "schema": {"type": "string"}}], "operationId": "KeycloakLoginController.loginViaKeycloak"}}, "/auth/keycloak-auth-redirect": {"get": {"x-controller-name": "KeycloakLoginController", "x-operation-name": "keycloakCallback", "tags": ["KeycloakLoginController"], "responses": {"200": {"description": "Keycloak Redirect Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "operationId": "KeycloakLoginController.keycloakCallback"}}, "/auth/login": {"post": {"x-controller-name": "LoginController", "x-operation-name": "login", "tags": ["LoginController"], "description": "Gets you the code that will be used for getting token (webapps)", "responses": {"200": {"description": "Auth Code that you can use to generate access and refresh tokens using the POST /auth/token API", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "operationId": "LoginController.login"}}, "/auth/login-token": {"post": {"x-controller-name": "LoginController", "x-operation-name": "loginWithClientUser", "tags": ["LoginController"], "description": "Gets you refresh token and access token in one hit. (mobile app)", "responses": {"200": {"description": "Token Response Model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "deprecated": true, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "operationId": "LoginController.loginWithClientUser"}}, "/auth/me": {"get": {"x-controller-name": "ProfileController", "x-operation-name": "me", "tags": ["ProfileController"], "security": [{"HTTPBearer": []}], "description": "To get the user details", "responses": {"200": {"description": "User Object", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "x-origin", "in": "header", "schema": {"type": "string"}}], "operationId": "ProfileController.me"}}, "/auth/oauth-apple": {"post": {"x-controller-name": "AppleLoginController", "x-operation-name": "postLoginViaApple", "tags": ["AppleLoginController"], "responses": {"200": {"description": "POST Call for Apple based login", "content": {}}}, "description": "", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "AppleLoginController.postLoginViaApple"}}, "/auth/reset-password": {"patch": {"x-controller-name": "ForgetPasswordController", "x-operation-name": "resetPassword", "tags": ["ForgetPasswordController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "If User password successfully changed."}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordWithClient"}}}}, "operationId": "ForgetPasswordController.resetPassword"}}, "/auth/saml": {"post": {"x-controller-name": "SamlLoginController", "x-operation-name": "postLoginViaSaml", "tags": ["SamlLoginController"], "description": "POST Call for saml based login", "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ClientAuthRequest"}}}}, "operationId": "SamlLoginController.postLoginViaSaml"}}, "/auth/saml-redirect": {"post": {"x-controller-name": "SamlLoginController", "x-operation-name": "oktaSamlCallback", "tags": ["SamlLoginController"], "responses": {"200": {"description": "okta auth callback", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object"}}}, "x-parameter-index": 1}, "operationId": "SamlLoginController.oktaSamlCallback"}}, "/auth/send-otp": {"post": {"x-controller-name": "OtpController", "x-operation-name": "sendOtp", "tags": ["OtpController"], "description": "Sends OTP", "responses": {"200": {"description": "Sends otp to user", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtpSendRequest"}}}}, "operationId": "OtpController.sendOtp"}}, "/auth/sign-up/create-token": {"post": {"x-controller-name": "SignupRequestController", "x-operation-name": "requestSignup", "tags": ["SignupRequestController"], "responses": {"204": {"description": "Sucess Response."}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignupRequestDto"}}}}, "operationId": "SignupRequestController.requestSignup"}}, "/auth/sign-up/create-user": {"post": {"x-controller-name": "SignupRequestController", "x-operation-name": "signupWithToken", "tags": ["SignupRequestController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Sucess Response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalUserProfileDto"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalUserProfileDto"}}}}, "operationId": "SignupRequestController.signupWithToken"}}, "/auth/sign-up/verify-token": {"get": {"x-controller-name": "SignupRequestController", "x-operation-name": "verifyInviteToken", "tags": ["SignupRequestController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Sucess Response."}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "operationId": "SignupRequestController.verifyInviteToken"}}, "/auth/switch-token": {"post": {"x-controller-name": "LoginController", "x-operation-name": "switchToken", "tags": ["LoginController"], "security": [{"HTTPBearer": []}], "description": "To switch the access-token", "responses": {"200": {"description": "Switch access token with the tenant id provided.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthRefreshTokenRequest"}}}}, "operationId": "LoginController.switchToken"}}, "/auth/token": {"post": {"x-controller-name": "LoginController", "x-operation-name": "getToken", "tags": ["LoginController"], "description": "Send the code received from the POST /auth/login api and get refresh token and access token (webapps)", "responses": {"200": {"description": "Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthTokenRequest"}}}}, "operationId": "LoginController.getToken"}}, "/auth/token-refresh": {"post": {"x-controller-name": "LoginController", "x-operation-name": "exchangeToken", "tags": ["LoginController"], "security": [{"HTTPBearer": []}], "description": "Gets you a new access and refresh token once your access token is expired", "responses": {"200": {"description": "New Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "device_id", "in": "header", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthRefreshTokenRequest"}}}}, "operationId": "LoginController.exchangeToken"}}, "/auth/verify-otp": {"post": {"x-controller-name": "OtpController", "x-operation-name": "verifyOtp", "tags": ["OtpController"], "description": "Gets you the code that will be used for getting token (webapps)", "responses": {"200": {"description": "Auth Code that you can use to generate access and refresh tokens using the POST /auth/token API", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtpLoginRequest"}}}}, "operationId": "OtpController.verifyOtp"}}, "/auth/verify-reset-password-link": {"get": {"x-controller-name": "ForgetPasswordController", "x-operation-name": "verifyResetPasswordLink", "tags": ["ForgetPasswordController"], "responses": {"200": {"description": "Check if <PERSON>ken Is Valid and not Expired."}}, "description": "", "parameters": [{"name": "token", "in": "query", "schema": {"type": "string"}, "required": true}], "operationId": "ForgetPasswordController.verifyResetPasswordLink"}}, "/auth/{provider}/callback": {"post": {"x-controller-name": "SocialLoginController", "x-operation-name": "getTokenFromSocialProvider", "tags": ["SocialLoginController"], "responses": {"200": {"description": "Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}, "description": "", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}}, {"name": "x-origin", "in": "header", "schema": {"type": "string"}}, {"name": "provider", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SocialLoginController.getTokenFromSocialProvider"}}, "/cognito/logout": {"post": {"x-controller-name": "LogoutController", "x-operation-name": "cognitoLogout", "tags": ["LogoutController"], "security": [{"HTTPBearer": []}], "description": "This API will log out the user from application as well as cognito", "responses": {"200": {"description": "Success Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "description": "This is the access token which is required to authenticate user."}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequestPartial"}}}, "x-parameter-index": 1}, "operationId": "LogoutController.cognitoLogout"}}, "/connect/endsession": {"post": {"x-controller-name": "IdentityServerController", "x-operation-name": "logout", "tags": ["IdentityServerController"], "security": [{"HTTPBearer": []}], "description": "To logout", "responses": {"200": {"description": "Success Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "description": "This is the access token which is required to authenticate user."}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthRefreshTokenRequestPartial"}}}, "x-parameter-index": 1}, "operationId": "IdentityServerController.logout"}}, "/connect/generate-keys": {"post": {"x-controller-name": "IdentityServerController", "x-operation-name": "generateKeys", "tags": ["IdentityServerController"], "description": "Generate the set of public and private keys", "responses": {"200": {"description": "JWKS Keys"}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "operationId": "IdentityServerController.generateKeys"}}, "/connect/get-keys": {"get": {"x-controller-name": "IdentityServerController", "x-operation-name": "get<PERSON><PERSON><PERSON>", "tags": ["IdentityServerController"], "description": "Get the public keys", "responses": {"200": {"description": "JWKS Keys"}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "operationId": "IdentityServerController.getKeys"}}, "/connect/rotate-keys": {"post": {"x-controller-name": "IdentityServerController", "x-operation-name": "rotateKeys", "tags": ["IdentityServerController"], "description": "Generate the set of public and private keys", "responses": {"200": {"description": "JWKS Keys"}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "operationId": "IdentityServerController.rotateKeys"}}, "/connect/token": {"post": {"x-controller-name": "IdentityServerController", "x-operation-name": "getToken", "tags": ["IdentityServerController"], "description": "Send the code received from the POST /auth/login api and get refresh token and access token (webapps)", "responses": {"200": {"description": "Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthTokenRequest"}}}}, "operationId": "IdentityServerController.getToken"}}, "/connect/userinfo": {"get": {"x-controller-name": "IdentityServerController", "x-operation-name": "me", "tags": ["IdentityServerController"], "security": [{"HTTPBearer": []}], "description": "To get the user details", "responses": {"200": {"description": "User Object", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "operationId": "IdentityServerController.me"}}, "/customers/count": {"get": {"x-controller-name": "CustomerController", "x-operation-name": "count", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCustomer   |\n", "responses": {"200": {"description": "Customer model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "customers.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Customer>"}}}}], "operationId": "CustomerController.count"}}, "/customers/{id}/status": {"patch": {"x-controller-name": "CustomerController", "x-operation-name": "updateCustomerStatus", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCustomer   |\n", "responses": {"200": {"description": "Return value of CustomerController.updateCustomerStatus", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Customer status update success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerStatusUpdateDto"}}}, "x-parameter-index": 1}, "operationId": "CustomerController.updateCustomerStatus"}}, "/customers/{id}": {"put": {"x-controller-name": "CustomerController", "x-operation-name": "replaceById", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCustomer   |\n", "responses": {"200": {"description": "Return value of CustomerController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Customer PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Customer"}}}, "x-parameter-index": 1}, "operationId": "CustomerController.replaceById"}, "patch": {"x-controller-name": "CustomerController", "x-operation-name": "updateById", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCustomer   |\n", "responses": {"200": {"description": "Return value of CustomerController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Customer PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPartial"}}}, "x-parameter-index": 1}, "operationId": "CustomerController.updateById"}, "get": {"x-controller-name": "CustomerController", "x-operation-name": "findById", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCustomer   |\n", "responses": {"200": {"description": "Customer model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/customers.Filter"}}}}], "operationId": "CustomerController.findById"}, "delete": {"x-controller-name": "CustomerController", "x-operation-name": "deleteById", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteCustomer   |\n", "responses": {"200": {"description": "Return value of CustomerController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Customer DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "CustomerController.deleteById"}}, "/customers": {"post": {"x-controller-name": "CustomerController", "x-operation-name": "create", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| Create<PERSON>ustomer   |\n", "responses": {"200": {"description": "Customer model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Customer"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewCustomer"}}}}, "operationId": "CustomerController.create"}, "patch": {"x-controller-name": "CustomerController", "x-operation-name": "updateAll", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCustomer   |\n", "responses": {"200": {"description": "Customer PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "customers.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Customer>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerPartial"}}}}, "operationId": "CustomerController.updateAll"}, "get": {"x-controller-name": "CustomerController", "x-operation-name": "find", "tags": ["CustomerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCustomer   |\n", "responses": {"200": {"description": "Array of Customer model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/customers.Filter1"}}}}], "operationId": "CustomerController.find"}}, "/faqs/count": {"get": {"x-controller-name": "FaqController", "x-operation-name": "count", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewFaq   |\n", "responses": {"200": {"description": "Faq model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "faqs.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Faq>"}}}}], "operationId": "FaqController.count"}}, "/faqs/{id}": {"put": {"x-controller-name": "FaqController", "x-operation-name": "replaceById", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateFaq   |\n", "responses": {"200": {"description": "Return value of FaqController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Faq PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}, "x-parameter-index": 1}, "operationId": "FaqController.replaceById"}, "patch": {"x-controller-name": "FaqController", "x-operation-name": "updateById", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateFaq   |\n", "responses": {"200": {"description": "Return value of FaqController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Faq PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqPartial"}}}, "x-parameter-index": 1}, "operationId": "FaqController.updateById"}, "get": {"x-controller-name": "FaqController", "x-operation-name": "findById", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewFaq   |\n", "responses": {"200": {"description": "Faq model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/faqs.Filter"}}}}], "operationId": "FaqController.findById"}, "delete": {"x-controller-name": "FaqController", "x-operation-name": "deleteById", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteFaq   |\n", "responses": {"200": {"description": "Return value of FaqController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Faq DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "FaqController.deleteById"}}, "/faqs": {"post": {"x-controller-name": "FaqController", "x-operation-name": "create", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateFaq   |\n", "responses": {"200": {"description": "Faq model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewFaq"}}}}, "operationId": "FaqController.create"}, "patch": {"x-controller-name": "FaqController", "x-operation-name": "updateAll", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateFaq   |\n", "responses": {"200": {"description": "Faq PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "faqs.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Faq>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FaqPartial"}}}}, "operationId": "FaqController.updateAll"}, "get": {"x-controller-name": "FaqController", "x-operation-name": "find", "tags": ["FaqController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewFaq   |\n", "responses": {"200": {"description": "Array of Faq model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FaqWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/faqs.Filter1"}}}}], "operationId": "FaqController.find"}}, "/firm-details/count": {"get": {"x-controller-name": "FirmDetailsController", "x-operation-name": "count", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "FirmDetails model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "firm_details.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<FirmDetails>"}}}}], "operationId": "FirmDetailsController.count"}}, "/firm-details/seller/{sellerId}": {"get": {"x-controller-name": "FirmDetailsController", "x-operation-name": "findOneBySellerId", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "FirmDetails model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDetailsWithRelations"}}}}}, "parameters": [{"name": "sellerId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/firm_details.Filter"}}}}], "operationId": "FirmDetailsController.findOneBySellerId"}}, "/firm-details/{id}": {"put": {"x-controller-name": "FirmDetailsController", "x-operation-name": "replaceById", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of FirmDetailsController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "FirmDetails PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDetails"}}}, "x-parameter-index": 1}, "operationId": "FirmDetailsController.replaceById"}, "patch": {"x-controller-name": "FirmDetailsController", "x-operation-name": "updateById", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of FirmDetailsController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "FirmDetails PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDetailsDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "FirmDetailsController.updateById"}, "get": {"x-controller-name": "FirmDetailsController", "x-operation-name": "findById", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "FirmDetails model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDetailsWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/firm_details.Filter"}}}}], "operationId": "FirmDetailsController.findById"}, "delete": {"x-controller-name": "FirmDetailsController", "x-operation-name": "deleteById", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteSeller   |\n", "responses": {"200": {"description": "Return value of FirmDetailsController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "FirmDetails DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "FirmDetailsController.deleteById"}}, "/firm-details": {"post": {"x-controller-name": "FirmDetailsController", "x-operation-name": "create", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateSeller   |\n", "responses": {"200": {"description": "FirmDetails model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDetails"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/newFirmDetails"}}}}, "operationId": "FirmDetailsController.create"}, "patch": {"x-controller-name": "FirmDetailsController", "x-operation-name": "updateAll", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "FirmDetails PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "firm_details.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<FirmDetails>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDetailsPartial"}}}}, "operationId": "FirmDetailsController.updateAll"}, "get": {"x-controller-name": "FirmDetailsController", "x-operation-name": "find", "tags": ["FirmDetailsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "Array of FirmDetails model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FirmDetailsWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/firm_details.Filter1"}}}}], "operationId": "FirmDetailsController.find"}}, "/firm-documents/count": {"get": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "count", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "FirmDocuments model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "firm_documents.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<FirmDocuments>"}}}}], "operationId": "FirmDocumentsController.count"}}, "/firm-documents/{id}": {"put": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "replaceById", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of FirmDocumentsController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "FirmDocuments PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDocuments"}}}, "x-parameter-index": 1}, "operationId": "FirmDocumentsController.replaceById"}, "patch": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "updateById", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of FirmDocumentsController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "FirmDocuments PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDocumentsPartial"}}}, "x-parameter-index": 1}, "operationId": "FirmDocumentsController.updateById"}, "get": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "findById", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "FirmDocuments model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDocumentsWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/firm_documents.Filter"}}}}], "operationId": "FirmDocumentsController.findById"}, "delete": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "deleteById", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteSeller   |\n", "responses": {"200": {"description": "Return value of FirmDocumentsController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "FirmDocuments DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "FirmDocumentsController.deleteById"}}, "/firm-documents": {"post": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "create", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateSeller   |\n", "responses": {"200": {"description": "FirmDocuments model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDocuments"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewFirmDocuments"}}}}, "operationId": "FirmDocumentsController.create"}, "patch": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "updateAll", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "FirmDocuments PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "firm_documents.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<FirmDocuments>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirmDocumentsPartial"}}}}, "operationId": "FirmDocumentsController.updateAll"}, "get": {"x-controller-name": "FirmDocumentsController", "x-operation-name": "find", "tags": ["FirmDocumentsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "Array of FirmDocuments model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FirmDocumentsWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/firm_documents.Filter1"}}}}], "operationId": "FirmDocumentsController.find"}}, "/google/logout": {"post": {"x-controller-name": "LogoutController", "x-operation-name": "googleLogout", "tags": ["LogoutController"], "security": [{"HTTPBearer": []}], "description": "This API will log out the user from application as well as google", "responses": {"200": {"description": "Success Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "description": "This is the access token which is required to authenticate user."}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequestPartial"}}}, "x-parameter-index": 1}, "operationId": "LogoutController.googleLogout"}}, "/keycloak/logout": {"post": {"x-controller-name": "LogoutController", "x-operation-name": "keycloakLogout", "tags": ["LogoutController"], "security": [{"HTTPBearer": []}], "description": "This API will log out the user from application as well as keycloak", "responses": {"200": {"description": "Success Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "description": "This is the access token which is required to authenticate user."}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequestPartial"}}}, "x-parameter-index": 1}, "operationId": "LogoutController.keycloakLogout"}}, "/login-activity/count": {"get": {"x-controller-name": "LoginActivityController", "x-operation-name": "count", "tags": ["LoginActivityController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "LoginActivity model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewLoginActivity   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "login_activity.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<LoginActivity>"}}}}], "operationId": "LoginActivityController.count"}}, "/login-activity/{id}": {"get": {"x-controller-name": "LoginActivityController", "x-operation-name": "findById", "tags": ["LoginActivityController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "LoginActivity model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginActivityWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewLoginActivity   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/login_activity.Filter"}}}}], "operationId": "LoginActivityController.findById"}}, "/login-activity": {"get": {"x-controller-name": "LoginActivityController", "x-operation-name": "find", "tags": ["LoginActivityController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of LoginActivity model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LoginActivityWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewLoginActivity   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/login_activity.Filter"}}}}], "operationId": "LoginActivityController.find"}}, "/logout": {"post": {"x-controller-name": "LogoutController", "x-operation-name": "logout", "tags": ["LogoutController"], "security": [{"HTTPBearer": []}], "description": "To logout", "responses": {"200": {"description": "Success Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "description": "This is the access token which is required to authenticate user."}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequestPartial"}}}, "x-parameter-index": 1}, "operationId": "LogoutController.logout"}}, "/permissions": {"get": {"x-controller-name": "PermissionsController", "x-operation-name": "getPermissions", "tags": ["PermissionsController"], "description": "", "responses": {"200": {"description": "Array of permissions data", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}, "category": {"type": "string"}}}}}}}}, "operationId": "PermissionsController.getPermissions"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/profile/{id}": {"patch": {"x-controller-name": "ProfileController", "x-operation-name": "updateById", "tags": ["ProfileController"], "description": "", "responses": {"200": {"description": "Return value of ProfileController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Profile PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPartial"}}}, "x-parameter-index": 1}, "operationId": "ProfileController.updateById"}}, "/seller-stores/count": {"get": {"x-controller-name": "SellerStoreController", "x-operation-name": "count", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "SellerStore model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "seller_stores.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<SellerStore>"}}}}], "operationId": "SellerStoreController.count"}}, "/seller-stores/seller/{sellerId}": {"get": {"x-controller-name": "SellerStoreController", "x-operation-name": "findBySellerId", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "SellerStore model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerStoreWithRelations"}}}}}, "parameters": [{"name": "sellerId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/seller_stores.Filter"}}}}], "operationId": "SellerStoreController.findBySellerId"}}, "/seller-stores/{id}": {"put": {"x-controller-name": "SellerStoreController", "x-operation-name": "replaceById", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of SellerStoreController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "SellerStore PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerStore"}}}, "x-parameter-index": 1}, "operationId": "SellerStoreController.replaceById"}, "patch": {"x-controller-name": "SellerStoreController", "x-operation-name": "updateById", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of SellerStoreController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "SellerStore PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerStorePartial"}}}, "x-parameter-index": 1}, "operationId": "SellerStoreController.updateById"}, "get": {"x-controller-name": "SellerStoreController", "x-operation-name": "findById", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "SellerStore model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerStoreWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/seller_stores.Filter"}}}}], "operationId": "SellerStoreController.findById"}, "delete": {"x-controller-name": "SellerStoreController", "x-operation-name": "deleteById", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteSeller   |\n", "responses": {"200": {"description": "Return value of SellerStoreController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "SellerStore DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SellerStoreController.deleteById"}}, "/seller-stores": {"post": {"x-controller-name": "SellerStoreController", "x-operation-name": "create", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateSeller   |\n", "responses": {"200": {"description": "SellerStore model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerStoreDto"}}}}}, "requestBody": {"content": {"application/json": {"title": "NewSellerStore", "schema": {"$ref": "#/components/schemas/SellerStore"}}}}, "operationId": "SellerStoreController.create"}, "patch": {"x-controller-name": "SellerStoreController", "x-operation-name": "updateAll", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "SellerStore PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "seller_stores.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<SellerStore>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerStorePartial"}}}}, "operationId": "SellerStoreController.updateAll"}, "get": {"x-controller-name": "SellerStoreController", "x-operation-name": "find", "tags": ["SellerStoreController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "Array of SellerStore model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SellerStoreWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/seller_stores.Filter1"}}}}], "operationId": "SellerStoreController.find"}}, "/sellers/count": {"get": {"x-controller-name": "SellerController", "x-operation-name": "count", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "Seller model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "sellers.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Seller>"}}}}], "operationId": "SellerController.count"}}, "/sellers/on-board": {"post": {"x-controller-name": "SellerController", "x-operation-name": "sellerOnBoard", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateSeller   |\n", "responses": {"200": {"description": "Seller onboarding success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Seller"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Onboard"}}}}, "operationId": "SellerController.sellerOnBoard"}}, "/sellers/{id}/status": {"patch": {"x-controller-name": "SellerController", "x-operation-name": "updateStatusById", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of SellerController.updateStatusById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Seller PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}, "verificationCode": {"type": "string"}, "rejectionReason": {"type": "string"}, "onHoldReason": {"type": "string"}}}}}, "x-parameter-index": 1}, "operationId": "SellerController.updateStatusById"}}, "/sellers/{id}": {"put": {"x-controller-name": "SellerController", "x-operation-name": "replaceById", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of SellerController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Seller PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Seller"}}}, "x-parameter-index": 1}, "operationId": "SellerController.replaceById"}, "patch": {"x-controller-name": "SellerController", "x-operation-name": "updateById", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Return value of SellerController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Seller PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerDetailsDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "SellerController.updateById"}, "get": {"x-controller-name": "SellerController", "x-operation-name": "findById", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "Seller model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/sellers.Filter"}}}}], "operationId": "SellerController.findById"}, "delete": {"x-controller-name": "SellerController", "x-operation-name": "deleteById", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteSeller   |\n", "responses": {"200": {"description": "Return value of SellerController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Seller DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SellerController.deleteById"}}, "/sellers": {"post": {"x-controller-name": "SellerController", "x-operation-name": "create", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateSeller   |\n", "responses": {"200": {"description": "Seller model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Seller"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewSeller"}}}}, "operationId": "SellerController.create"}, "patch": {"x-controller-name": "SellerController", "x-operation-name": "updateAll", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateSeller   |\n", "responses": {"200": {"description": "Seller PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "sellers.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Seller>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SellerPartial"}}}}, "operationId": "SellerController.updateAll"}, "get": {"x-controller-name": "SellerController", "x-operation-name": "find", "tags": ["SellerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewSeller   |\n", "responses": {"200": {"description": "Array of Seller model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SellerWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/sellers.Filter1"}}}}], "operationId": "SellerController.find"}}, "/user-tenant/count": {"get": {"x-controller-name": "UserTenantController", "x-operation-name": "count", "tags": ["UserTenantController"], "description": "", "responses": {"200": {"description": "UserTenant model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_tenants.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserTenant>"}}}}], "operationId": "UserTenantController.count"}}, "/user-tenant/{id}": {"get": {"x-controller-name": "ProfileController", "x-operation-name": "findUserTenantById", "tags": ["ProfileController"], "security": [{"HTTPBearer": []}], "description": "To get the user tenant details", "responses": {"200": {"description": "User Object", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_tenants.Filter"}}}}], "operationId": "ProfileController.findUserTenantById"}}, "/user-tenant": {"get": {"x-controller-name": "UserTenantController", "x-operation-name": "find", "tags": ["UserTenantController"], "description": "", "responses": {"200": {"description": "Array of UserTenant model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserTenantWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_tenants.Filter"}}}}], "operationId": "UserTenantController.find"}}, "/warehouses/count": {"get": {"x-controller-name": "WarehouseController", "x-operation-name": "count", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewWarehouse   |\n", "responses": {"200": {"description": "Warehouse model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "warehouses.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Warehouse>"}}}}], "operationId": "WarehouseController.count"}}, "/warehouses/{id}/seller": {"get": {"x-controller-name": "WarehouseSellerController", "x-operation-name": "getSeller", "tags": ["WarehouseSellerController"], "responses": {"200": {"description": "Seller belonging to Warehouse", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Seller"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "WarehouseSellerController.getSeller"}}, "/warehouses/{id}": {"put": {"x-controller-name": "WarehouseController", "x-operation-name": "replaceById", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateWarehouse   |\n", "responses": {"200": {"description": "Return value of WarehouseController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Warehouse PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Warehouse"}}}, "x-parameter-index": 1}, "operationId": "WarehouseController.replaceById"}, "patch": {"x-controller-name": "WarehouseController", "x-operation-name": "updateById", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateWarehouse   |\n", "responses": {"200": {"description": "Return value of WarehouseController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Warehouse PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehousePartial"}}}, "x-parameter-index": 1}, "operationId": "WarehouseController.updateById"}, "get": {"x-controller-name": "WarehouseController", "x-operation-name": "findById", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewWarehouse   |\n", "responses": {"200": {"description": "Warehouse model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/warehouses.Filter"}}}}], "operationId": "WarehouseController.findById"}, "delete": {"x-controller-name": "WarehouseController", "x-operation-name": "deleteById", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteWarehouse   |\n", "responses": {"200": {"description": "Return value of WarehouseController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Warehouse DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "WarehouseController.deleteById"}}, "/warehouses": {"post": {"x-controller-name": "WarehouseController", "x-operation-name": "create", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateWarehouse   |\n", "responses": {"200": {"description": "Warehouse model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Warehouse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewWarehouse"}}}}, "operationId": "WarehouseController.create"}, "patch": {"x-controller-name": "WarehouseController", "x-operation-name": "updateAll", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateWarehouse   |\n", "responses": {"200": {"description": "Warehouse PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "warehouses.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Warehouse>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehousePartial"}}}}, "operationId": "WarehouseController.updateAll"}, "get": {"x-controller-name": "WarehouseController", "x-operation-name": "find", "tags": ["WarehouseController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewWarehouse   |\n", "responses": {"200": {"description": "Array of Warehouse model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/warehouses.Filter1"}}}}], "operationId": "WarehouseController.find"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"LoginRequest": {"title": "LoginRequest", "type": "object", "description": "This is the signature for login request.", "properties": {"client_id": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "client_secret": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "username": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "password": {"type": "string", "description": "This property is supposed to be a string and is a required field"}}, "required": ["client_id", "username", "password"], "additionalProperties": false}, "TokenResponse": {"title": "TokenResponse", "type": "object", "description": "This is signature for successful token response.", "properties": {"accessToken": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "refreshToken": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "expires": {"type": "number"}, "pubnubToken": {"type": "string"}}, "required": ["accessToken", "refreshToken", "expires"], "additionalProperties": false}, "AuthTokenRequest": {"title": "AuthTokenRequest", "type": "object", "description": "This is the signature for requesting the accessToken and refreshToken.", "properties": {"code": {"type": "string"}, "clientId": {"type": "string"}}, "required": ["code", "clientId"], "additionalProperties": false}, "AuthRefreshTokenRequest": {"title": "AuthRefreshTokenRequest", "type": "object", "properties": {"refreshToken": {"type": "string"}, "tenantId": {"type": "string"}}, "required": ["refreshToken"], "additionalProperties": false}, "ResetPasswordPartial": {"title": "ResetPasswordPartial", "type": "object", "description": "This is a signature for reset password. (tsType: Partial<ResetPassword>, schemaOptions: { partial: true })", "properties": {"refreshToken": {"type": "string"}, "username": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "password": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "oldPassword": {"type": "string", "description": "This property is supposed to be a string and is a required field"}}, "additionalProperties": false}, "ResetPassword": {"title": "ResetPassword", "type": "object", "description": "This is a signature for reset password.", "properties": {"refreshToken": {"type": "string"}, "username": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "password": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "oldPassword": {"type": "string", "description": "This property is supposed to be a string and is a required field"}}, "required": ["refreshToken", "username", "password"], "additionalProperties": false}, "ClientAuthRequest": {"title": "ClientAuthRequest", "type": "object", "description": "This is signature for client authentication request.", "properties": {"client_id": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "client_secret": {"type": "string", "description": "This property is supposed to be a string and is a required field"}}, "required": ["client_id", "client_secret"], "additionalProperties": false}, "SuccessResponse": {"title": "SuccessResponse", "type": "object", "properties": {"success": {"type": "boolean"}}, "additionalProperties": true}, "RefreshTokenRequestPartial": {"title": "RefreshTokenRequestPartial", "type": "object", "description": "(tsType: Partial<RefreshTokenRequest>, schemaOptions: { partial: true })", "properties": {"refreshToken": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<RefreshTokenRequest>"}, "RefreshTokenRequest": {"title": "RefreshTokenRequest", "type": "object", "properties": {"refreshToken": {"type": "string"}}, "required": ["refreshToken"], "additionalProperties": false}, "OtpSendRequest": {"title": "OtpSendRequest", "type": "object", "description": "This is the signature for OTP login request.", "properties": {"client_id": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "client_secret": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "key": {"type": "string", "description": "This property is supposed to be a string and is a required field"}}, "required": ["client_id", "client_secret", "key"], "additionalProperties": false}, "OtpLoginRequest": {"title": "OtpLoginRequest", "type": "object", "description": "This is the signature for OTP login request.", "properties": {"key": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "otp": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "clientId": {"type": "string"}}, "required": ["key", "otp"], "additionalProperties": false}, "AuthUser": {"title": "AuthUser", "type": "object", "description": "This is the signature for authenticated user which holds permissions and role.", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "role": {"type": "string"}, "externalAuthToken": {"type": "string"}, "deviceInfo": {"type": "object", "description": "This property consists of two optional fields.\n    1. userAgent\n    2. deviceId "}, "age": {"type": "number"}, "externalRefreshToken": {"type": "string"}, "authClientId": {"type": "number"}, "userPreferences": {"type": "object"}, "tenantId": {"type": "string"}, "userTenantId": {"type": "string"}, "passwordExpiryTime": {"type": "string", "format": "date-time"}, "status": {"type": "number", "enum": [1, 2, 3, 0, 4]}}, "required": ["firstName", "username", "role"], "additionalProperties": false}, "Function": {}, "ForgetPasswordDto": {"title": "ForgetPasswordDto", "type": "object", "properties": {"username": {"type": "string"}, "client_id": {"type": "string"}, "client_secret": {"type": "string"}}, "required": ["username", "client_id", "client_secret"], "additionalProperties": false}, "AuthClient": {"title": "AuthClient", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "number"}, "clientId": {"type": "string"}, "clientSecret": {"type": "string"}, "secret": {"type": "string", "description": "Value can be a string or a private key."}, "redirectUrl": {"type": "string"}, "accessTokenExpiration": {"type": "number"}, "refreshTokenExpiration": {"type": "number"}, "authCodeExpiration": {"type": "number"}}, "required": ["clientId", "clientSecret", "secret", "accessTokenExpiration", "refreshTokenExpiration", "authCodeExpiration"], "additionalProperties": false}, "ResetPasswordWithClient": {"title": "ResetPasswordWithClient", "type": "object", "properties": {"token": {"type": "string"}, "password": {"type": "string"}, "client_id": {"type": "string"}, "client_secret": {"type": "string"}}, "required": ["token", "password", "client_id", "client_secret"], "additionalProperties": false}, "SignupRequestDto": {"title": "SignupRequestDto", "type": "object", "properties": {"email": {"type": "string"}, "data": {"type": "object"}}, "required": ["email"], "additionalProperties": false}, "LocalUserProfileDto": {"title": "LocalUserProfileDto", "type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "required": ["email", "password"], "additionalProperties": true}, "SignupRequest": {"title": "SignupRequest", "type": "object", "properties": {"email": {"type": "string"}, "expiry": {"type": "string"}}, "required": ["email"], "additionalProperties": false}, "LoginActivityWithRelations": {"title": "LoginActivityWithRelations", "type": "object", "description": "This is to maintain the daily login activity. (tsType: LoginActivityWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "string"}, "actor": {"type": "string"}, "tenantId": {"type": "string"}, "loginTime": {"type": "string", "format": "date-time"}, "tokenPayload": {"type": "string"}, "loginType": {"type": "string"}, "deviceInfo": {"type": "string"}, "ipAddress": {"type": "string"}}, "additionalProperties": false}, "Date": {}, "ActiveUsersFilter": {"title": "ActiveUsersFilter", "type": "object", "properties": {"inclusion": {"type": "boolean"}, "userIdentity": {"type": "string"}, "userIdentifier": {"type": "object"}}, "required": ["inclusion", "userIdentity", "userIdentifier"], "additionalProperties": false}, "AuthRefreshTokenRequestPartial": {"title": "AuthRefreshTokenRequestPartial", "type": "object", "description": "(tsType: Partial<AuthRefreshTokenRequest>, schemaOptions: { partial: true })", "properties": {"refreshToken": {"type": "string"}, "tenantId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<AuthRefreshTokenRequest>"}, "Warehouse": {"title": "Warehouse", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "sellerId": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "gstNumber": {"type": "string"}}, "required": ["name", "street", "city", "sellerId", "state", "zipCode", "country", "phone"], "additionalProperties": true}, "NewWarehouse": {"title": "NewWarehouse", "type": "object", "description": "(tsType: Omit<Warehouse, 'id'>, schemaOptions: { title: 'NewWarehouse', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "sellerId": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "gstNumber": {"type": "string"}}, "required": ["name", "street", "city", "sellerId", "state", "zipCode", "country", "phone"], "additionalProperties": true, "x-typescript-type": "Omit<Warehouse, 'id'>"}, "UserCredentialsWithRelations": {"title": "UserCredentialsWithRelations", "type": "object", "description": "(tsType: UserCredentialsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "userId": {"type": "string"}, "authProvider": {"type": "string"}, "authId": {"type": "string"}, "authToken": {"type": "string"}, "secretKey": {"type": "string", "description": "Secret for Authenticator app"}, "password": {"type": "string"}, "user": {"$ref": "#/components/schemas/UserWithRelations"}, "foreignKey": {}}, "required": ["userId", "authProvider"], "additionalProperties": false, "x-typescript-type": "UserCredentialsWithRelations"}, "TenantWithRelations": {"title": "TenantWithRelations", "type": "object", "description": "(tsType: TenantWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "address": {"type": "string"}, "website": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "country": {"type": "string"}, "status": {"type": "number", "description": "Tenant status - Active or Inactive", "enum": [1, 0]}, "tenantConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/TenantConfigWithRelations"}}}, "required": ["name", "key", "status"], "additionalProperties": false, "x-typescript-type": "TenantWithRelations"}, "TenantConfigWithRelations": {"title": "TenantConfigWithRelations", "type": "object", "description": "(tsType: TenantConfigWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "configKey": {"type": "string"}, "configValue": {"type": "object"}, "tenantId": {"type": "string"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["config<PERSON><PERSON>", "tenantId"], "additionalProperties": false, "x-typescript-type": "TenantConfigWithRelations"}, "UserWithRelations": {"title": "UserWithRelations", "type": "object", "description": "This is signature for user model. (tsType: UserWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}, "credentials": {"$ref": "#/components/schemas/UserCredentialsWithRelations"}, "defaultTenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}, "userTenants": {"type": "array", "items": {"$ref": "#/components/schemas/UserTenantWithRelations"}}}, "required": ["firstName", "username"], "additionalProperties": false}, "RoleWithRelations": {"title": "RoleWithRelations", "type": "object", "description": "(tsType: RoleWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "tenantId": {"type": "string"}, "allowedClients": {"type": "array", "items": {"type": "string"}}, "roleType": {"type": "number", "maximum": 15, "minimum": 0}, "permissions": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "tenantId", "roleType"], "additionalProperties": false, "x-typescript-type": "RoleWithRelations"}, "UserLevelPermissionWithRelations": {"title": "UserLevelPermissionWithRelations", "type": "object", "description": "(tsType: UserLevelPermissionWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "userTenantId": {"type": "string"}, "permission": {"type": "string"}, "allowed": {"type": "boolean"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}}, "required": ["userTenantId", "permission", "allowed"], "additionalProperties": false, "x-typescript-type": "UserLevelPermissionWithRelations"}, "UserTenantWithRelations": {"title": "UserTenantWithRelations", "type": "object", "description": "(tsType: UserTenantWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "status": {"type": "number", "maximum": 12, "minimum": 0}, "locale": {"type": "string"}, "tenantId": {"type": "string"}, "userId": {"type": "string"}, "roleId": {"type": "string"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}, "user": {"$ref": "#/components/schemas/UserWithRelations"}, "role": {"$ref": "#/components/schemas/RoleWithRelations"}, "userLevelPermissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserLevelPermissionWithRelations"}}}, "required": ["tenantId", "userId", "roleId"], "additionalProperties": false, "x-typescript-type": "UserTenantWithRelations"}, "SampleProductImageWithRelations": {"title": "SampleProductImageWithRelations", "type": "object", "description": "(tsType: SampleProductImageWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "thumbnail": {"type": "array", "items": {"type": "string"}}, "sellerId": {"type": "string"}, "seller": {"$ref": "#/components/schemas/SellerWithRelations"}, "foreignKey": {}}, "required": ["name", "thumbnail", "sellerId"], "additionalProperties": true, "x-typescript-type": "SampleProductImageWithRelations"}, "SellerStoreWithRelations": {"title": "SellerStoreWithRelations", "type": "object", "description": "(tsType: SellerStoreWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "storeName": {"type": "string"}, "legalName": {"type": "string"}, "website": {"type": "string"}, "signature": {"type": "string"}, "description": {"type": "string"}, "fbId": {"type": "string"}, "instaId": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "dp": {"type": "string"}, "banner": {"type": "string"}, "logo": {"type": "string"}, "workingHours": {"type": "number"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}, "unavailabilityStartDate": {"type": "string", "format": "date-time"}, "unavailabilityEndDate": {"type": "string", "format": "date-time"}, "sellerId": {"type": "string"}, "seller": {"$ref": "#/components/schemas/SellerWithRelations"}, "foreignKey": {}}, "required": ["sellerId"], "additionalProperties": true, "x-typescript-type": "SellerStoreWithRelations"}, "SellerWithRelations": {"title": "SellerWithRelations", "type": "object", "description": "(tsType: SellerWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "verificationCode": {"type": "string"}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}, "onHoldReason": {"type": "string"}, "vendorId": {"type": "string"}, "userTenantId": {"type": "string"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}, "sampleProductImages": {"type": "array", "items": {"$ref": "#/components/schemas/SampleProductImageWithRelations"}}, "sellerStore": {"$ref": "#/components/schemas/SellerStoreWithRelations"}}, "required": ["sellerId", "userTenantId"], "additionalProperties": true, "x-typescript-type": "SellerWithRelations"}, "WarehouseWithRelations": {"title": "WarehouseWithRelations", "type": "object", "description": "(tsType: WarehouseWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "sellerId": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "gstNumber": {"type": "string"}, "seller": {"$ref": "#/components/schemas/SellerWithRelations"}, "foreignKey": {}}, "required": ["name", "street", "city", "sellerId", "state", "zipCode", "country", "phone"], "additionalProperties": true, "x-typescript-type": "WarehouseWithRelations"}, "WarehousePartial": {"title": "WarehousePartial", "type": "object", "description": "(tsType: Partial<Warehouse>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "sellerId": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "gstNumber": {"type": "string"}}, "additionalProperties": true, "x-typescript-type": "Partial<Warehouse>"}, "Seller": {"title": "<PERSON><PERSON>", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "verificationCode": {"type": "string"}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}, "onHoldReason": {"type": "string"}, "vendorId": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["sellerId", "userTenantId"], "additionalProperties": true}, "NewSeller": {"title": "NewSeller", "type": "object", "description": "(tsType: Omit<Partial<Seller>, 'id' | 'sellerId' | 'verificationCode'>, schemaOptions: { title: 'NewSeller', exclude: [ 'id', 'sellerId', 'verificationCode' ], partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}, "onHoldReason": {"type": "string"}, "vendorId": {"type": "string"}, "userTenantId": {"type": "string"}}, "additionalProperties": true, "x-typescript-type": "Omit<Partial<Seller>, 'id' | 'sellerId' | 'verificationCode'>"}, "SampleProductDto": {"title": "SampleProductDto", "type": "object", "properties": {"name": {"type": "string"}, "thumbnails": {"type": "array", "items": {"type": "string", "minimum": 1}}}, "required": ["name", "thumbnails"], "additionalProperties": false}, "Onboard": {"title": "Onboard", "type": "object", "description": "(tsType: Omit<SellerDto, 'id' | 'sellerId'>, schemaOptions: { title: 'Onboard', exclude: [ 'id', 'sellerId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "storeName": {"type": "string"}, "legalName": {"type": "string"}, "website": {"type": "string"}, "signature": {"type": "string"}, "description": {"type": "string"}, "fbId": {"type": "string"}, "instaId": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "dp": {"type": "string"}, "banner": {"type": "string"}, "logo": {"type": "string"}, "workingHours": {"type": "number"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}, "unavailabilityStartDate": {"type": "string", "format": "date-time"}, "unavailabilityEndDate": {"type": "string", "format": "date-time"}, "sampleProductImages": {"type": "array", "items": {"$ref": "#/components/schemas/SampleProductDto"}}}, "additionalProperties": true, "x-typescript-type": "Omit<SellerDto, 'id' | 'sellerId'>"}, "SellerPartial": {"title": "SellerPartial", "type": "object", "description": "(tsType: Partial<Seller>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "verificationCode": {"type": "string"}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}, "onHoldReason": {"type": "string"}, "vendorId": {"type": "string"}, "userTenantId": {"type": "string"}}, "additionalProperties": true, "x-typescript-type": "Partial<Seller>"}, "SellerDetailsDtoPartial": {"title": "SellerDetailsDtoPartial", "type": "object", "description": "This is signature for user model. (tsType: Partial<SellerDetailsDto>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}, "vendorId": {"type": "string"}}, "additionalProperties": false}, "SellerStoreDto": {"title": "SellerStoreDto", "type": "object", "properties": {"storeName": {"type": "string"}, "legalName": {"type": "string"}, "website": {"type": "string"}, "description": {"type": "string"}, "fbId": {"type": "string"}, "instaId": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "dp": {"type": "string"}, "logo": {"type": "string"}, "banner": {"type": "string"}, "signature": {"type": "string"}, "sellerId": {"type": "string"}, "workingHours": {"type": "number"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}}, "required": ["addressLine1", "city", "state", "country", "pincode", "sellerId"], "additionalProperties": false}, "SellerStore": {"title": "SellerStore", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "storeName": {"type": "string"}, "legalName": {"type": "string"}, "website": {"type": "string"}, "signature": {"type": "string"}, "description": {"type": "string"}, "fbId": {"type": "string"}, "instaId": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "dp": {"type": "string"}, "banner": {"type": "string"}, "logo": {"type": "string"}, "workingHours": {"type": "number"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}, "unavailabilityStartDate": {"type": "string", "format": "date-time"}, "unavailabilityEndDate": {"type": "string", "format": "date-time"}, "sellerId": {"type": "string"}}, "required": ["sellerId"], "additionalProperties": true}, "SellerStorePartial": {"title": "SellerStorePartial", "type": "object", "description": "(tsType: Partial<SellerStore>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "storeName": {"type": "string"}, "legalName": {"type": "string"}, "website": {"type": "string"}, "signature": {"type": "string"}, "description": {"type": "string"}, "fbId": {"type": "string"}, "instaId": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "dp": {"type": "string"}, "banner": {"type": "string"}, "logo": {"type": "string"}, "workingHours": {"type": "number"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}, "unavailabilityStartDate": {"type": "string", "format": "date-time"}, "unavailabilityEndDate": {"type": "string", "format": "date-time"}, "sellerId": {"type": "string"}}, "additionalProperties": true, "x-typescript-type": "Partial<SellerStore>"}, "UserPartial": {"title": "UserPartial", "type": "object", "description": "This is signature for user model. (tsType: Partial<User>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}}, "additionalProperties": false}, "User": {"title": "User", "type": "object", "description": "This is signature for user model.", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}}, "required": ["firstName", "username"], "additionalProperties": false}, "FirmDocuments": {"title": "FirmDocuments", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "documentName": {"type": "string"}, "documentValue": {"type": "string"}, "firmDetailsId": {"type": "string"}}, "required": ["documentName", "documentValue", "firmDetailsId"], "additionalProperties": true}, "NewFirmDocuments": {"title": "NewFirmDocuments", "type": "object", "description": "(tsType: Omit<FirmDocuments, 'id'>, schemaOptions: { title: 'NewFirmDocuments', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "documentName": {"type": "string"}, "documentValue": {"type": "string"}, "firmDetailsId": {"type": "string"}}, "required": ["documentName", "documentValue", "firmDetailsId"], "additionalProperties": true, "x-typescript-type": "Omit<FirmDocuments, 'id'>"}, "FirmDetailsWithRelations": {"title": "FirmDetailsWithRelations", "type": "object", "description": "(tsType: FirmDetailsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "typeOfFirm": {"type": "string"}, "gstNumber": {"type": "string"}, "gstName": {"type": "string"}, "enrollmentNumber": {"type": "string"}, "panNumber": {"type": "string"}, "firmDocumentsRelation": {"type": "array", "items": {"$ref": "#/components/schemas/FirmDocumentsWithRelations"}}}, "required": ["sellerId"], "additionalProperties": true, "x-typescript-type": "FirmDetailsWithRelations"}, "FirmDocumentsWithRelations": {"title": "FirmDocumentsWithRelations", "type": "object", "description": "(tsType: FirmDocumentsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "documentName": {"type": "string"}, "documentValue": {"type": "string"}, "firmDetailsId": {"type": "string"}, "firmDetails": {"$ref": "#/components/schemas/FirmDetailsWithRelations"}, "foreignKey": {}}, "required": ["documentName", "documentValue", "firmDetailsId"], "additionalProperties": true, "x-typescript-type": "FirmDocumentsWithRelations"}, "FirmDocumentsPartial": {"title": "FirmDocumentsPartial", "type": "object", "description": "(tsType: Partial<FirmDocuments>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "documentName": {"type": "string"}, "documentValue": {"type": "string"}, "firmDetailsId": {"type": "string"}}, "additionalProperties": true, "x-typescript-type": "Partial<FirmDocuments>"}, "FirmDetails": {"title": "FirmDetails", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "typeOfFirm": {"type": "string"}, "gstNumber": {"type": "string"}, "gstName": {"type": "string"}, "enrollmentNumber": {"type": "string"}, "panNumber": {"type": "string"}}, "required": ["sellerId"], "additionalProperties": true}, "newFirmDetails": {"title": "newFirmDetails", "type": "object", "description": "(tsType: Omit<FirmDetailsDto, 'id'>, schemaOptions: { title: 'newFirmDetails', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "sellerId": {"type": "string"}, "typeOfFirm": {"type": "string"}, "gstNumber": {"type": "string"}, "gstName": {"type": "string"}, "enrollmentNumber": {"type": "string"}, "panNumber": {"type": "string"}, "firmDocuments": {"type": "array", "items": {"type": "object"}}}, "required": ["sellerId", "firmDocuments"], "additionalProperties": true, "x-typescript-type": "Omit<FirmDetailsDto, 'id'>"}, "FirmDetailsPartial": {"title": "FirmDetailsPartial", "type": "object", "description": "(tsType: Partial<FirmDetails>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "typeOfFirm": {"type": "string"}, "gstNumber": {"type": "string"}, "gstName": {"type": "string"}, "enrollmentNumber": {"type": "string"}, "panNumber": {"type": "string"}}, "additionalProperties": true, "x-typescript-type": "Partial<FirmDetails>"}, "FirmDetailsDtoPartial": {"title": "FirmDetailsDtoPartial", "type": "object", "description": "(tsType: Partial<FirmDetailsDto>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "sellerId": {"type": "string"}, "typeOfFirm": {"type": "string"}, "gstNumber": {"type": "string"}, "gstName": {"type": "string"}, "enrollmentNumber": {"type": "string"}, "panNumber": {"type": "string"}, "firmDocuments": {"type": "array", "items": {"type": "object"}}}, "additionalProperties": true, "x-typescript-type": "Partial<FirmDetailsDto>"}, "Faq": {"title": "Faq", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "status": {"type": "number"}, "priority": {"type": "number"}, "visibility": {"type": "number"}}, "required": ["question", "answer", "category", "status", "priority", "visibility"], "additionalProperties": true}, "NewFaq": {"title": "NewFaq", "type": "object", "description": "(tsType: Omit<Faq, 'id'>, schemaOptions: { title: 'NewFaq', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "status": {"type": "number"}, "priority": {"type": "number"}, "visibility": {"type": "number"}}, "required": ["question", "answer", "category", "status", "priority", "visibility"], "additionalProperties": true, "x-typescript-type": "Omit<Faq, 'id'>"}, "FaqWithRelations": {"title": "FaqWithRelations", "type": "object", "description": "(tsType: FaqWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "status": {"type": "number"}, "priority": {"type": "number"}, "visibility": {"type": "number"}}, "required": ["question", "answer", "category", "status", "priority", "visibility"], "additionalProperties": true, "x-typescript-type": "FaqWithRelations"}, "FaqPartial": {"title": "FaqPartial", "type": "object", "description": "(tsType: Partial<Faq>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "status": {"type": "number"}, "priority": {"type": "number"}, "visibility": {"type": "number"}}, "additionalProperties": true, "x-typescript-type": "Partial<Faq>"}, "Customer": {"title": "Customer", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "customerId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "userTenantId": {"type": "string"}, "zohoContactId": {"type": "string"}}, "required": ["id", "customerId", "userTenantId"], "additionalProperties": false}, "NewCustomer": {"title": "NewCustomer", "type": "object", "description": "This is signature for user model. (tsType: Omit<User, 'id'>, schemaOptions: { title: 'NewCustomer', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}}, "required": ["firstName", "username"], "additionalProperties": false}, "CustomerWithRelations": {"title": "CustomerWithRelations", "type": "object", "description": "(tsType: CustomerWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "customerId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "userTenantId": {"type": "string"}, "zohoContactId": {"type": "string"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}}, "required": ["id", "customerId", "userTenantId"], "additionalProperties": false, "x-typescript-type": "CustomerWithRelations"}, "CustomerPartial": {"title": "CustomerPartial", "type": "object", "description": "(tsType: Partial<Customer>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "customerId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "userTenantId": {"type": "string"}, "zohoContactId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Customer>"}, "CustomerStatusUpdateDto": {"title": "CustomerStatusUpdateDto", "type": "object", "properties": {"status": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}}, "required": ["status"], "additionalProperties": false}, "Admin": {"title": "Admin", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "adminId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["adminId", "userTenantId"], "additionalProperties": false}, "NewAdmin": {"title": "NewAdmin", "type": "object", "description": "This is signature for user model. (tsType: Omit<AdminDto, 'id'>, schemaOptions: { title: 'NewAdmin', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}}, "required": ["firstName", "username", "permissions"], "additionalProperties": false}, "AdminWithRelations": {"title": "AdminWithRelations", "type": "object", "description": "(tsType: AdminWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "adminId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "userTenantId": {"type": "string"}, "userTenant": {"$ref": "#/components/schemas/UserTenantWithRelations"}, "foreignKey": {}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserLevelPermissionWithRelations"}}}, "required": ["adminId", "userTenantId"], "additionalProperties": false, "x-typescript-type": "AdminWithRelations"}, "AdminPartial": {"title": "AdminPartial", "type": "object", "description": "(tsType: Partial<Admin>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "adminId": {"type": "string"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "string"}, "userTenantId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Admin>"}, "AdminDtoPartial": {"title": "AdminDtoPartial", "type": "object", "description": "This is signature for user model. (tsType: Partial<AdminDto>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "designation": {"type": "string"}, "dob": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "defaultTenantId": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "Admin.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "<PERSON><PERSON><PERSON>"}, "Admin.IncludeFilter.Items": {"title": "Admin.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["userTenant", "permissions"]}, "scope": {"$ref": "#/components/schemas/Admin.ScopeFilter"}}}, "Admin.Filter": {"type": "object", "title": "Admin.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "adminId": {"type": "boolean"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "adminId", "emailVerified", "phoneVerified", "status", "userTenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "Admin.Fields"}, "include": {"title": "Admin.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Admin.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Admin>"}, "Admin.Filter1": {"type": "object", "title": "Admin.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Admin.<PERSON><PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "adminId": {"type": "boolean"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "adminId", "emailVerified", "phoneVerified", "status", "userTenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "Admin.Fields"}, "include": {"title": "Admin.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Admin.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Admin>"}, "customers.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "customers.ScopeFilter"}, "customers.IncludeFilter.Items": {"title": "customers.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["userTenant"]}, "scope": {"$ref": "#/components/schemas/customers.ScopeFilter"}}}, "customers.Filter": {"type": "object", "title": "customers.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "customerId": {"type": "boolean"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "boolean"}, "userTenantId": {"type": "boolean"}, "zohoContactId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "customerId", "emailVerified", "phoneVerified", "status", "userTenantId", "zohoContactId"], "example": "deleted"}, "uniqueItems": true}], "title": "customers.Fields"}, "include": {"title": "customers.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/customers.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Customer>"}, "customers.Filter1": {"type": "object", "title": "customers.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "customers.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "customerId": {"type": "boolean"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "status": {"type": "boolean"}, "userTenantId": {"type": "boolean"}, "zohoContactId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "customerId", "emailVerified", "phoneVerified", "status", "userTenantId", "zohoContactId"], "example": "deleted"}, "uniqueItems": true}], "title": "customers.Fields"}, "include": {"title": "customers.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/customers.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Customer>"}, "faqs.Filter": {"type": "object", "title": "faqs.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "question": {"type": "boolean"}, "answer": {"type": "boolean"}, "category": {"type": "boolean"}, "status": {"type": "boolean"}, "priority": {"type": "boolean"}, "visibility": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "faqs.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Faq>"}, "faqs.Filter1": {"type": "object", "title": "faqs.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "faqs.<PERSON><PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "question": {"type": "boolean"}, "answer": {"type": "boolean"}, "category": {"type": "boolean"}, "status": {"type": "boolean"}, "priority": {"type": "boolean"}, "visibility": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "faqs.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Faq>"}, "firm_details.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "firm_details.ScopeFilter"}, "firm_details.IncludeFilter.Items": {"title": "firm_details.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["firmDocumentsRelation"]}, "scope": {"$ref": "#/components/schemas/firm_details.ScopeFilter"}}}, "firm_details.Filter": {"type": "object", "title": "firm_details.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "sellerId": {"type": "boolean"}, "typeOfFirm": {"type": "boolean"}, "gstNumber": {"type": "boolean"}, "gstName": {"type": "boolean"}, "enrollmentNumber": {"type": "boolean"}, "panNumber": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "firm_details.Fields"}, "include": {"title": "firm_details.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/firm_details.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<FirmDetails>"}, "firm_details.Filter1": {"type": "object", "title": "firm_details.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "firm_details.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "sellerId": {"type": "boolean"}, "typeOfFirm": {"type": "boolean"}, "gstNumber": {"type": "boolean"}, "gstName": {"type": "boolean"}, "enrollmentNumber": {"type": "boolean"}, "panNumber": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "firm_details.Fields"}, "include": {"title": "firm_details.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/firm_details.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<FirmDetails>"}, "firm_documents.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "firm_documents.ScopeFilter"}, "firm_documents.IncludeFilter.Items": {"title": "firm_documents.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["firmDetails"]}, "scope": {"$ref": "#/components/schemas/firm_documents.ScopeFilter"}}}, "firm_documents.Filter": {"type": "object", "title": "firm_documents.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "documentName": {"type": "boolean"}, "documentValue": {"type": "boolean"}, "firmDetailsId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "firm_documents.Fields"}, "include": {"title": "firm_documents.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/firm_documents.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<FirmDocuments>"}, "firm_documents.Filter1": {"type": "object", "title": "firm_documents.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "firm_documents.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "documentName": {"type": "boolean"}, "documentValue": {"type": "boolean"}, "firmDetailsId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "firm_documents.Fields"}, "include": {"title": "firm_documents.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/firm_documents.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<FirmDocuments>"}, "login_activity.Filter": {"type": "object", "title": "login_activity.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "login_activity.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "actor": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "loginTime": {"type": "boolean"}, "tokenPayload": {"type": "boolean"}, "loginType": {"type": "boolean"}, "deviceInfo": {"type": "boolean"}, "ipAddress": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "actor", "tenantId", "loginTime", "tokenPayload", "loginType", "deviceInfo", "ip<PERSON><PERSON><PERSON>"], "example": "id"}, "uniqueItems": true}], "title": "login_activity.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<LoginActivity>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "seller_stores.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "seller_stores.ScopeFilter"}, "seller_stores.IncludeFilter.Items": {"title": "seller_stores.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["seller"]}, "scope": {"$ref": "#/components/schemas/seller_stores.ScopeFilter"}}}, "seller_stores.Filter": {"type": "object", "title": "seller_stores.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "storeName": {"type": "boolean"}, "legalName": {"type": "boolean"}, "website": {"type": "boolean"}, "signature": {"type": "boolean"}, "description": {"type": "boolean"}, "fbId": {"type": "boolean"}, "instaId": {"type": "boolean"}, "addressLine1": {"type": "boolean"}, "addressLine2": {"type": "boolean"}, "city": {"type": "boolean"}, "state": {"type": "boolean"}, "country": {"type": "boolean"}, "pincode": {"type": "boolean"}, "dp": {"type": "boolean"}, "banner": {"type": "boolean"}, "logo": {"type": "boolean"}, "workingHours": {"type": "boolean"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}, "unavailabilityStartDate": {"type": "boolean"}, "unavailabilityEndDate": {"type": "boolean"}, "sellerId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "seller_stores.Fields"}, "include": {"title": "seller_stores.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/seller_stores.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<SellerStore>"}, "seller_stores.Filter1": {"type": "object", "title": "seller_stores.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "seller_stores.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "storeName": {"type": "boolean"}, "legalName": {"type": "boolean"}, "website": {"type": "boolean"}, "signature": {"type": "boolean"}, "description": {"type": "boolean"}, "fbId": {"type": "boolean"}, "instaId": {"type": "boolean"}, "addressLine1": {"type": "boolean"}, "addressLine2": {"type": "boolean"}, "city": {"type": "boolean"}, "state": {"type": "boolean"}, "country": {"type": "boolean"}, "pincode": {"type": "boolean"}, "dp": {"type": "boolean"}, "banner": {"type": "boolean"}, "logo": {"type": "boolean"}, "workingHours": {"type": "boolean"}, "hideWorkingHours": {"type": "boolean"}, "allowBulkOrder": {"type": "boolean"}, "allowCategorisation": {"type": "boolean"}, "unavailabilityStartDate": {"type": "boolean"}, "unavailabilityEndDate": {"type": "boolean"}, "sellerId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "seller_stores.Fields"}, "include": {"title": "seller_stores.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/seller_stores.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<SellerStore>"}, "sellers.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "sellers.ScopeFilter"}, "sellers.IncludeFilter.Items": {"title": "sellers.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["userTenant", "sampleProductImages", "sellerStore"]}, "scope": {"$ref": "#/components/schemas/sellers.ScopeFilter"}}}, "sellers.Filter": {"type": "object", "title": "sellers.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "sellerId": {"type": "boolean"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "verificationCode": {"type": "boolean"}, "status": {"type": "boolean"}, "rejectionReason": {"type": "boolean"}, "onHoldReason": {"type": "boolean"}, "vendorId": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "sellers.Fields"}, "include": {"title": "sellers.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/sellers.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Seller>"}, "sellers.Filter1": {"type": "object", "title": "sellers.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "sellers.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "sellerId": {"type": "boolean"}, "emailVerified": {"type": "boolean"}, "phoneVerified": {"type": "boolean"}, "verificationCode": {"type": "boolean"}, "status": {"type": "boolean"}, "rejectionReason": {"type": "boolean"}, "onHoldReason": {"type": "boolean"}, "vendorId": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "sellers.Fields"}, "include": {"title": "sellers.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/sellers.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Seller>"}, "user_tenants.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "user_tenants.ScopeFilter"}, "user_tenants.IncludeFilter.Items": {"title": "user_tenants.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["tenant", "user", "role", "userLevelPermissions"]}, "scope": {"$ref": "#/components/schemas/user_tenants.ScopeFilter"}}}, "user_tenants.Filter": {"type": "object", "title": "user_tenants.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "user_tenants.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "status": {"type": "boolean"}, "locale": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "userId": {"type": "boolean"}, "roleId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "status", "locale", "tenantId", "userId", "roleId"], "example": "deleted"}, "uniqueItems": true}], "title": "user_tenants.Fields"}, "include": {"title": "user_tenants.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/user_tenants.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserTenant>"}, "warehouses.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "warehouses.ScopeFilter"}, "warehouses.IncludeFilter.Items": {"title": "warehouses.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["seller"]}, "scope": {"$ref": "#/components/schemas/warehouses.ScopeFilter"}}}, "warehouses.Filter": {"type": "object", "title": "warehouses.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "street": {"type": "boolean"}, "city": {"type": "boolean"}, "sellerId": {"type": "boolean"}, "state": {"type": "boolean"}, "zipCode": {"type": "boolean"}, "country": {"type": "boolean"}, "phone": {"type": "boolean"}, "email": {"type": "boolean"}, "gstNumber": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "warehouses.Fields"}, "include": {"title": "warehouses.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/warehouses.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Warehouse>"}, "warehouses.Filter1": {"type": "object", "title": "warehouses.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "warehouses.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "street": {"type": "boolean"}, "city": {"type": "boolean"}, "sellerId": {"type": "boolean"}, "state": {"type": "boolean"}, "zipCode": {"type": "boolean"}, "country": {"type": "boolean"}, "phone": {"type": "boolean"}, "email": {"type": "boolean"}, "gstNumber": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "warehouses.Fields"}, "include": {"title": "warehouses.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/warehouses.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Warehouse>"}}}, "servers": [{"url": "/"}]}