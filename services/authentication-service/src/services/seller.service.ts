import {
  injectable,
  BindingScope,
  inject,
  Getter,
  service,
} from '@loopback/core';
import {
  SampleProductImageRepository,
  SellerRepository,
  SellerStoreRepository,
} from '../repositories';
import {AnyObject, IsolationLevel, repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {
  Customer,
  IAuthUserWithTenant,
  SampleProductImage,
  Seller,
  SellerDto,
  SellerStore,
} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {Transaction} from '@loopback/sequelize';
import {SellerStatus} from '../enums/seller-status.enum';
import {SellerStoreService} from './seller-store.service';
import {
  UserLevelPermissionRepository,
  UserRepository,
  UserTenantRepository,
} from '@sourceloop/authentication-service';
import {SellerDetailsDto} from '../models/seller-details-dto.model';
import {PREFIXES} from '@local/core';
import {CustomerService} from './customer.service';
import {NotificationHelperService} from './notification-helper.service';

@injectable({scope: BindingScope.TRANSIENT})
export class SellerService {
  constructor(
    @repository(SellerRepository)
    private readonly sellerRepository: SellerRepository,
    @service(SellerStoreService)
    private readonly sellerStoreService: SellerStoreService,
    @repository(SampleProductImageRepository)
    private readonly sampleProductImageRepository: SampleProductImageRepository,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @repository(UserRepository)
    private readonly userRepository: UserRepository,
    @repository(UserTenantRepository)
    private readonly userTenantRepository: UserTenantRepository,
    @repository(UserLevelPermissionRepository)
    private readonly userLevelPermissionRepository: UserLevelPermissionRepository,
    @repository(SellerStoreRepository)
    private readonly sellerStoreRepository: SellerStoreRepository,
    @service(CustomerService)
    private readonly customerService: CustomerService,
    @service(NotificationHelperService)
    private readonly notificationHelper: NotificationHelperService,
  ) {}

  async generateSellerId(): Promise<string> {
    const lastCreatedEntry =
      await this.sellerRepository.findOneIncludeSoftDelete({
        order: ['created_on DESC'],
        fields: {sellerId: true},
      });

    if (!lastCreatedEntry) {
      return `${PREFIXES.SELLER}-000001`;
    }
    const sequenceNumber = Number(lastCreatedEntry.sellerId.split('-')[1]);
    if (!sequenceNumber || isNaN(sequenceNumber)) {
      throw HttpErrors.BadRequest('Failed to generate seller Id');
    }
    return `${PREFIXES.SELLER}-${(sequenceNumber + 1)
      .toString()
      .padStart(6, '0')}`;
  }

  async generateVerificationCode(): Promise<string> {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let verificationCode = '';
    for (let i = 0; i < 6; i++) {
      verificationCode += chars.charAt(
        Math.floor(Math.random() * chars.length),
      );
    }
    return verificationCode;
  }

  async createNewSeller(
    seller: Partial<Omit<Seller, 'id, sellerId, verificationCode'>>,
    transaction?: Transaction,
  ): Promise<Seller> {
    const user = await this.getCurrentUser();
    const sellerId = await this.generateSellerId();
    const verificationCode = await this.generateVerificationCode();
    const newSeller = new Seller({
      emailVerified: seller.emailVerified ?? false,
      phoneVerified: seller.phoneVerified ?? false,
      verificationCode: verificationCode ?? '',
      status: seller.status ?? SellerStatus.PENDING,
      rejectionReason: seller.rejectionReason ?? '',
      userTenantId: seller.userTenantId ?? user.userTenantId,
      sellerId,
    });
    const createdSeller = await this.sellerRepository.create(newSeller, {
      transaction,
    });
    await this.customerService.createNewCustomer(
      new Customer({userTenantId: seller.userTenantId ?? user.userTenantId}),
      transaction,
    );
    return createdSeller;
  }

  async sellerOnBoard(sellerDto: Omit<SellerDto, 'id' | 'sellerId'>) {
    const tx = await this.sellerRepository.dataSource.beginTransaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });
    try {
      const user = await this.getCurrentUser();
      let seller = await this.sellerRepository.findOne({
        where: {userTenantId: user.userTenantId},
      });
      if (!seller?.id) {
        seller = await this.createNewSeller({}, tx);
      }
      const {sampleProductImages, ...rest} = sellerDto;
      const sellerStore = new SellerStore({
        ...rest,
        sellerId: seller.id,
      });
      await this.sellerStoreService.createSellerStore(sellerStore, tx);

      // Create sample product images
      if (sampleProductImages?.length) {
        await Promise.all(
          sampleProductImages.map(image =>
            this.sampleProductImageRepository.create(
              new SampleProductImage({
                name: image.name,
                thumbnail: image.thumbnails,
                sellerId: seller.id,
              }),
              {transaction: tx},
            ),
          ),
        );
      }
      await tx.commit();

      // Send congratulations email and SMS (non-blocking)
      if (seller.id) {
        this.sendOnboardingNotifications(seller.id).catch((error: any) => {
          console.error('Error sending onboarding notifications:', error);
          // Don't throw error to avoid breaking the onboarding flow
        });
      }

      return seller;
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }

  async updateStatusById(
    id: string,
    sellerPayload: {
      status: string;
      verificationCode?: string;
      rejectionReason?: string;
      onHoldReason?: string;
    },
  ): Promise<void> {
    const existingSeller = await this.sellerRepository.findById(id);

    if (sellerPayload.status === SellerStatus.APPROVED) {
      if (!sellerPayload.verificationCode) {
        throw new HttpErrors.BadRequest(
          'Verification code is required when status is APPROVED',
        );
      }
      if (sellerPayload.verificationCode !== existingSeller.verificationCode) {
        throw new HttpErrors.BadRequest('Invalid verification code');
      }
    }

    if (
      sellerPayload.status === SellerStatus.REJECTED &&
      !sellerPayload.rejectionReason
    ) {
      throw new HttpErrors.BadRequest(
        'Rejection reason is required when status is REJECTED',
      );
    }

    if (
      sellerPayload.status === SellerStatus.ON_HOLD &&
      !sellerPayload.onHoldReason
    ) {
      throw new HttpErrors.BadRequest(
        'On hold reason is required when status is ON_HOLD',
      );
    }

    if (sellerPayload.status === SellerStatus.REJECTED) {
      await this.sellerStoreRepository.deleteAll({
        sellerId: existingSeller?.id,
      });
    }

    if (sellerPayload.status === SellerStatus.INACTIVE) {
      // eslint-disable-next-line @typescript-eslint/return-await
      return await this.sellerRepository.updateById(id, {
        status: sellerPayload.status,
      });
    }

    await this.sellerRepository.updateById(id, {
      status: sellerPayload.status,
      verificationCode: existingSeller.verificationCode,
      rejectionReason:
        sellerPayload.rejectionReason ?? existingSeller.rejectionReason,
      onHoldReason: sellerPayload.onHoldReason ?? existingSeller.onHoldReason,
    });
  }

  async updateSeller(
    sellerId: string,
    sellerDto: Partial<SellerDetailsDto>,
  ): Promise<void> {
    const tx = await this.userRepository.dataSource.beginTransaction(
      IsolationLevel.READ_COMMITTED,
    );

    const {permissions} = sellerDto;

    try {
      const seller = await this.sellerRepository.findOne({
        where: {id: sellerId},
      });

      if (!seller) {
        throw new HttpErrors.NotFound('Seller not found');
      }

      const userTenant = await this.userTenantRepository.findOne({
        where: {id: seller.userTenantId},
      });

      if (!userTenant) {
        throw new HttpErrors.NotFound('UserTenant not found');
      }
      const userModel: AnyObject = {};
      Object.keys(
        this.userRepository.entityClass.definition.properties,
      ).forEach(element => {
        if (
          element in sellerDto &&
          sellerDto[element as keyof typeof sellerDto] !== undefined
        ) {
          userModel[element] = sellerDto[element as keyof typeof sellerDto];
        }
      });
      if (Object.keys(userModel).length) {
        await this.userRepository.updateAll(
          userModel,
          {id: userTenant.userId},
          {transaction: tx},
        );
      }

      if (permissions && Array.isArray(permissions)) {
        await this.userLevelPermissionRepository.deleteAll(
          {userTenantId: userTenant.id},
          {transaction: tx},
        );

        const permissionPromises = permissions.map(permission =>
          this.userLevelPermissionRepository.create(
            {
              userTenantId: userTenant.id,
              permission,
              allowed: true,
            },
            {transaction: tx},
          ),
        );
        await Promise.all(permissionPromises);
      }

      const sellerModel: AnyObject = {};
      Object.keys(
        this.sellerRepository.entityClass.definition.properties,
      ).forEach(element => {
        if (
          element in sellerDto &&
          sellerDto[element as keyof typeof sellerDto] !== undefined
        ) {
          sellerModel[element] = sellerDto[element as keyof typeof sellerDto];
        }
      });
      if (Object.keys(sellerModel).length) {
        await this.sellerRepository.updateAll(
          sellerModel,
          {id: seller.id},
          {transaction: tx},
        );
      }

      await tx.commit();
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }

  async deleteSeller(sellerId: string): Promise<void> {
    const tx = await this.userRepository.dataSource.beginTransaction(
      IsolationLevel.READ_COMMITTED,
    );

    try {
      const seller = await this.sellerRepository.findOne({
        where: {id: sellerId},
      });

      if (!seller) {
        throw new HttpErrors.NotFound('Seller not found');
      }

      const userTenant = await this.userTenantRepository.findById(
        seller.userTenantId,
      );

      if (!userTenant) {
        throw new HttpErrors.NotFound('User tenant record not found');
      }

      const userId = userTenant.userId;

      await Promise.all([
        this.userRepository.credentials(userId).delete({transaction: tx}),
        this.userLevelPermissionRepository.deleteAll(
          {userTenantId: userTenant.id},
          {transaction: tx},
        ),
        this.sellerRepository.deleteById(seller.id, {transaction: tx}),
        this.userTenantRepository.deleteById(userTenant.id, {transaction: tx}),
        this.userRepository.deleteById(userId, {transaction: tx}),
      ]);

      await tx.commit();
    } catch (error) {
      await tx.rollback();
      console.error('Error in deleteSeller:', error);
      throw error;
    }
  }

  private async sendOnboardingNotifications(sellerId: string): Promise<void> {
    try {
      // Get seller with user details
      const seller = await this.sellerRepository.findById(sellerId);
      const userTenant = await this.userTenantRepository.findById(
        seller.userTenantId,
        {include: [{relation: 'user'}]},
      );

      if (!userTenant?.user) {
        console.error('User not found for seller:', sellerId);
        return;
      }

      const user = userTenant.user;
      const userName =
        `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Seller';

      // Prepare email template data
      const emailData = {
        productName: 'Ecomdukes',
        supportId: '<EMAIL>',
        brand: 'Ecomdukes',
      };

      // Send congratulations email
      if (user.email) {
        await this.notificationHelper.sendEmail(
          'seller-onboard-congratulations.hbs',
          'Welcome to Ecomdukes - Registration Successful! 🎉',
          emailData,
          user.email,
          userName,
        );
      }

      // Send congratulations SMS
      if (user.phone) {
        const smsMessage = `Congratulations ${userName}! Your Ecomdukes seller account has been successfully created and is under review. You'll receive an email notification once approved. Welcome aboard! - Team Ecomdukes`;

        await this.notificationHelper.sendSMS(smsMessage, user.phone, userName);
      }
    } catch (error) {
      console.error('Error in sendOnboardingNotifications:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }
}
