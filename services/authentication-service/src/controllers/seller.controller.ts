import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Seller, SellerDto} from '../models';
import {SellerRepository} from '../repositories';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {service} from '@loopback/core';
import {S3Service, SellerService} from '../services';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {SellerDetailsDto} from '../models/seller-details-dto.model';

const basePath = '/sellers';
export class SellerController {
  constructor(
    @repository(SellerRepository)
    public sellerRepository: SellerRepository,
    @service(SellerService)
    public sellerService: SellerService,
    @service(S3Service)
    private readonly s3Service: S3Service,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Seller model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Seller)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Seller, {
            title: 'NewSeller',
            exclude: ['id', 'sellerId', 'verificationCode'],
            partial: true,
          }),
        },
      },
    })
    seller: Partial<Omit<Seller, 'id, sellerId,verificationCode'>>,
  ): Promise<Seller> {
    return this.sellerService.createNewSeller(seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSeller]})
  @post(`${basePath}/on-board`)
  @response(STATUS_CODE.OK, {
    description: 'Seller onboarding success',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Seller)}},
  })
  async sellerOnBoard(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerDto, {
            title: 'Onboard',
            exclude: ['id', 'sellerId'],
          }),
        },
      },
    })
    sellerDto: Omit<SellerDto, 'id' | 'sellerId'>,
  ): Promise<Seller> {
    return this.sellerService.sellerOnBoard(sellerDto);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Seller model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Seller) where?: Where<Seller>): Promise<Count> {
    return this.sellerRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Seller model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Seller, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Seller) filter?: Filter<Seller>): Promise<Seller[]> {
    return this.sellerRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Seller PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Seller, {partial: true}),
        },
      },
    })
    seller: Seller,
    @param.where(Seller) where?: Where<Seller>,
  ): Promise<Count> {
    return this.sellerRepository.updateAll(seller, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Seller model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Seller, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Seller, {exclude: 'where'})
    filter?: FilterExcludingWhere<Seller>,
  ): Promise<Seller> {
    return this.sellerRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(204, {
    description: 'Seller PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerDetailsDto, {partial: true}),
        },
      },
    })
    seller: Partial<SellerDetailsDto>,
  ): Promise<void> {
    await this.sellerService.updateSeller(id, seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}/status`)
  @response(204, {
    description: 'Seller PATCH success',
  })
  async updateStatusById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['status'],
            properties: {
              status: {type: 'string'},
              verificationCode: {type: 'string'},
              rejectionReason: {type: 'string'},
              onHoldReason: {type: 'string'},
            },
          },
        },
      },
    })
    seller: {
      status: string;
      verificationCode?: string;
      rejectionReason?: string;
      onHoldReason?: string;
    },
  ): Promise<void> {
    await this.sellerService.updateStatusById(id, seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(204, {
    description: 'Seller PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() seller: Seller,
  ): Promise<void> {
    await this.sellerRepository.replaceById(id, seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSeller]})
  @del(`${basePath}/{id}`)
  @response(204, {
    description: 'Seller DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerService.deleteSeller(id);
  }
}
