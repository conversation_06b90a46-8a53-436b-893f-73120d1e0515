'use client';
import { Typo<PERSON>, Grid, Stack, Card, CardContent, Box } from '@mui/material';
import { useGetUserQuery } from '../../redux/auth/authApiSlice';
import Loader from 'components/Loader';
import Image from 'next/image';
import { useMemo } from 'react';
import { useGetSellerByIdQuery } from 'redux/auth/sellerApiSlice';

const OnHoldStatus = () => {
  const { data: user } = useGetUserQuery();
  const sellerId = useMemo(() => {
    return user?.profileId ?? '';
  }, [user]);
  const { data: seller, isLoading: sellerLoading } = useGetSellerByIdQuery(sellerId, { skip: !sellerId });

  if (sellerLoading) return <Loader />;
  return (
    <Stack spacing={2} alignItems="center" justifyContent="center" minHeight="100vh" sx={{ px: 2, mb: { xs: -0.5, sm: 0.5 } }}>
      <Card
        sx={{
          width: '100%',
          maxWidth: 939,
          p: { xs: 2, sm: 4 },
          borderRadius: 2
        }}
      >
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography
                    variant="h5"
                    sx={{
                      textAlign: { xs: 'center', sm: 'left' },
                      fontSize: { xs: '10px', sm: '16px' },
                      color: 'orange'
                    }}
                  >
                    Application On Hold
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      textAlign: { xs: 'center', sm: 'left' },
                      fontSize: { xs: '10px', sm: '16px' }
                    }}
                  >
                    Seller ID : {seller?.sellerId}
                  </Typography>
                </Box>

                <Typography
                  color="black"
                  sx={{
                    textAlign: { xs: 'center', sm: 'right' },
                    fontSize: { xs: '10px', sm: '16px' }
                  }}
                >
                  Your Verification code is: {seller?.verificationCode}
                </Typography>
              </Stack>
            </Grid>

            <Grid item xs={12} display="flex" justifyContent="center" mt={3}>
              <Image src="/assets/images/auth/AnimationLoader.gif" alt="Application on hold" height={100} width={100} />
            </Grid>

            <Grid item xs={12} display="flex" justifyContent="center" mt={2} mb={2}>
              <Typography
                color="secondary"
                textAlign="center"
                variant="body1"
                sx={{ maxWidth: '90%', fontSize: { xs: '14px', sm: '22px' } }}
              >
                Your seller account application is currently on hold for review. We will get back to you soon.
              </Typography>
            </Grid>

            {seller?.onHoldReason && (
              <Grid item xs={12}>
                <Card sx={{ backgroundColor: '#fff3cd', border: '1px solid #ffeaa7' }}>
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{
                        color: '#856404',
                        fontWeight: 'bold',
                        fontSize: { xs: '14px', sm: '18px' }
                      }}
                    >
                      Reason for Hold:
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: '#856404',
                        fontSize: { xs: '12px', sm: '16px' },
                        mt: 1
                      }}
                    >
                      {seller.onHoldReason}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}

            <Grid item xs={12}>
              <Typography
                color="secondary"
                textAlign="center"
                variant="body1"
                sx={{ maxWidth: '100%', fontSize: { xs: '14px', sm: '22px' } }}
              >
                If you have any questions, please contact us via email <NAME_EMAIL>.
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2} sx={{ width: '100%', maxWidth: 939, p: 2 }}>
        <Typography variant="h6">Step 3/5</Typography>
      </Stack>
    </Stack>
  );
};

export default OnHoldStatus;
