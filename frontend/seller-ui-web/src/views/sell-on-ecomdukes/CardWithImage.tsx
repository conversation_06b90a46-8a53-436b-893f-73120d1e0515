'use client';

import Link from 'next/link';
import { Box, Button, Container, Grid, Typography } from '@mui/material';
import Image from 'next/image';
import { SellContentSection } from 'types/sell-content';

import AnimateButton from 'components/@extended/AnimateButton';
import { convertToHtml } from 'utils/convertToHtml';

interface CardWithImageSectionProps {
  section: SellContentSection;
}

export default function CardWithImageSection({ section }: CardWithImageSectionProps) {
  const descriptionItem = section?.items?.find((item) => item.renderType === 'DESCRIPTION_ONLY');
  const imageItem = section?.items?.find((item) => item.renderType === 'IMAGE_ONLY');
  const buttonItems = section?.items?.filter((item) => item.renderType === 'BUTTON');

  const showAppIcons = descriptionItem?.metadata?.showAppIcons;
  const appIconType = descriptionItem?.metadata?.appIconType;

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        px: 2,
        py: 4
      }}
    >
      <Box
        id={section.id}
        sx={{
          width: '100%',
          maxWidth: '1500px',
          bgcolor: '#EFF1F2',
          borderRadius: 3,
          boxShadow: 3,
          p: { xs: 2, sm: 5, md: 8, lg: 5 }
        }}
      >
        <Container disableGutters>
          <Grid container spacing={4} alignItems="center">
            {/* Left side: Title and Description */}
            <Grid item xs={12} md={6}>
              <Typography variant="h4" sx={{ color: '#5b6b79', fontWeight: 'bold', mb: 3 }}>
                {section.sectionTitle}
              </Typography>

              {descriptionItem?.description && (
                <Box
                  sx={{
                    '& h1, & h2, & h3, & h4, & h5, & h6, & p': {
                      fontWeight: 400,
                      lineHeight: 1.4,
                      margin: '8px 0'
                    },
                    '& p:empty::before': {
                      content: '"\\00a0"', // non-breaking space
                      display: 'inline-block'
                    }
                  }}
                  dangerouslySetInnerHTML={{ __html: convertToHtml(descriptionItem.description) }}
                />
              )}

              {(buttonItems?.length ?? 0) > 0 && (
                <Box sx={{ mt: 4, display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {buttonItems?.map((btn) => (
                    <AnimateButton key={btn.id}>
                      <Button
                        component={Link}
                        href={btn.metadata?.redirectUrl || '#'}
                        size="large"
                        color="primary"
                        variant="contained"
                        sx={{ fontWeight: 'bold', p: '15px', width: { xs: '100%', sm: '80%', md: '60%' } }}
                      >
                        {btn.title || 'Click Here'}
                      </Button>
                    </AnimateButton>
                  ))}
                </Box>
              )}

              {/* App Icon Display Moved Here */}
              {showAppIcons && appIconType === 'IMAGE' && (
                <Box
                  sx={{
                    display: 'flex',
                    gap: 2,
                    justifyContent: { xs: 'center', sm: 'flex-start' },
                    flexWrap: 'wrap',
                    mt: 4
                  }}
                >
                  <Image src="/assets/images/landing/appstore.png" alt="App Store" width={150} height={45} />
                  <Image src="/assets/images/landing/playstoree.png" alt="Google Play" width={150} height={45} />
                </Box>
              )}

              {showAppIcons && appIconType === 'ICON' && (
                <Box sx={{ mt: 4 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      flexWrap: 'wrap',
                      gap: 2,
                      justifyContent: { xs: 'center', sm: 'flex-start' }
                    }}
                  >
                    <Typography variant="h5" sx={{ mb: 0 }}>
                      We are available on
                    </Typography>

                    <Image
                      src="/assets/images/footer/play-store.svg"
                      alt="Play Store"
                      width={40}
                      height={40}
                      style={{ objectFit: 'contain' }}
                    />
                    <Image
                      src="/assets/images/footer/apple-logo.svg"
                      alt="App Store"
                      width={40}
                      height={40}
                      style={{ objectFit: 'contain' }}
                    />
                  </Box>
                </Box>
              )}
            </Grid>

            {/* Right Side: Image + Optional App Icons */}
            <Grid item xs={12} md={6}>
              <Box sx={{ width: '100%', maxWidth: 500, mx: 'auto' }}>
                {imageItem?.previewUrl && (
                  <Image
                    src={imageItem.previewUrl}
                    alt={section.sectionTitle || 'Image'}
                    width={500}
                    height={500}
                    style={{ maxWidth: '100%', height: 'auto' }}
                  />
                )}
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
}
