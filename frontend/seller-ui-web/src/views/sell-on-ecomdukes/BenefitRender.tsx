'use client';

import { Container, Typography, Card, <PERSON><PERSON>ontent, Accordion, AccordionSummary, AccordionDetails, Grid, Box } from '@mui/material';
import { ArrowDown2 } from 'iconsax-react';
import Image from 'next/image';
import { SellContentSection } from 'types/sell-content';
import { convertToHtml } from 'utils/convertToHtml';

interface SellerBenefitsPageProps {
  section: SellContentSection;
}

const BenefitRender: React.FC<SellerBenefitsPageProps> = ({ section }) => {
  return (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 4 } }}>
      <Card sx={{ backgroundColor: 'white', borderRadius: 3, boxShadow: 'none' }}>
        <CardContent>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 'bold',
              fontSize: { xs: '1.25rem', sm: '1.5rem', md: '2rem' },
              mb: { xs: 2, md: 4 },
              color: '#00004F'
            }}
          >
            {section.sectionTitle}
          </Typography>

          {section?.items?.map((item, index) => (
            <Grid
              container
              key={index}
              spacing={4}
              alignItems="center"
              sx={{ mb: { xs: 4, md: 6 }, flexDirection: { xs: 'column', md: index % 2 === 0 ? 'row' : 'row-reverse' } }}
            >
              <Grid item xs={12} md={6}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    color: '#4A4A4A',
                    mb: 2
                  }}
                >
                  {item.title}
                </Typography>

                {item.renderType === 'ACCORDION' &&
                  item.metadata?.subtitles?.map((sub, subi) => (
                    <Accordion key={subi} sx={{ mb: 2, borderRadius: 2 }}>
                      <AccordionSummary
                        expandIcon={<ArrowDown2 />}
                        sx={{
                          color: '#4A4A4A',
                          border: '1px solid #D8D8D8',
                          borderRadius: '12px',
                          fontSize: '1rem',
                          fontWeight: 400,
                          px: 2,
                          backgroundColor: 'white'
                        }}
                      >
                        {sub.subtitle}
                      </AccordionSummary>
                      <AccordionDetails sx={{ bgcolor: '#f1f1f1', borderRadius: '12px', px: 2 }}>
                        <Box
                          sx={{
                            '& h1, & h2, & h3, & h4, & h5, & h6, & p': {
                              fontWeight: 400,
                              lineHeight: 1.4,
                              margin: '8px 0'
                            },
                            '& p:empty::before': {
                              content: '"\\00a0"', // non-breaking space
                              display: 'inline-block'
                            }
                          }}
                          dangerouslySetInnerHTML={{ __html: convertToHtml(sub.description) }}
                        />
                      </AccordionDetails>
                    </Accordion>
                  ))}

                {item.renderType === 'TITLE_DESCRIPTION' && (
                  <Box>
                    <div
                      dangerouslySetInnerHTML={{ __html: convertToHtml(item.description) }}
                      style={{ fontSize: '1rem', color: '#4A4A4A' }}
                    />
                  </Box>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ width: '100%', textAlign: 'center' }}>
                  <Image
                    src={item?.previewUrl || `/assets/images/placeholder.png`}
                    alt={item?.title ?? ''}
                    width={500}
                    height={400}
                    style={{ maxWidth: '100%', height: 'auto' }}
                  />
                </Box>
              </Grid>
            </Grid>
          ))}
        </CardContent>
      </Card>
    </Container>
  );
};

export default BenefitRender;
