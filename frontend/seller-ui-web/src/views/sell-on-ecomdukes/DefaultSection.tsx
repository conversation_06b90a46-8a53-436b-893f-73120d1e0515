'use client';

import { Box, Card, CardContent, Container, Typography } from '@mui/material';
import { SellContentSection } from 'types/sell-content';
import { convertToHtml } from 'utils/convertToHtml';

interface DefaultSectionProps {
  section: SellContentSection;
}

export default function DefaultSection({ section }: DefaultSectionProps) {
  const item = section.items?.[0];

  if (!item) return null;

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 4 } }}>
      <Card sx={{ backgroundColor: 'white', borderRadius: 3, boxShadow: 'none' }}>
        <CardContent sx={{ px: { xs: 2, sm: 4, md: 10 } }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              color: '#000050',
              mb: { xs: 3, sm: 4 },
              textAlign: 'left',
              fontSize: { xs: '1.25rem', sm: '1.5rem', md: '2rem' }
            }}
          >
            {section.sectionTitle}
          </Typography>

          <Box
            sx={{
              fontSize: '1rem',
              '& ol': {
                pl: '1.5em',
                mb: 2
              },
              '& li': {
                mb: 1
              },
              '& p:empty::before': {
                content: '"\\00a0"',
                display: 'inline-block'
              }
            }}
            dangerouslySetInnerHTML={{
              __html: convertToHtml(item.description)
            }}
          />
        </CardContent>
      </Card>
    </Container>
  );
}
