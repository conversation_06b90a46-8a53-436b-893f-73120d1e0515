'use client';

import { Box, Card, CardContent, Container, Grid, Typography } from '@mui/material';
import { SellContentSection } from 'types/sell-content';
import { convertToHtml } from 'utils/convertToHtml';

interface SellerOnboardingInfoProps {
  section: SellContentSection;
}

export default function StepsSection({ section }: SellerOnboardingInfoProps) {
  const { sectionTitle, items } = section;

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 4 } }}>
      <Card sx={{ backgroundColor: 'white', borderRadius: 3, boxShadow: 'none' }}>
        <CardContent sx={{ px: { xs: 2, sm: 4, md: 10 } }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              color: '#000050',
              mb: { xs: 3, sm: 4 },
              textAlign: 'left',
              fontSize: { xs: '1.25rem', sm: '1.5rem', md: '2rem' }
            }}
          >
            {sectionTitle}
          </Typography>

          <Grid container spacing={3}>
            {[...(items ?? [])]
              .sort((a, b) => (a?.displayOrder ?? 0) - (b?.displayOrder ?? 0))
              .map((item) => (
                <Grid item xs={12} sm={6} md={3} key={item.id}>
                  <Box
                    sx={{
                      border: '1px solid #000050',
                      borderRadius: 2,
                      p: { xs: 2, sm: 3 },
                      height: '100%',
                      mx: 'auto',
                      width: '100%',
                      maxWidth: '250px'
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 'bold',
                        mb: 2,
                        color: '#000050',
                        textAlign: 'center',
                        fontSize: { xs: '1rem', sm: '1.2rem', md: '1.5rem' }
                      }}
                    >
                      {item.title}
                    </Typography>

                    <Box
                      sx={{
                        '& h1, & h2, & h3, & h4, & h5, & h6, & p': {
                          fontWeight: 400,
                          lineHeight: 1.4,
                          margin: '8px 0'
                        },
                        '& p:empty::before': {
                          content: '"\\00a0"',
                          display: 'inline-block'
                        }
                      }}
                      dangerouslySetInnerHTML={{ __html: convertToHtml(item.description) }}
                    />
                  </Box>
                </Grid>
              ))}
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
