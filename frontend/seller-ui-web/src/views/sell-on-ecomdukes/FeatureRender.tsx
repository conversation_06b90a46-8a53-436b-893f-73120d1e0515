// sections/FeaturesSection.tsx
import { SellContentSection } from 'types/sell-content';
import { Grid, Card, CardContent, Typography, Container, Box } from '@mui/material';
import Image from 'next/image';
import { convertToHtml } from 'utils/convertToHtml';

export default function FeaturesSection({ section }: { section: SellContentSection }) {
  return (
    <Container sx={{ marginTop: '30px' }}>
      <Grid container spacing={2} justifyContent="center" textAlign="center" sx={{ mb: 5 }}>
        <Grid item xs={12}>
          <Typography variant="h2" sx={{ color: '#00004F', fontWeight: 'bold' }}>
            {section.sectionTitle}
          </Typography>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {section?.items?.map((item) => (
          <Grid item xs={12} sm={6} md={4} key={item.id}>
            <Card
              sx={{
                p: 2,
                height: '100%',
                width: '100%',
                borderRadius: 2,
                boxShadow: '0px 4px 10px rgba(0,0,0,0.1)',
                textAlign: 'left',
                transition: 'all 0.3s ease',
                '&:hover': { boxShadow: '0px 6px 15px rgba(0,0,0,0.15)' }
              }}
            >
              <CardContent>
                {item.imageUrl && (
                  <Image
                    src={item.previewUrl || `/assets/images/placeholder.png`}
                    alt={item.title ?? ''}
                    width={40}
                    height={40}
                    style={{ marginBottom: 16 }}
                  />
                )}
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1, color: '#00004F' }}>
                  {item.title}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  <Box
                    sx={{
                      '& h1, & h2, & h3, & h4, & h5, & h6, & p': {
                        fontWeight: 400,
                        lineHeight: 1.4,
                        margin: '8px 0'
                      },
                      '& p:empty::before': {
                        content: '"\\00a0"', // non-breaking space
                        display: 'inline-block'
                      }
                    }}
                    dangerouslySetInnerHTML={{ __html: convertToHtml(item.description) }}
                  />
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
