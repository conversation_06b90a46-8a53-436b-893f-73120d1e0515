export enum OrderItemStatus {
  New = 'new',
  Pending = 'pending',
  Accepted = 'accepted',
  Processing = 'processing',
  Dispatched = 'dispatched',
  Rejected = 'rejected',
  ReturnRefund = 'return_refund',
  Delivered = 'delivered',
  RefundCompleted = 'refund_completed',
  Cancelled = 'cancelled',
  Paid = 'paid',
  ReadyToDispatch = 'ready_to_dispatch',
  PaymentFailed = 'payment_failed'
}
export const StatusLabelMap: Record<string, OrderItemStatus[]> = {
  New: [OrderItemStatus.New],
  Accepted: [OrderItemStatus.Accepted],
  Processing: [OrderItemStatus.Processing],
  Dispatched: [OrderItemStatus.Dispatched],
  Rejected: [OrderItemStatus.Rejected],
  ReturnRefund: [OrderItemStatus.ReturnRefund],
  Delivered: [OrderItemStatus.Delivered],
  RefundCompleted: [OrderItemStatus.RefundCompleted]
};
