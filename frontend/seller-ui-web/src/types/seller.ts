import { SampleProduct } from './auth';

export interface Seller {
  id: string;
  sellerId: string;
  userTenantId: string;
  createdBy: string;
  createdOn: string;
  modifiedBy: string;
  modifiedOn: string;
  deleted: boolean;
  deletedBy: string | null;
  deletedOn: string | null;
  emailVerified: boolean;
  phoneVerified: boolean;
  verificationCode: string;
  status: SellerStatus;
  rejectionReason: string;
  onHoldReason: string;
  sampleProductImages: SampleProduct[];
}

export interface ProductImage {
  name: string;
  thumbnails: File[];
}
export interface IsellerOnboardForm {
  website: string;
  fbId: string;
  instaId: string;
  sampleProductImages: ProductImage[];
}
export enum SellerStatus {
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  ON_HOLD = 'ON_HOLD',
  INACTIVE = 'INACTIVE'
}

export interface CashFreeVendor {
  email?: string;
  status?: string;
  phone?: string;
  name?: string;
  vendorId?: string;
  addedOn?: string;
  updatedOn?: string;
  bank?: BankDetails;
  upi?: string;
  scheduleOption?: ScheduleOption[];
  vendorType?: string;
  accountType?: string;
  businessType?: string;
  remarks?: string;
  relatedDocs?: VendorEntityRelatedDoc[];
}

export interface BankDetails {
  accountNumber?: string;
  accountHolder?: string;
  ifsc?: string;
}

export interface ScheduleOption {
  settlementScheduleMessage?: string;
  scheduleId?: number;
  merchantDefault?: boolean;
}

export interface VendorEntityRelatedDoc {
  vendorId?: string;
  docType?: string;
  docValue?: string;
  status?: string;
  remarks?: string;
  docName?: string;
}
