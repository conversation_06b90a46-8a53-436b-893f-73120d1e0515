import { useRef, useState, ReactNode } from 'react';

// next
import { useRouter } from 'next/navigation';

// material-ui
import { useTheme } from '@mui/material/styles';
import ButtonBase from '@mui/material/ButtonBase';
import CardContent from '@mui/material/CardContent';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Image from 'next/image';
// project-imports
import ProfileTab from './ProfileTab';
import Avatar from 'components/@extended/Avatar';
import MainCard from 'components/MainCard';
import Transitions from 'components/@extended/Transitions';
import { ThemeMode } from 'config';
import { useAppDispatch } from '../../../../../redux/hooks';
import { unsetCredentials } from '../../../../../redux/auth/authSlice';
import { useGetUserQuery } from '../../../../../redux/auth/authApiSlice';
import { apiSlice } from 'redux/apiSlice';
import { ApiTagTypes } from 'redux/types';

interface TabPanelProps {
  children?: ReactNode;
  dir?: string;
  index: number;
  value: number;
}

// tab panel wrapper
function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
      sx={{ p: 1 }}
    >
      {value === index && children}
    </Box>
  );
}

// ==============================|| HEADER CONTENT - PROFILE ||============================== //

export default function ProfilePage() {
  const theme = useTheme();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { data: user } = useGetUserQuery();

  const handleLogout = async () => {
    dispatch(unsetCredentials());
    dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
    router.push('/login');
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const anchorRef = useRef<any>(null);
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const [value] = useState(0);

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75, display: 'flex', alignItems: 'center', gap: 1 }}>
      <ButtonBase
        onClick={() => router.push('/chat')}
        sx={{
          width: 40,
          height: 40,
          p: 0.25,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '&:hover': {
            bgcolor: theme.palette.mode === ThemeMode.DARK ? 'secondary.light' : 'secondary.lighter'
          },
          '&:focus-visible': {
            outline: `2px solid ${theme.palette.secondary.dark}`,
            outlineOffset: 2
          }
        }}
        aria-label="open messages"
      >
        <Image src="/assets/images/Message_WhiteBG.svg" alt="Message Icon" width={30} height={30} />
      </ButtonBase>

      <ButtonBase
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          p: 0.25,
          borderRadius: 1,
          '&:hover': {
            bgcolor: theme.palette.mode === ThemeMode.DARK ? 'secondary.light' : 'secondary.lighter'
          },
          '&:focus-visible': {
            outline: `2px solid ${theme.palette.secondary.dark}`,
            outlineOffset: 2
          }
        }}
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
      >
        <Typography variant="subtitle1" color="textPrimary">
          {`${user?.firstName} ${user?.lastName}`}
        </Typography>
        <Avatar alt="profile user" src={user?.photoUrl || undefined}>
          {!user?.photoUrl && user?.firstName?.charAt(0)?.toUpperCase()}
        </Avatar>{' '}
      </ButtonBase>

      <Popper
        placement="bottom-end"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 9]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Transitions type="grow" position="top-right" in={open} {...TransitionProps}>
            <ClickAwayListener onClickAway={handleClose}>
              <Box>
                <Paper
                  sx={{
                    boxShadow: theme.customShadows.z1,
                    width: 290,
                    minWidth: 240,
                    maxWidth: 290,
                    [theme.breakpoints.down('md')]: {
                      maxWidth: 250
                    },
                    borderRadius: 1.5
                  }}
                >
                  <MainCard border={false} content={false}>
                    <CardContent sx={{ px: 2.5, pt: 3 }}>
                      <Grid container justifyContent="space-between" alignItems="center">
                        <Grid item>
                          <Stack direction="row" spacing={1.25} alignItems="center">
                            <Avatar alt="profile user" src={user?.photoUrl || undefined}>
                              {!user?.photoUrl && user?.firstName?.charAt(0)?.toUpperCase()}
                            </Avatar>{' '}
                            <Stack>
                              <Typography variant="subtitle1">Hello</Typography>

                              <Typography variant="h4" color="secondary">
                                {`${user?.firstName} ${user?.lastName}`}
                              </Typography>
                            </Stack>
                          </Stack>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </MainCard>
                </Paper>
                <Paper
                  sx={{
                    boxShadow: theme.customShadows.z1,
                    width: 290,
                    minWidth: 240,
                    maxWidth: 290,
                    [theme.breakpoints.down('md')]: {
                      maxWidth: 250,
                      mt: 1
                    },
                    [theme.breakpoints.up('md')]: {
                      mt: 2
                    },
                    borderRadius: 1.5,
                    maxHeight: '60vh',
                    overflowY: 'auto'
                  }}
                >
                  <MainCard border={false} content={false}>
                    <CardContent>
                      <TabPanel value={value} index={0} dir={theme.direction}>
                        <ProfileTab handleLogout={handleLogout} handleClose={(event) => handleClose(event as MouseEvent | TouchEvent)} />
                      </TabPanel>
                    </CardContent>
                  </MainCard>
                </Paper>
              </Box>
            </ClickAwayListener>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
}
