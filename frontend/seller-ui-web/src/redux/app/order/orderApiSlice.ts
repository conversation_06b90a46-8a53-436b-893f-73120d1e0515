import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from 'redux/apiSlice';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { OrderLineItem } from 'types/order';
import { OrderItemStatus } from 'enums/orderStatus';

export const orderApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getOrder: builder.query<OrderLineItem[], { filter?: IFilter }>({
      query: ({ filter }) => ({
        url: '/order-line-items',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter)
        }
      })
    }),
    getOrderDetailsById: builder.query<OrderLineItem, { id: string; filter: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/order-line-items/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter)
        }
      })
    }),
    getOrderCount: builder.query<{ count: number }, void>({
      query: () => ({
        url: '/order-line-items/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateOrderItemStatus: builder.mutation<void, { orderItemId: string; newStatus: OrderItemStatus; rejectionReason?: string }>({
      query: ({ orderItemId, newStatus, rejectionReason }) => ({
        url: `/order-line-items/${orderItemId}/status/${newStatus}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateOrder: builder.mutation<void, { id: string; rejectionReason: string; data: Partial<OrderLineItem> }>({
      query: ({ id, data, rejectionReason }) => ({
        url: `/order-line-items/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: { rejectionReason, ...data }
      })
    }),
    downloadInvoicePdf: builder.query<Blob, string>({
      query: (orderLineItemId) => ({
        url: `/invoices/${orderLineItemId}/download`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        responseHandler: (response: Response) => response.blob()
      })
    })
  })
});

export const {
  useGetOrderQuery,
  useLazyGetOrderQuery,
  useGetOrderDetailsByIdQuery,
  useGetOrderCountQuery,
  useUpdateOrderMutation,
  useUpdateOrderItemStatusMutation,
  useDownloadInvoicePdfQuery,
  useLazyDownloadInvoicePdfQuery
} = orderApiSlice;
