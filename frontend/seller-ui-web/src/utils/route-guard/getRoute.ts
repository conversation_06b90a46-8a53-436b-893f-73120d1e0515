import { APP_DEFAULT_PATH, APP_PROGRESS_PATH } from 'config';
import { SellerStatus } from 'types/seller';
import { User } from 'types/user-profile';

export enum OnboardPath {
  EMAIL_VERIFICATION = '/onboard/email-code-verification',
  SMS_VERIFICATION = '/onboard/sms-code-verification',
  STORE_CREATION = '/onboard/store-creation',
  PRE_VERIFY = '/onboard/pre-verify',
  POST_VERIFY = '/onboard/post-verify',
  ON_HOLD = '/onboard/on-hold'
}

export const getRoute = (profile: User, currentPath: string, isFromGuestGuard = false): string => {
  const ALLOWED_PATHS = ['/faq'];

  if (ALLOWED_PATHS.includes(currentPath)) {
    return currentPath;
  }

  const steps: [boolean, OnboardPath | string][] = [
    [!profile.emailVerified, OnboardPath.EMAIL_VERIFICATION],
    [profile.emailVerified && !profile.phoneVerified, OnboardPath.SMS_VERIFICATION],
    [profile.emailVerified && profile.phoneVerified && !profile.onBoardComplete, OnboardPath.STORE_CREATION],
    [
      profile.emailVerified && profile.phoneVerified && profile.onBoardComplete && profile.profileStatus === SellerStatus.PENDING,
      OnboardPath.PRE_VERIFY
    ],
    [
      profile.emailVerified && profile.phoneVerified && profile.onBoardComplete && profile.profileStatus === SellerStatus.ON_HOLD,
      OnboardPath.ON_HOLD
    ],
    [
      profile.emailVerified &&
        profile.phoneVerified &&
        profile.onBoardComplete &&
        profile.profileStatus === SellerStatus.APPROVED &&
        currentPath === OnboardPath.PRE_VERIFY,

      OnboardPath.POST_VERIFY
    ],
    [
      profile.emailVerified &&
        profile.phoneVerified &&
        profile.onBoardComplete &&
        profile.profileStatus === SellerStatus.REJECTED &&
        currentPath === OnboardPath.PRE_VERIFY,

      OnboardPath.STORE_CREATION
    ],
    [
      profile.emailVerified &&
        profile.phoneVerified &&
        profile.onBoardComplete &&
        profile.profileStatus === SellerStatus.APPROVED &&
        currentPath === APP_PROGRESS_PATH,

      APP_DEFAULT_PATH
    ]
  ];

  const nextStep = steps.find(([condition, path]) => condition && currentPath !== path);

  if (nextStep) return nextStep[1];

  return isFromGuestGuard ? APP_DEFAULT_PATH : currentPath;
};
