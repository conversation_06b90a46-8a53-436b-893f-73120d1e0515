import { ISellerStore } from './auth';
import { User } from './user-profile';

export type UserTenant = {
  id: number;
  roleId: string;
  status: number;
  tenantId: string;
  user: Partial<User>;
  userId: number;
  createdOn: string;
};

export interface SellerList {
  id: string;
  sellerId: string;
  userTenant: Partial<UserTenant>;
  status: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  verificationCode: string;
  rejectionReason: string;
  onHoldReason: string;
  sellerStore?: ISellerStore;
  preSignedPhotoUrl?: string | null;
}

export interface Permission {
  id: string;
  userTenantId: string;
  permission: string;
  allowed: boolean;
}
export interface SellerFormValues {
  adminId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  designation?: string;
  dob?: string | null;
  gender: string;
  photoUrl?: string;
  permissions: Permission[];
  status?: string | number;
  preSignedPhotoUrl?: string;
}
