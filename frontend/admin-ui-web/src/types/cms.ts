export enum SectionType {
  CAROUSEL = 'carousel',
  FEATURED_PRODUCTS = 'featured_products',
  BANNER = 'banner',
  TEXT_BLOCK = 'text_block',
  FEATURED_COLLECTION = 'featured-collection',
  RECENTLY_VIEWED = 'recently-viewed',
  MOST_VIEWED = 'most-viewed',
  TOP_SELLING = 'top-selling',
  PRODUCT_FILTER = 'product-filter',
  ALL_CATEGORIES = 'all-categories',
  FACETS = 'facets',
  GIFT_PRODUCTS = 'gift-products',
  SIMILAR_PRODUCTS = 'similar-products'
}

export enum SectionItemType {
  PRODUCT = 'product',
  CATEGORY = 'category',
  IMAGE = 'image',
  CUSTOM_CARD = 'custom_card',
  COLLECTION = 'collection',
  TEXT = 'text',
  FACET_VALUES = 'facet-values'
}

export enum CardStyle {
  BASIC = 'basic',
  IMAGE_ONLY = 'image-only',
  IMAGE_TITLE = 'image-title',
  IMAGE_TITLE_SUBTITLE = 'image-title-subtitle',
  OFFER_CARD = 'offer-card',
  OFFER_CARD_INVERSE = 'offer-card-inverse',
  PRODUCT_CARD = 'product-card',
  AVATAR_WITH_TITLE = 'avatar-with-title'
}

export enum PageType {
  HOME = 'home',
  SELL_ON_ECOMDUKES = 'sell-on-ecomdukes',
  ABOUT_US = 'about-us',
  CONTACT_US = 'contact-us'
}

export interface PageSectionDto extends PageSection {
  items?: SectionItem[];
}

export interface BulkSection {
  sections: PageSectionDto[];
}

export interface PageSectionMetadata {
  showTitle?: boolean;
}
export interface PageSection {
  id: string;
  type: SectionType;
  title: string;
  metadata?: PageSectionMetadata;
  cardStyle?: CardStyle;
  displayOrder: number;
  isActive: boolean;
  sectionItems?: SectionItem[];
  pageType: string;
}
export interface CarouselMetadata {
  redirectUrl?: string;
}

export interface FeaturedProductsMetadata {
  productIds?: string[];
}

export interface OccasionCardsMetadata {
  maxItems?: number;
  viewAllLink?: string;
}

export interface SectionItem {
  id: string;
  entityType: SectionItemType;
  entityId?: string;
  imageUrl?: string;
  previewUrl?: string;
  title: string;
  subtitle?: string;
  content?: string;
  metadata?: SectionItemMetadata;
  displayOrder: number;
}

export interface BannerMetadata {
  redirectUrl?: string;
}

export interface CategoryGridMetadata {
  categoryId?: string;
}

export interface SectionItemMetadata {
  redirectUrl?: string;
  productIds?: string[];
  maxItems?: number;
  viewAllLink?: string;
  categoryId?: string;
  showRating?: boolean;
  showPrice?: boolean;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  richText?: string;
  filter?: object;
  facetValueIds?: string[];
}
