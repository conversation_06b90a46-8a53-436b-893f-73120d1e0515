import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import Stack from '@mui/material/Stack';
import ListItem from '@mui/material/ListItem';
import Typography from '@mui/material/Typography';
import MainCard from '../../../components/MainCard';
import Transitions from '../../../components/@extended/Transitions';
import { CircularProgress, Button, Dialog, DialogContent, TextField, DialogActions } from '@mui/material';
import { useLazyGetSellerByIdQuery, useUpdateSellerStatusMutation } from '../../../redux/app/seller/sellerApiSlice';
import React, { useEffect } from 'react';
import moment from 'moment-timezone';
import { DEFAULT_DATE_FORMAT } from 'config';
import { SellerStatus } from '../../../enums/seller.enum';
import { SnackbarProps } from 'types/snackbar';
import { openSnackbar } from 'api/snackbar';
import { useRouter } from 'next/navigation';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';

export default function SellerView({ data: viewData }: any) {
  const theme = useTheme();
  const matchDownMD = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();
  const handleError = useApiErrorHandler();
  const [open, setOpen] = React.useState(false);
  const [onHoldOpen, setOnHoldOpen] = React.useState(false);

  const handleClose = () => {
    setOpen(false);
  };

  const handleOnHoldClose = () => {
    setOnHoldOpen(false);
  };

  const [fetchSeller, { data: sellerData, isLoading: sellerDataLoading }] = useLazyGetSellerByIdQuery();
  const {
    emailVerified = false,
    phoneVerified = false,
    id: sellerId,
    verificationCode = '',
    rejectionReason = '',
    onHoldReason = '',
    status = ''
  } = sellerData || {};
  const { createdOn = '' } = sellerData?.userTenant || {};
  const { firstName = '', lastName = '', email = '', dob = '', username = '', gender = '' } = sellerData?.userTenant?.user || {};
  const { fbId = '', instaId = '' } = sellerData?.sellerStore || {};

  const [updateSellerStatus, { error: updateError, reset: updateReset }] = useUpdateSellerStatusMutation();

  const handleUpdateStatus = async (status: SellerStatus, rejectionReason?: string, onHoldReason?: string) => {
    await updateSellerStatus({
      id: sellerId || '',
      status: status,
      verificationCode,
      rejectionReason: rejectionReason || '',
      onHoldReason: onHoldReason || ''
    }).unwrap();
    openSnackbar({
      open: true,
      message: 'Seller status updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push('/seller');
  };

  useEffect(() => {
    if (viewData?.id) {
      fetchSeller({
        id: viewData.id,
        include: [
          {
            relation: 'userTenant',
            scope: {
              include: [{ relation: 'user' }]
            }
          },
          {
            relation: 'sellerStore'
          }
        ]
      });
    }
  }, [viewData, fetchSeller]);

  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateError]);

  return sellerDataLoading ? (
    <CircularProgress color="inherit" />
  ) : (
    <React.Fragment>
      <Transitions type="slide" direction="down" in={true}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Stack spacing={2.5}>
              <MainCard title="Personal">
                <List sx={{ py: 0 }}>
                  <ListItem divider={!matchDownMD}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Name</Typography>
                          <Typography>{`${firstName} ${lastName}`.trim()}</Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Email</Typography>
                          <Stack spacing={0.5} direction="row" alignItems="center">
                            <Typography>{email}</Typography>
                          </Stack>
                        </Stack>
                      </Grid>
                    </Grid>
                  </ListItem>
                  <ListItem divider={!matchDownMD}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">DOB</Typography>
                          <Stack spacing={0.5} direction="row" alignItems="center">
                            <Typography>{dob || 'Not Provided'}</Typography>
                          </Stack>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">UserName</Typography>
                          <Typography>{username}</Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </ListItem>
                  <ListItem divider={!matchDownMD}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Gender</Typography>
                          <Typography>{gender || 'Not Provided'}</Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </ListItem>
                </List>
              </MainCard>
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <Stack spacing={2.5}>
              <MainCard title="General">
                <List sx={{ py: 0 }}>
                  {/* Added Social Media Fields */}
                  <ListItem divider={!matchDownMD}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Facebook ID</Typography>
                          <Typography>{fbId || 'Not Provided'}</Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Instagram ID</Typography>
                          <Typography>{instaId || 'Not Provided'}</Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </ListItem>
                  <ListItem divider={!matchDownMD}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Created On</Typography>
                          <Typography>{moment(createdOn ?? '').format(DEFAULT_DATE_FORMAT)}</Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Status</Typography>
                          <Stack spacing={0.5} direction="row" alignItems="center">
                            <Typography>{status}</Typography>
                          </Stack>
                        </Stack>
                      </Grid>
                    </Grid>
                  </ListItem>
                  <ListItem divider={!matchDownMD}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Email Verified</Typography>
                          <Typography>{emailVerified ? 'Yes' : 'No'}</Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Stack spacing={0.5}>
                          <Typography color="secondary">Phone Verified</Typography>
                          <Stack spacing={0.5} direction="row" alignItems="center">
                            <Typography>{phoneVerified ? 'Yes' : 'No'}</Typography>
                          </Stack>
                        </Stack>
                      </Grid>
                    </Grid>
                  </ListItem>
                  {status === SellerStatus.PENDING && (
                    <ListItem>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Stack spacing={0.5}>
                            <Typography color="secondary">Seller Approval</Typography>
                            <Typography>
                              The seller&apos;s registration is pending. Please approve, put on hold, or reject the request.
                            </Typography>
                          </Stack>
                        </Grid>
                        <Grid item xs={12} md={6} sx={{ display: 'flex', alignItems: 'center' }}>
                          <Stack spacing={2}>
                            <Stack spacing={1} direction="row" alignItems="center">
                              <Button
                                variant="contained"
                                color="primary"
                                onClick={() => handleUpdateStatus(SellerStatus.APPROVED)}
                                size="small"
                              >
                                Approve
                              </Button>
                              <Button variant="contained" onClick={() => setOnHoldOpen(true)} color="warning" size="small">
                                Put on Hold
                              </Button>
                              <Button variant="contained" onClick={() => setOpen(true)} color="error" size="small">
                                Decline
                              </Button>
                            </Stack>
                          </Stack>
                        </Grid>
                      </Grid>
                    </ListItem>
                  )}
                  {status === SellerStatus.ON_HOLD && (
                    <ListItem>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Stack spacing={0.5}>
                            <Typography color="secondary">On Hold Status</Typography>
                            <Typography>This seller is currently on hold. You can approve or reject the request.</Typography>
                            <Typography color="secondary" sx={{ mt: 1 }}>
                              Reason for Hold:
                            </Typography>
                            <Typography>{onHoldReason}</Typography>
                          </Stack>
                        </Grid>
                        <Grid item xs={12} md={6} sx={{ display: 'flex', alignItems: 'center' }}>
                          <Stack spacing={2}>
                            <Stack spacing={1} direction="row" alignItems="center">
                              <Button
                                variant="contained"
                                color="primary"
                                onClick={() => handleUpdateStatus(SellerStatus.APPROVED)}
                                size="small"
                              >
                                Approve
                              </Button>
                              <Button variant="contained" onClick={() => setOpen(true)} color="error" size="small">
                                Reject
                              </Button>
                            </Stack>
                          </Stack>
                        </Grid>
                      </Grid>
                    </ListItem>
                  )}
                  {status === SellerStatus.REJECTED && (
                    <ListItem>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Stack spacing={0.5}>
                            <Typography color="secondary">Reason for Reject</Typography>
                            <Typography>{rejectionReason}</Typography>
                          </Stack>
                        </Grid>
                      </Grid>
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Stack>
          </Grid>
        </Grid>
      </Transitions>
      <Dialog open={open} onClose={handleClose}>
        <form
          onSubmit={(event: React.FormEvent<HTMLFormElement>) => {
            event.preventDefault();
            const formData = new FormData(event.currentTarget);
            const formJson = Object.fromEntries(formData.entries());
            const rejectionReason = formJson.reason as string;

            handleUpdateStatus(SellerStatus.REJECTED, rejectionReason);
            handleClose();
          }}
        >
          <DialogContent>
            <Typography variant="h5">Reject Seller</Typography>
            <Typography variant="body2">Please provide a reason for rejecting this seller.</Typography>
            <TextField
              autoFocus
              required
              margin="dense"
              id="reason"
              name="reason"
              label="Rejection Reason"
              type="text"
              fullWidth
              variant="standard"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} color="primary">
              Cancel
            </Button>
            <Button type="submit" color="error">
              Reject
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <Dialog open={onHoldOpen} onClose={handleOnHoldClose}>
        <form
          onSubmit={(event: React.FormEvent<HTMLFormElement>) => {
            event.preventDefault();
            const formData = new FormData(event.currentTarget);
            const formJson = Object.fromEntries(formData.entries());
            const onHoldReason = formJson.reason as string;

            handleUpdateStatus(SellerStatus.ON_HOLD, undefined, onHoldReason);
            handleOnHoldClose();
          }}
        >
          <DialogContent>
            <Typography variant="h5">Put Seller on Hold</Typography>
            <Typography variant="body2">Please provide a reason for putting this seller on hold.</Typography>
            <TextField
              autoFocus
              required
              margin="dense"
              id="reason"
              name="reason"
              label="On Hold Reason"
              type="text"
              fullWidth
              variant="standard"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleOnHoldClose} color="primary">
              Cancel
            </Button>
            <Button type="submit" color="warning">
              Put on Hold
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </React.Fragment>
  );
}
