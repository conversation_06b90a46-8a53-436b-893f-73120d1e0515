import { <PERSON><PERSON>, TextField, Button, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react';
import { CategoryGridMetadata, SectionItem } from 'types/cms';
import { categoryGridItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 } from 'iconsax-react';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const CategoryGridItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);

  // Create a mock file object for existing images
  const createMockFileFromPreview = (previewUrl: string) => {
    return {
      preview: previewUrl,
      name: 'existing-image',
      size: 0,
      type: 'image/*',
      lastModified: Date.now(),
      webkitRelativePath: '',
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
      bytes: () => Promise.resolve(new Uint8Array(0)),
      slice: () => new Blob(),
      stream: () => new ReadableStream(),
      text: () => Promise.resolve('')
    } as File;
  };

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  useEffect(() => {
    // If there's a previewUrl but no file in fileMap, create a mock file
    if (data.previewUrl && !fileMap[data.id ?? '']) {
      const updatedFileMap = { ...fileMap };
      updatedFileMap[data.id ?? ''] = createMockFileFromPreview(data.previewUrl);
      setFileMap(updatedFileMap);
    }
  }, [data.previewUrl, data.id, fileMap, setFileMap]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: categoryGridItemSchema,
    onSubmit: (values) => {
      onChange(values);
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SingleFileUpload
            setFieldValue={(_: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={fileMap[data.id ?? '']}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Category Name"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="metadata.categoryId"
            label="Category ID"
            value={(formik.values.metadata as CategoryGridMetadata)?.categoryId || ''}
            onChange={(e) => {
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                categoryId: e.target.value
              });
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button variant="contained" color="primary" startIcon={<Save2 />} onClick={handleSave}>
              Save
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
