import { <PERSON>rid, TextField, Button, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react';
import { BannerMetadata, SectionItem } from 'types/cms';
import { bannerItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 } from 'iconsax-react';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const BannerItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);
  const [isSaved, setIsSaved] = useState(true);
  const [localFileMap, setLocalFileMap] = useState<Record<string, File>>({ ...fileMap });

  // Create a mock file object for existing images
  const createMockFileFromPreview = (previewUrl: string) => {
    return {
      preview: previewUrl,
      name: 'existing-image',
      size: 0,
      type: 'image/*',
      lastModified: Date.now(),
      webkitRelativePath: '',
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
      bytes: () => Promise.resolve(new Uint8Array(0)),
      slice: () => new Blob(),
      stream: () => new ReadableStream(),
      text: () => Promise.resolve('')
    } as File;
  };

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  useEffect(() => {
    const updatedFileMap = { ...fileMap };

    // If there's a previewUrl but no file in fileMap, create a mock file
    if (data.previewUrl && !fileMap[data.id ?? '']) {
      updatedFileMap[data.id ?? ''] = createMockFileFromPreview(data.previewUrl);
    }

    setLocalFileMap(updatedFileMap);
  }, [fileMap, data.previewUrl, data.id]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: bannerItemSchema,
    onSubmit: (values) => {
      onChange(values);
      setIsSaved(true);
      setFileMap({ ...localFileMap });
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
    setIsSaved(false);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SingleFileUpload
            setFieldValue={(_: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setLocalFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={localFileMap[data.id ?? '']}
          />
          {formik.errors.imageUrl && <Box sx={{ color: 'error.main', mt: 1, fontSize: '0.75rem' }}>{formik.errors.imageUrl as string}</Box>}
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Title"
            placeholder="Enter banner title"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
            error={Boolean(formik.errors.title)}
            helperText={formik.errors.title}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle"
            placeholder="Enter banner subtitle"
            value={formik.values.subtitle || ''}
            onChange={(e) => {
              handleLocalChange('subtitle', e.target.value);
            }}
            error={Boolean(formik.errors.subtitle)}
            helperText={formik.errors.subtitle}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="metadata.redirectUrl"
            label="Redirection URL"
            placeholder="Enter the redirection URL"
            value={(formik.values.metadata as BannerMetadata)?.redirectUrl || ''}
            onChange={(e) => {
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                redirectUrl: e.target.value
              });
            }}
            error={Boolean((formik.errors.metadata as BannerMetadata)?.redirectUrl)}
            helperText={
              (formik.errors.metadata as BannerMetadata)?.redirectUrl ? (formik.errors.metadata as BannerMetadata).redirectUrl : ''
            }
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<Save2 />}
              onClick={handleSave}
              disabled={isSaved || !formik.dirty || !formik.isValid}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
