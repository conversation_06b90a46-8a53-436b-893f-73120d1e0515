'use client';

import { useMemo, useState, MouseEvent, useCallback } from 'react';
import { useRouter } from 'next/navigation';

// material-ui
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';

// third-party
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';

import IconButton from '../../components/@extended/IconButton';

import { DEFAULT_DATE_FORMAT, ThemeMode } from 'config';

// assets
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import moment from 'moment-timezone';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import { SellerTable } from './SellerTable';
import AlertSellerDelete from 'sections/apps/seller/AlertSellerDelete';
import { useGetSellersCountQuery, useGetSellersQuery } from '../../redux/app/seller/sellerApiSlice';
import IndeterminateCheckbox from '../../components/third-party/react-table/IndeterminateCheckbox';
import { SellerList } from 'types/seller';
import { PermissionKeys } from 'enums/permission-keys.enum';
import withPermission from 'hoc/withPermission';
import { useAuth } from 'contexts/AuthContext';
import { Avatar } from '@mui/material';

const SellerListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [filters, setFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [sellerDeleteId, setsellerDeleteId] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10
  });

  const canEdit = hasPermission(PermissionKeys.UpdateSeller);
  const canDelete = hasPermission(PermissionKeys.DeleteSeller);

  const {
    data: sellerlist,
    isLoading: sellerListLoading,
    refetch
  } = useGetSellersQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(filters, '', ['sellerId']),
    ...convertPaginationToLoopback(pagination),
    include: [
      {
        relation: 'userTenant',
        required: true,
        scope: {
          include: [
            {
              relation: 'user',
              scope: globalFilter
                ? {
                    where: {
                      or: [
                        {
                          firstName: { ilike: `%${globalFilter}%` }
                        },
                        { lastName: { ilike: `%${globalFilter}%` } },
                        { email: { ilike: `%${globalFilter}%` } }
                      ]
                    }
                  }
                : undefined,
              required: true
            }
          ]
        }
      }
    ]
  });

  const { data: sellerCount } = useGetSellersCountQuery({
    where: convertFiltersToLoopbackWhere(filters, globalFilter, ['name', 'sellerId']),
    ...convertPaginationToLoopback(pagination),
    include: [
      {
        relation: 'userTenant',
        required: true,
        scope: {
          include: [
            {
              relation: 'user',
              scope: globalFilter
                ? {
                    where: {
                      or: [
                        {
                          firstName: { ilike: `%${globalFilter}%` }
                        },
                        { lastName: { ilike: `%${globalFilter}%` } },
                        { email: { ilike: `%${globalFilter}%` } }
                      ]
                    }
                  }
                : undefined,
              required: true
            }
          ]
        }
      }
    ]
  });

  const handleClose = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const columns = useMemo<ColumnDef<SellerList>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      {
        header: 'Seller ID',
        accessorKey: 'sellerId',
        enableSorting: true,
        enableColumnFilter: true,
        cell: ({ row }) => {
          const sellerData = row.original;
          const imageUrl = sellerData?.preSignedPhotoUrl;
          const sellerId = sellerData?.sellerId;
          const firstName = sellerData?.userTenant?.user?.firstName || '';
          const lastName = sellerData?.userTenant?.user?.lastName || '';

          const initials = firstName || lastName ? `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase() : 'U';

          return (
            <div style={{ display: 'flex', alignItems: 'center', width: 150 }}>
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt="Seller"
                  style={{
                    width: 40,
                    height: 40,
                    objectFit: 'cover',
                    borderRadius: 4,
                    marginRight: 12
                  }}
                />
              ) : (
                <Avatar
                  alt="Seller Avatar"
                  sx={{
                    width: 40,
                    height: 40,
                    marginRight: 1,
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    fontSize: '0.875rem'
                  }}
                >
                  {initials}
                </Avatar>
              )}
              <span>{sellerId}</span>
            </div>
          );
        }
      },
      {
        header: 'Name',
        accessorKey: 'clientName',
        enableSorting: true,
        enableColumnFilter: false,
        cell: ({ row }) => {
          const { firstName = '', lastName = '' } = row?.original?.userTenant?.user || {};
          return <Typography>{`${firstName} ${lastName}`.trim()}</Typography>;
        }
      },
      {
        header: 'Email',
        accessorKey: 'contactEmail',
        enableSorting: true,
        enableColumnFilter: false,
        cell: ({ row }) => {
          return <Typography>{row?.original?.userTenant?.user?.email}</Typography>;
        }
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        enableColumnFilter: false,
        meta: {
          className: 'cell-right'
        },
        cell(cell) {
          return moment(cell.getValue() ?? '').format(DEFAULT_DATE_FORMAT);
        }
      },
      {
        header: 'Status',
        accessorKey: 'status',
        enableColumnFilter: false,
        cell: ({ row }) => {
          const status = row?.original?.status;
          const getStatusColor = (status: string) => {
            switch (status) {
              case 'APPROVED':
                return 'success';
              case 'REJECTED':
                return 'error';
              case 'ON_HOLD':
                return 'warning';
              case 'PENDING':
                return 'info';
              default:
                return 'default';
            }
          };

          return <Chip label={status} color={getStatusColor(status) as any} size="small" variant="outlined" />;
        }
      },
      {
        header: 'Actions',
        meta: {
          className: 'cell-center'
        },
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  color="primary"
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/seller/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setsellerDeleteId(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [theme]
  );

  return (
    <>
      <SellerTable
        {...{
          data: sellerlist || [],
          columns,
          setSorting,
          sorting,
          filters,
          setFilters,
          loading: sellerListLoading,
          globalFilter,
          setGlobalFilter,
          pagination,
          setPagination,
          totalRows: (sellerCount?.count as any) ?? 0,
          refetch
        }}
      />
      <AlertSellerDelete id={sellerDeleteId} title={sellerDeleteId} open={open} handleClose={handleClose} refetch={refetch} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewSeller)(SellerListPage);
