'use client';

import React, { useEffect, useMemo } from 'react';
import { Card, CardContent, Typography, Box, Grid, Chip, Switch } from '@mui/material';
import { useGetShippingRuleByIdQuery } from 'redux/app/shipping-rule/shippingRuleApiSlice';
import { useGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { ShippingRuleConditionType, ShippingRulePriority } from 'enums/shipping-rule.enum';
import { Collection } from 'types/collection';
import Loader from 'components/Loader';

function ViewShippingRule({ shippingRuleId }: { shippingRuleId: string }) {
  const { data, isLoading, error, refetch } = useGetShippingRuleByIdQuery({ id: shippingRuleId });

  // Fetch collections for displaying collection names
  const { data: collections = [] } = useGetCollectionsQuery({
    where: { status: 'active' }
  });

  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Helper functions for formatting display values
  const formatConditionType = useMemo(() => {
    const typeMap: Record<string, string> = {
      [ShippingRuleConditionType.PAYMENT_MODE]: 'Payment Mode',
      [ShippingRuleConditionType.WEIGHT]: 'Weight',
      [ShippingRuleConditionType.ORDER_VALUE]: 'Order Value',
      [ShippingRuleConditionType.ORDER_QTY]: 'Order Quantity',
      [ShippingRuleConditionType.STATE]: 'State',
      [ShippingRuleConditionType.CITY]: 'City',
      [ShippingRuleConditionType.PINCODE]: 'Pincode',
      [ShippingRuleConditionType.PRODUCT_CATEGORY]: 'Product Category'
    };
    return (conditionType: string) => typeMap[conditionType] || conditionType;
  }, []);

  const formatPriority = useMemo(() => {
    const priorityMap: Record<number, string> = {
      [ShippingRulePriority.LOW]: 'Low',
      [ShippingRulePriority.MEDIUM]: 'Medium',
      [ShippingRulePriority.HIGH]: 'High',
      [ShippingRulePriority.CRITICAL]: 'Critical'
    };
    return (priority?: number) => {
      if (!priority) return 'Not set';
      return priorityMap[priority] || priority.toString();
    };
  }, []);

  const formatConditionValues = useMemo(() => {
    return (conditionType: string, values: string[]): string[] => {
      if (conditionType === ShippingRuleConditionType.PRODUCT_CATEGORY) {
        return values.map((value) => {
          const collection = collections.find((col: Collection) => col.id === value);
          return collection ? collection.name : value;
        });
      }
      return values;
    };
  }, [collections]);

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching Shipping Rule</Typography>;

  return (
    <Card>
      <CardContent>
        <Typography variant="h4" gutterBottom>
          Shipping Rule Details
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Condition Type
              </Typography>
              <Typography variant="body1">{formatConditionType(data.conditionType)}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Result
              </Typography>
              <Typography variant="body1">{data.result}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Priority
              </Typography>
              <Typography variant="body1">{formatPriority(data.priority)}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Status
              </Typography>
              <Switch checked={data.isActive} disabled />
              <Typography variant="body2" component="span" sx={{ ml: 1 }}>
                {data.isActive ? 'Active' : 'Inactive'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Condition Values
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {data.conditionValues && data.conditionValues.length > 0 ? (
                  formatConditionValues(data.conditionType, data.conditionValues).map((value, index) => (
                    <Chip key={index} label={value} variant="outlined" />
                  ))
                ) : (
                  <Typography variant="body2" color="textSecondary">
                    No condition values
                  </Typography>
                )}
              </Box>
            </Box>
          </Grid>
          {data.createdOn && (
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Created On
                </Typography>
                <Typography variant="body1">{new Date(data.createdOn).toLocaleDateString()}</Typography>
              </Box>
            </Grid>
          )}
          {data.modifiedOn && (
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Modified On
                </Typography>
                <Typography variant="body1">{new Date(data.modifiedOn).toLocaleDateString()}</Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
}

export default ViewShippingRule;
