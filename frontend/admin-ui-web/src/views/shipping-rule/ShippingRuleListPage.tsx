'use client';

import { useMemo, useState, MouseEvent, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import IconButton from 'components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import { useAuth } from 'contexts/AuthContext';
import Loader from 'components/Loader';
import withPermission from 'hoc/withPermission';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import { PermissionKeys } from 'enums/permission-keys.enum';
import ShippingRuleTable from './ShippingRuleTable';
import { ShippingRule } from 'types/shipping-rule';
import AlertShippingRuleDelete from './AlertShippingRuleDelete';
import {
  useGetShippingRulesQuery,
  useGetShippingRuleCountQuery,
  useUpdateShippingRuleMutation
} from 'redux/app/shipping-rule/shippingRuleApiSlice';
import { useGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { Chip, Switch } from '@mui/material';
import { ShippingRuleConditionType, ShippingRulePriority } from 'enums/shipping-rule.enum';
import { Collection } from 'types/collection';

const ShippingRuleListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10
  });

  const [open, setOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string>('');

  // Fetch collections for displaying collection names
  const { data: collections = [] } = useGetCollectionsQuery({
    where: { status: 'active' }
  });

  const canCreate = hasPermission(PermissionKeys.CreateShippingRule);
  const canEdit = hasPermission(PermissionKeys.UpdateShippingRule);
  const canDelete = hasPermission(PermissionKeys.DeleteShippingRule);

  const filter = useMemo(() => {
    const where = convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['conditionType', 'result']);
    const orderFromSorting = convertSortingToLoopbackSort(sorting);
    const order = orderFromSorting?.length ? orderFromSorting : ['createdOn DESC'];
    const { skip, limit } = convertPaginationToLoopback(pagination);

    return {
      where,
      order,
      skip,
      limit
    };
  }, [columnFilters, globalFilter, sorting, pagination]);

  const { data: shippingRules, isLoading: isListLoading, refetch } = useGetShippingRulesQuery(filter);

  const { data: shippingRuleCount, isLoading: isCountLoading } = useGetShippingRuleCountQuery({
    where: filter.where
  });

  const [updateShippingRule, { isLoading: isUpdating }] = useUpdateShippingRuleMutation();

  const handleToggleStatus = useCallback(
    async (id: string, isActive: boolean) => {
      await updateShippingRule({
        id,
        data: { isActive: !isActive }
      }).unwrap();
      refetch();
    },
    [updateShippingRule, refetch]
  );

  const handleClose = useCallback(() => {
    setOpen(false);
    setDeleteId('');
  }, []);

  // Helper function to format condition type display
  const formatConditionType = useCallback((conditionType: string): string => {
    const typeMap: Record<string, string> = {
      [ShippingRuleConditionType.PAYMENT_MODE]: 'Payment Mode',
      [ShippingRuleConditionType.WEIGHT]: 'Weight',
      [ShippingRuleConditionType.ORDER_VALUE]: 'Order Value',
      [ShippingRuleConditionType.ORDER_QTY]: 'Order Quantity',
      [ShippingRuleConditionType.STATE]: 'State',
      [ShippingRuleConditionType.CITY]: 'City',
      [ShippingRuleConditionType.PINCODE]: 'Pincode',
      [ShippingRuleConditionType.PRODUCT_CATEGORY]: 'Product Category'
    };
    return typeMap[conditionType] || conditionType;
  }, []);

  // Helper function to format priority display
  const formatPriority = useCallback((priority?: number): string => {
    if (!priority) return 'Not set';
    const priorityMap: Record<number, string> = {
      [ShippingRulePriority.LOW]: 'Low',
      [ShippingRulePriority.MEDIUM]: 'Medium',
      [ShippingRulePriority.HIGH]: 'High',
      [ShippingRulePriority.CRITICAL]: 'Critical'
    };
    return priorityMap[priority] || priority.toString();
  }, []);

  // Helper function to format condition values display
  const formatConditionValues = useCallback(
    (conditionType: string, values: string[]): string[] => {
      if (conditionType === ShippingRuleConditionType.PRODUCT_CATEGORY) {
        return values.map((value) => {
          const collection = collections.find((col: Collection) => col.id === value);
          return collection ? collection.name : value;
        });
      }
      return values;
    },
    [collections]
  );

  useEffect(() => {
    refetch();
  }, [refetch]);

  const columns = useMemo<ColumnDef<ShippingRule>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <IndeterminateCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      {
        header: 'Condition Type',
        accessorKey: 'conditionType',
        cell: ({ getValue }) => formatConditionType(getValue() as string)
      },
      {
        header: 'Condition Values',
        accessorKey: 'conditionValues',
        cell: ({ row }) => {
          const values = row.getValue('conditionValues') as string[];
          const conditionType = row.getValue('conditionType') as string;
          const formattedValues = formatConditionValues(conditionType, values);
          return (
            <Stack direction="row" spacing={0.5} flexWrap="wrap">
              {formattedValues?.slice(0, 2).map((value, index) => <Chip key={index} label={value} size="small" variant="outlined" />)}
              {formattedValues?.length > 2 && <Chip label={`+${formattedValues.length - 2} more`} size="small" variant="outlined" />}
            </Stack>
          );
        }
      },
      {
        header: 'Result',
        accessorKey: 'result'
      },
      {
        header: 'Priority',
        accessorKey: 'priority',
        cell: ({ getValue }) => formatPriority(getValue() as number)
      },
      {
        header: 'Status',
        accessorKey: 'isActive',
        cell: ({ row }) => {
          const id = row.original.id;
          const isActive = row.getValue('isActive') as boolean;

          return <Switch checked={isActive} size="small" onChange={() => handleToggleStatus(id ?? '', isActive)} disabled={isUpdating} />;
        }
      },
      {
        header: 'Actions',
        meta: {
          className: 'cell-center'
        },
        disableSortBy: true,
        cell: ({ row }) => {
          const collapseIcon = row.getIsExpanded() ? (
            <Add style={{ color: theme.palette.error.main, transform: 'rotate(45deg)' }} />
          ) : (
            <Eye />
          );

          return (
            <Stack direction="row" alignItems="center" spacing={0}>
              <Tooltip title="View">
                <IconButton onClick={row.getToggleExpandedHandler()}>{collapseIcon}</IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/settings/shipping-rule/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    setDeleteId(row.original.id as string);
                    setOpen(true);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete, isUpdating, handleToggleStatus, formatConditionType, formatPriority, formatConditionValues]
  );

  return (
    <>
      {isListLoading || isCountLoading ? (
        <Loader />
      ) : (
        <ShippingRuleTable
          {...{
            data: shippingRules as ShippingRule[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: isListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: shippingRuleCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertShippingRuleDelete open={open} id={deleteId} title="Shipping Rule" handleClose={handleClose} refetch={refetch} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewShippingRule)(ShippingRuleListPage);
