'use client';

import { useFormik } from 'formik';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  MenuItem,
  Autocomplete
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useCreateShippingRuleMutation, useUpdateShippingRuleMutation } from 'redux/app/shipping-rule/shippingRuleApiSlice';
import { ShippingRule, ShippingRuleFormProps } from 'types/shipping-rule';
import { shippingRuleValidationSchema } from '../../../validations/shipping-rule';
import { useState, useMemo } from 'react';
import { Add, Trash } from 'iconsax-react';
import { ShippingRuleConditionType, ShippingRulePriority } from 'enums/shipping-rule.enum';
import { State, City } from 'country-state-city';
import { useGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { Collection } from 'types/collection';

function ShippingRuleCreate({ isEdit = false, shippingRuleId, initialValues, refetch }: ShippingRuleFormProps) {
  const router = useRouter();
  const [createShippingRule] = useCreateShippingRuleMutation();
  const [updateShippingRule] = useUpdateShippingRuleMutation();
  const [newConditionValue, setNewConditionValue] = useState('');

  // Fetch collections for PRODUCT_CATEGORY condition type
  const { data: collections = [], isLoading: collectionsLoading } = useGetCollectionsQuery({
    where: { status: 'active' }
  });

  const handleFormSubmit = async (values: Partial<ShippingRule>) => {
    const payload: Partial<ShippingRule> = {
      conditionType: values.conditionType,
      conditionValues: values.conditionValues || [],
      result: values.result,
      priority: values.priority !== undefined ? Number(values.priority) : undefined,
      isActive: values.isActive
    };

    if (isEdit && shippingRuleId) {
      await updateShippingRule({ id: shippingRuleId, data: payload }).unwrap();
      openSnackbar({
        open: true,
        message: 'Shipping rule updated successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
    } else {
      await createShippingRule(payload).unwrap();
      openSnackbar({
        open: true,
        message: 'Shipping rule created successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
    }

    if (refetch) refetch();
    router.push('/settings/shipping-rule');
  };

  const formik = useFormik({
    initialValues: {
      conditionType: initialValues?.conditionType || '',
      conditionValues: initialValues?.conditionValues || [],
      result: initialValues?.result || '',
      priority: initialValues?.priority || ShippingRulePriority.MEDIUM,
      isActive: initialValues?.isActive ?? true
    },
    validationSchema: shippingRuleValidationSchema,
    onSubmit: handleFormSubmit
  });

  // Clear condition values when condition type changes
  const handleConditionTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    formik.handleChange(event);
    formik.setFieldValue('conditionValues', []); // Clear existing values
    setNewConditionValue(''); // Clear input field
  };

  const handleAddConditionValue = () => {
    if (newConditionValue.trim()) {
      formik.setFieldValue('conditionValues', [...formik.values.conditionValues, newConditionValue.trim()]);
      setNewConditionValue('');
    }
  };

  const handleRemoveConditionValue = (index: number) => {
    const updatedValues = formik.values.conditionValues.filter((_, i) => i !== index);
    formik.setFieldValue('conditionValues', updatedValues);
  };

  // Generate range options for WEIGHT, ORDER_VALUE, ORDER_QTY
  const generateRangeOptions = (type: string) => {
    const ranges = [];
    const maxValues = {
      [ShippingRuleConditionType.WEIGHT]: 1000, // kg
      [ShippingRuleConditionType.ORDER_VALUE]: 10000, // currency
      [ShippingRuleConditionType.ORDER_QTY]: 100 // quantity
    };

    const stepSizes = {
      [ShippingRuleConditionType.WEIGHT]: 100,
      [ShippingRuleConditionType.ORDER_VALUE]: 1000,
      [ShippingRuleConditionType.ORDER_QTY]: 10
    };

    const max = maxValues[type as keyof typeof maxValues] || 1000;
    const step = stepSizes[type as keyof typeof stepSizes] || 100;

    for (let i = 0; i < max; i += step) {
      const nextValue = i + step;
      ranges.push(`${i}-${nextValue}`);
    }
    ranges.push(`${max}+`); // Add "1000+" option
    return ranges;
  };

  // Get available options based on condition type
  const getConditionValueOptions = useMemo(() => {
    switch (formik.values.conditionType) {
      case ShippingRuleConditionType.WEIGHT:
      case ShippingRuleConditionType.ORDER_VALUE:
      case ShippingRuleConditionType.ORDER_QTY:
        return generateRangeOptions(formik.values.conditionType);

      case ShippingRuleConditionType.STATE:
        return State.getStatesOfCountry('IN').map((state) => state.name);

      case ShippingRuleConditionType.CITY:
        // Get all cities from major Indian states
        const majorStates = ['MH', 'DL', 'KA', 'TN', 'UP', 'WB', 'GJ', 'RJ', 'MP', 'HR', 'KL'];
        const cities: string[] = [];
        majorStates.forEach((stateCode) => {
          const stateCities = City.getCitiesOfState('IN', stateCode);
          cities.push(...stateCities.map((city) => city.name));
        });
        return [...new Set(cities)].sort(); // Remove duplicates and sort

      case ShippingRuleConditionType.PAYMENT_MODE:
        return ['UPI', 'Card', 'Cash on Delivery', 'Net Banking', 'Wallet'];

      case ShippingRuleConditionType.PRODUCT_CATEGORY:
        return collections.map((collection: Collection) => ({
          id: collection.id,
          name: collection.name
        }));

      case ShippingRuleConditionType.PINCODE:
      default:
        return [];
    }
  }, [formik.values.conditionType, collections]);

  const isManualInput = formik.values.conditionType === ShippingRuleConditionType.PINCODE;

  const handleConditionValueSelect = (value: string | { id: string; name: string }) => {
    let valueToAdd: string;

    // Handle collection objects for PRODUCT_CATEGORY
    if (formik.values.conditionType === ShippingRuleConditionType.PRODUCT_CATEGORY && typeof value === 'object') {
      valueToAdd = value.id; // Store collection ID
    } else if (typeof value === 'string') {
      valueToAdd = value;
    } else {
      return; // Invalid value type
    }

    if (!formik.values.conditionValues.includes(valueToAdd)) {
      formik.setFieldValue('conditionValues', [...formik.values.conditionValues, valueToAdd]);
    }
  };

  // Helper function to get display label for condition values
  const getConditionValueDisplayLabel = (value: string): string => {
    if (formik.values.conditionType === ShippingRuleConditionType.PRODUCT_CATEGORY) {
      // Find collection name by ID
      const collection = collections.find((col: Collection) => col.id === value);
      return collection ? collection.name : value;
    }
    return value;
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h4" gutterBottom>
          {isEdit ? 'Edit Shipping Rule' : 'Create Shipping Rule'}
        </Typography>
        <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                select
                id="conditionType"
                name="conditionType"
                label="Condition Type"
                value={formik.values.conditionType}
                onChange={handleConditionTypeChange}
                onBlur={formik.handleBlur}
                error={formik.touched.conditionType && Boolean(formik.errors.conditionType)}
                helperText={formik.touched.conditionType && formik.errors.conditionType}
                sx={{ flex: 1 }}
              >
                {Object.entries(ShippingRuleConditionType).map(([key, value]) => (
                  <MenuItem key={key} value={value}>
                    {key.replace(/_/g, ' ')}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} md={6}>
              {/* Show different input types based on condition type */}
              {isManualInput ? (
                <Box sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    fullWidth
                    label="Add Pincode (6 digits)"
                    value={newConditionValue}
                    onChange={(e) => setNewConditionValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddConditionValue();
                      }
                    }}
                    inputProps={{ maxLength: 6, pattern: '[0-9]*' }}
                    helperText="Enter a 6-digit pincode"
                  />
                  <IconButton sx={{ mt: 0.6 }} onClick={handleAddConditionValue} color="primary">
                    <Add />
                  </IconButton>
                </Box>
              ) : (
                // Dropdown for other condition types
                getConditionValueOptions.length > 0 && (
                  <>
                    <Box sx={{ mb: 2 }}>
                      <Autocomplete<string | { id: string; name: string }>
                        options={getConditionValueOptions as (string | { id: string; name: string })[]}
                        value={null}
                        onChange={(_, value) => {
                          if (value) {
                            handleConditionValueSelect(value);
                          }
                        }}
                        getOptionLabel={(option) => {
                          // Handle collection objects
                          if (typeof option === 'object' && 'name' in option) {
                            return option.name;
                          }
                          // Handle string values
                          return typeof option === 'string' ? option : '';
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Select Condition Value"
                            placeholder="Choose from available options"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {collectionsLoading && formik.values.conditionType === ShippingRuleConditionType.PRODUCT_CATEGORY ? (
                                    <Typography variant="caption" sx={{ mr: 1 }}>
                                      Loading...
                                    </Typography>
                                  ) : null}
                                  {params.InputProps.endAdornment}
                                </>
                              )
                            }}
                          />
                        )}
                        disabled={
                          !formik.values.conditionType ||
                          (formik.values.conditionType === ShippingRuleConditionType.PRODUCT_CATEGORY && collectionsLoading)
                        }
                      />
                    </Box>
                  </>
                )
              )}

              {/* Display selected condition values as chips */}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formik.values.conditionValues.map((value, index) => (
                  <Chip
                    key={index}
                    label={getConditionValueDisplayLabel(value)}
                    onDelete={() => handleRemoveConditionValue(index)}
                    deleteIcon={<Trash />}
                    variant="outlined"
                  />
                ))}
              </Box>

              {formik.touched.conditionValues && formik.errors.conditionValues && (
                <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                  {formik.errors.conditionValues}
                </Typography>
              )}
            </Grid>

            <Grid item xs={12}>
              <Box display="flex" gap={2}>
                <TextField
                  fullWidth
                  select
                  id="priority"
                  name="priority"
                  label="Priority"
                  value={formik.values.priority}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.priority && Boolean(formik.errors.priority)}
                  helperText={formik.touched.priority && formik.errors.priority}
                  sx={{ flex: 1 }}
                >
                  {Object.keys(ShippingRulePriority)
                    .filter((key) => isNaN(Number(key)))
                    .map((key) => (
                      <MenuItem key={key} value={ShippingRulePriority[key as keyof typeof ShippingRulePriority]}>
                        {key.charAt(0) + key.slice(1).toLowerCase()}
                      </MenuItem>
                    ))}
                </TextField>

                <TextField
                  fullWidth
                  select
                  id="result"
                  name="result"
                  label="Result"
                  value={formik.values.result}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.result && Boolean(formik.errors.result)}
                  helperText={formik.touched.result && formik.errors.result}
                  sx={{ flex: 1 }}
                >
                  <MenuItem value="Shipyaari">Shipyaari</MenuItem>
                  <MenuItem value="Shiprocket">Shiprocket</MenuItem>
                </TextField>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formik.values.isActive}
                    onChange={(e) => formik.setFieldValue('isActive', e.target.checked)}
                    name="isActive"
                  />
                }
                label="Active"
                sx={{ ml: 2, alignSelf: 'center' }}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button variant="outlined" onClick={() => router.push('/settings/shipping-rule')}>
                  Cancel
                </Button>
                <Button type="submit" variant="contained">
                  {isEdit ? 'Update' : 'Create'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
}

export default ShippingRuleCreate;
