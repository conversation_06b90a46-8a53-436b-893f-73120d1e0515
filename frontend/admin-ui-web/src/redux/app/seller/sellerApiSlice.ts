import { SellerFormValues, SellerList } from 'types/seller';
import { ApiSliceIdentifier } from '../../../enums/api.enum';
import { apiSlice } from '../../../redux/apiSlice';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';

export const sellerApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSellers: builder.query<SellerList[], IFilter | void>({
      query: (filter) => ({
        url: '/sellers',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getSellersCount: builder.query<{ count: number }, IFilter>({
      query: (filter) => ({
        url: '/sellers/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getSellerById: builder.query<SellerList, { id: string; include?: Array<Record<string, unknown> | string> }>({
      query: ({ id, include }) => ({
        url: `/sellers/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: include || []
          })
        }
      })
    }),
    removeSeller: builder.mutation<void, string>({
      query: (id) => ({
        url: `/sellers/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateSellerStatus: builder.mutation<
      void,
      { id: string; status: string; verificationCode: string; rejectionReason?: string; onHoldReason?: string }
    >({
      query: ({ id, status, verificationCode, rejectionReason, onHoldReason }) => ({
        url: `/sellers/${id}/status`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {
          status,
          verificationCode,
          rejectionReason: rejectionReason || '',
          onHoldReason: onHoldReason || ''
        }
      })
    }),
    updateSeller: builder.mutation<void, { id: string; data: Partial<SellerFormValues> }>({
      query: ({ id, data }) => ({
        url: `/sellers/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    })
  })
});

export const {
  useRemoveSellerMutation,
  useGetSellersQuery,
  useLazyGetSellersCountQuery,
  useLazyGetSellerByIdQuery,
  useUpdateSellerStatusMutation,
  useUpdateSellerMutation,
  useLazyGetSellersQuery,
  useGetSellersCountQuery
} = sellerApiSlice;
