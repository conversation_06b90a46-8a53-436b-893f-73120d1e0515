import {ShippingMethodType, ShippingStatus} from '../enums/shipping.enum';

export interface Shipping {
  id?: string;
  sellerId: string;
  shippingCost: number;
  status?: ShippingStatus;
  minDeliveryDate?: Date;
  maxDeliveryDate?: Date;
  expectedDeliveryDate?: string;
  actualDeliveryDate?: string;
  trackingNumber?: string;
  trackingUrl?: string;
  shippingCarrier?: string;
  shippingMethodType?: ShippingMethodType;
  shippingNotes?: string;
  cartItemId?: string;
  orderLineItemId?: string;
  shippingMethodId?: string;
}

export interface PincodeShippingCalculationRequestDto {
  productVariantId: string;
  pincode: string;
}

export interface PincodeShippingCalculationResponseDto {
  shippingCost: number;
  minDeliveryDate: string;
  maxDeliveryDate: string;
  expectedDeliveryDate: string;
  shippingType: string;
  isServiceable: boolean;
  message?: string;
}
