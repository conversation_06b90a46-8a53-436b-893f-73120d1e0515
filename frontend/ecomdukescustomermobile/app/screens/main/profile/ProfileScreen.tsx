import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import {
  Text,
  TextInput,
  Divider,
  Icon,
  Portal,
  Dialog,
} from 'react-native-paper';
import CustomButton from '../../../components/CustomButton/CustomButton';
import customColors from '../../../theme/customColors';
import CustomTextInput from '../../../components/InputFields/CustomTextInput';
import {colors} from '../../../theme/colors';
import {
  useGetUserQuery,
  useUpdateCustomerStatusMutation,
  useUpdateUserMutation,
} from '../../../redux/auth/authApiSlice';
import {useFormik} from 'formik';
import Toast from 'react-native-toast-message';
import {launchImageLibrary} from 'react-native-image-picker';
import {useDispatch} from 'react-redux';
import {SellerStatus} from '../../../types/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {unsetCredentials} from '../../../redux/auth/authSlice';
import {useNavigation} from '@react-navigation/native';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../../navigations/types';
type SelectedImageType = {
  uri: string;
  name: string;
  type: string;
};
const ProfileScreen = () => {
  const genderOptions = [
    {label: 'Male', value: 'M'},
    {label: 'Female', value: 'F'},
    {label: 'Other', value: 'O'},
  ];
  const {data: user} = useGetUserQuery();
  const [updateUser] = useUpdateUserMutation();
  const [selectedImage, setSelectedImage] = useState<SelectedImageType | null>(
    null,
  );
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const customerId = user?.profileId ?? '';
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [updateCustomerStatus] = useUpdateCustomerStatusMutation();
  const dispatch = useDispatch();

  const confirmDeactivation = async () => {
    if (!customerId) {
      return;
    }

    await updateCustomerStatus({
      id: customerId,
      status: SellerStatus.INACTIVE,
    }).unwrap();
    Toast.show({text1: 'Deactivated successfully'});

    dispatch(unsetCredentials?.());
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'AuthStack',
          state: {
            routes: [{name: SCREEN_NAME.LOGIN}],
          },
        },
      ],
    });
    await AsyncStorage.removeItem('accessToken');
    await AsyncStorage.removeItem('refreshToken');
  };

  const [isEditingImage, setIsEditingImage] = useState(false);

  const [previewUri, setPreviewUri] = useState<string | null>(null); // should be string!

  const handlePickImage = async () => {
    const result = await launchImageLibrary({mediaType: 'photo'});

    if (!result.didCancel && result.assets && result.assets.length > 0) {
      const image = result.assets[0];
      const imageObj = {
        uri: image.uri!,
        name: image.fileName ?? 'profile.jpg',
        type: image.type ?? 'image/jpeg',
      };
      formik.setFieldValue('photoUrl', image.uri!);

      setSelectedImage(imageObj);
      setPreviewUri(image.uri!);
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      gender: user?.gender || '',
      email: user?.email || '',
      phone: user?.phone || '',
      password: '',
      photoUrl: user?.photoUrl || '',
      twoStepEnabled: true,
    },
    // validationSchema: profileFormValidation,
    onSubmit: values => {
      handleProfileUpdate(values);
    },
  });

  const handleProfileUpdate = async (values: typeof formik.initialValues) => {
    if (!user?.profileId) {
      return;
    }
    const formData = new FormData();
    formData.append('firstName', values.firstName);
    formData.append('lastName', values.lastName);
    formData.append('email', values.email);
    formData.append('phone', values.phone);
    // formData.append('twoStepEnabled', String(values.twoStepEnabled));
    if (values.gender) {
      formData.append('gender', values.gender);
    }

    if (selectedImage) {
      const imageUri =
        Platform.OS === 'ios'
          ? selectedImage.uri.replace('file://', '')
          : selectedImage.uri;

      formData.append('photoUrl', {
        uri: imageUri,
        name: selectedImage.name,
        type: selectedImage.type,
      } as any);
    }

    await updateUser({userId: user.id, body: formData}).unwrap();
    Toast.show({
      text1: 'Profile Updated',
      type: 'success',
    });
  };
  const [isEditable, setIsEditable] = useState(false);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.containerView}>
        <View style={styles.section}>
          <View style={styles.rowBetween}>
            <Text
              variant="titleSmall"
              style={{fontWeight: 'bold', fontSize: 16}}>
              Personal Information
            </Text>
            {isEditable ? (
              <Text
                style={styles.edit}
                onPress={() => {
                  setIsEditable(!isEditable);
                  setIsEditingImage(!isEditingImage);
                }}>
                View
              </Text>
            ) : (
              <Text
                style={styles.edit}
                onPress={() => {
                  setIsEditable(!isEditable);
                  setIsEditingImage(!isEditingImage);
                }}>
                {' '}
                Edit
              </Text>
            )}
          </View>
          <View style={styles.profileImageContainer}>
            <TouchableOpacity
              onPress={() => {
                if (isEditingImage) handlePickImage();
              }}
              activeOpacity={isEditingImage ? 0.7 : 1}>
              <View style={styles.profileImageWrapper}>
                {previewUri || formik.values.photoUrl ? (
                  <Image
                    source={{uri: previewUri || formik.values.photoUrl}}
                    style={styles.profileImage}
                  />
                ) : (
                  <View style={styles.profileImagePlaceholder} />
                )}
                {isEditingImage && (
                  <View style={styles.cameraIconOverlay}>
                    <Icon source="camera" size={35} color="#fff" />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.row}>
            <TextInput
              mode="outlined"
              style={styles.inputHalf}
              value={formik.values.firstName}
              onChangeText={formik.handleChange('firstName')}
              activeOutlineColor={colors.tertiary}
              textColor={isEditable ? customColors.textBlack : colors.gray.dark}
              editable={isEditable}
            />
            <TextInput
              mode="outlined"
              style={styles.inputHalf}
              value={formik.values.lastName}
              onChangeText={formik.handleChange('lastName')}
              activeOutlineColor={colors.tertiary}
              textColor={isEditable ? customColors.textBlack : colors.gray.dark}
              editable={isEditable}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text variant="titleSmall" style={{fontWeight: 'bold', fontSize: 16}}>
            Gender
          </Text>
          <View style={styles.genderOptionRow}>
            {genderOptions.map(option => (
              <View key={option.value} style={styles.genderOption}>
                {isEditable ? (
                  <TouchableOpacity
                    onPress={() =>
                      formik.setFieldValue('gender', option.value)
                    }>
                    <Icon
                      source={
                        formik.values.gender === option.value
                          ? 'checkbox-blank-circle'
                          : 'checkbox-blank-circle-outline'
                      }
                      size={25}
                      color={
                        formik.values.gender === option.value
                          ? colors.tertiary
                          : colors.gray.dark
                      }
                    />
                  </TouchableOpacity>
                ) : (
                  <Icon
                    source={
                      formik.values.gender === option.value
                        ? 'checkbox-blank-circle'
                        : 'checkbox-blank-circle-outline'
                    }
                    size={25}
                    color={
                      formik.values.gender === option.value
                        ? colors.tertiary
                        : colors.gray.dark
                    }
                  />
                )}
                <Text style={styles.genderText}>{option.label}</Text>
              </View>
            ))}
          </View>
        </View>
        <View style={styles.section}>
          <View style={styles.rowBetween}>
            <Text
              variant="titleSmall"
              style={{fontWeight: 'bold', fontSize: 16}}>
              Email Address
            </Text>
          </View>
          <CustomTextInput
            title=""
            mode="outlined"
            style={{padding: 10, borderRadius: 5}}
            value={formik.values.email}
            onChangeText={formik.handleChange('email')}
            textColor={isEditable ? customColors.textBlack : colors.gray.dark}
            editable={isEditable}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.rowBetween}>
            <Text
              variant="titleSmall"
              style={{fontWeight: 'bold', fontSize: 16}}>
              Mobile Number
            </Text>
          </View>
          <CustomTextInput
            mode="outlined"
            title=""
            style={{padding: 10, borderRadius: 5}}
            value={formik.values.phone}
            onChangeText={formik.handleChange('phone')}
            keyboardType="phone-pad"
            textColor={isEditable ? customColors.textBlack : colors.gray.dark}
            editable={isEditable}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.verificationView}>
            <Text
              variant="titleSmall"
              style={{fontWeight: 'bold', fontSize: 16}}>
              Two step verification
            </Text>
            <Switch
              value={formik.values.twoStepEnabled}
              onValueChange={val => {
                formik.setFieldValue('twoStepEnabled', val);
              }}
              trackColor={{
                false: colors.gray.light,
                true: colors.tertiary,
              }}
            />
          </View>
        </View>
        {isEditable && (
          <CustomButton
            title={'Save Profile'}
            mode="contained"
            onPress={formik.handleSubmit}
          />
        )}
        <CustomButton
          title=" Deactivate Account"
          onPress={() => setShowDeactivateModal(true)}
        />

        <Divider style={styles.bottomNavDivider} />
      </View>
      <Portal>
        <Dialog
          visible={showDeactivateModal}
          onDismiss={() => setShowDeactivateModal(false)}
          style={styles.dialogBox}>
          <Dialog.Content>
            <Text style={styles.modalheader}>
              Are you sure you want to deactivate your account?
            </Text>
          </Dialog.Content>
          <Dialog.Actions style={styles.dialogActions}>
            <CustomButton
              title="Cancel"
              style={{width: '40%'}}
              onPress={() => setShowDeactivateModal(false)}
              textColor={colors.gray.dark}
              mode="outlined"
            />
            <CustomButton
              style={{width: '40%'}}
              title=" Deactivate"
              onPress={confirmDeactivation}
              mode="contained"
            />
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: customColors.white,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
    justifyContent: 'flex-start',
  },
  containerView: {flex: 1, backgroundColor: customColors.white, padding: 15},

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  section: {
    marginVertical: 10,
  },
  inputHalf: {
    flex: 1,
    marginRight: 8,
  },
  row: {
    flexDirection: 'row',
    gap: 8,
  },
  rowBetween: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  verificationView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    gap: 8,
  },
  radioRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  edit: {
    color: customColors.primaryContainer,
    fontWeight: '500',
  },
  deactivateBtn: {
    marginTop: 20,
    paddingVertical: 6,
  },
  bottomNavDivider: {
    marginTop: 40,
  },
  genderOptionRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 8,
  },
  genderOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  genderText: {
    marginLeft: 4,
    fontSize: 14,
  },
  profileImageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },

  profileImageWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    backgroundColor: '#ddd',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },

  profileImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },

  profileImagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#ccc',
  },

  cameraIconOverlay: {
    position: 'absolute',
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  dialogBox: {
    borderRadius: 12,
    backgroundColor: customColors.white,
  },
  modalheader: {fontSize: 16, marginLeft: 10},
  dialogActions: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default ProfileScreen;
