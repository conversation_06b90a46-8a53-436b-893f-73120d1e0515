import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {IconButton} from 'react-native-paper';
import {colors} from '../../../../../theme/colors';

type Variant = {
  id: string;
  productOptionId: string;
  productOptionGroupId: string;
  productVariantId: string;
  productOption: {
    id: string;
    name: string;
    code: string;
    productOptionGroup: {
      id: string;
      name: string;
      code: string;
      unit: string;
    };
  };
};

type Props = {
  variants: Variant[];
};

const VariantOptions = ({variants}: Props) => {
  const [selectedOptions, setSelectedOptions] = useState<
    Record<string, string>
  >({});

  const groupedVariants = variants.reduce((acc, variant) => {
    const groupName =
      variant.productOption?.productOptionGroup?.name || 'Unknown';
    if (!acc[groupName]) {
      acc[groupName] = [];
    }
    acc[groupName].push(variant);
    return acc;
  }, {} as Record<string, Variant[]>);

  const handleSelect = (groupName: string, variantId: string) => {
    setSelectedOptions(prev => ({
      ...prev,
      [groupName]: variantId,
    }));
  };

  return (
    <View style={styles.container}>
      {Object.entries(groupedVariants).map(([groupName, groupVariants]) => (
        <View key={groupName} style={styles.groupContainer}>
          <Text style={styles.groupTitle}>{groupName}</Text>

          {groupVariants.map(variant => {
            const isSelected = selectedOptions[groupName] === variant.id;
            return (
              <TouchableOpacity
                key={variant.id}
                style={styles.optionRow}
                onPress={() => handleSelect(groupName, variant.id)}>
                <IconButton
                  icon={isSelected ? 'circle-slice-8' : 'circle-outline'}
                  iconColor={isSelected ? colors.primary : colors.gray.dark}
                  size={24}
                />
                <Text style={styles.optionText}>
                  {variant.productOption?.name}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      ))}
    </View>
  );
};

export default VariantOptions;

const styles = StyleSheet.create({
  container: {
    // padding: 16,
  },
  groupContainer: {
    // marginBottom: 24,
  },
  groupTitle: {
    fontSize: 14,
    flex: 1,
    fontWeight: 'bold',
    // marginBottom: 8,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: 10,
  },
  optionText: {
    fontSize: 14,
  },
});
