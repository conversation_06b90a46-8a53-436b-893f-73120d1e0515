import React, {useState} from 'react';
import {Dimensions, FlatList, Image, StyleSheet, View} from 'react-native';
import customColors from '../../../../../theme/customColors';
import {colors} from '../../../../../theme/colors';
import Video from 'react-native-video';
type MediaItem = {
  type: 'image' | 'video';
  url: string;
};
const ImageSwiper = ({media}: {media: MediaItem[]}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const {width} = Dimensions.get('window');

  const handleScroll = (e: any) => {
    const index = Math.round(e.nativeEvent.contentOffset.x / width);
    setCurrentIndex(index);
  };

  return (
    <View>
      <FlatList
        data={media}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        renderItem={({item}) =>
          item.type === 'image' ? (
            <Image
              source={{uri: item.url}}
              style={styles.image}
              resizeMode="contain"
            />
          ) : (
            <Video
              source={{uri: item.url}}
              style={styles.video}
              resizeMode="contain"
              controls
              paused={false}
              repeat
            />
          )
        }
        keyExtractor={(_, index) => index.toString()}
      />

      <View
        style={{flexDirection: 'row', justifyContent: 'center', marginTop: 10}}>
        {media.map((_, i) => (
          <View
            key={i}
            style={{
              width: 8,
              height: 8,
              borderRadius: 4,
              backgroundColor: currentIndex === i ? '#000' : '#ccc',
              margin: 4,
            }}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 10,
  },
  dot: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: colors.gray.medium,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: customColors.textBlack,
  },
  image: {
    width: 410,
    height: 350,
    // objectFit: 'contain',
    borderRadius: 8,
    // margin: 15,
  },
  video: {
    width: 410,
    height: 350,
    // objectFit: 'contain',
    borderRadius: 8,
    // margin: 15,
  },
});
export default ImageSwiper;
