import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {Text, Icon} from 'react-native-paper';
import {colors} from '../../../../../theme/colors';
type InfoCardProps = {
  handmadeWork?: string;
  uniqueFeature?: string;
  suitableFor?: string;
  deliveryBoxItems?: string;
};
const InfoCard: React.FC<InfoCardProps> = ({
  handmadeWork,
  uniqueFeature,
  suitableFor,
  deliveryBoxItems,
}) => {
  return (
    <View style={styles.card}>
      <View style={styles.row}>
        <Text style={styles.label}>Handmade/Personalised Work Involved:</Text>
        <Text style={styles.value}>{handmadeWork}</Text>
      </View>
      <View style={styles.row}>
        <Text style={styles.label}>What Makes this Product Unique:</Text>
        <Text style={styles.value}>{uniqueFeature}</Text>
      </View>
      <View style={styles.row}>
        <Text style={styles.label}>Suitable For:</Text>
        <Text style={styles.value}>{suitableFor}</Text>
      </View>
      <View style={styles.row}>
        <Text style={styles.label}>In the delivery box:</Text>
        <Text style={styles.value}>{deliveryBoxItems}</Text>
      </View>
    </View>
  );
};
type Props = {
  isGiftWrapAvailable: boolean;
  giftWrapCharge: string;
  information?: InfoCardProps;
  deliveryDate?: string;
  dispatchDate?: string;
  orderDate?: string;
};
const DeliveryDetails: React.FC<Props> = ({
  isGiftWrapAvailable,
  giftWrapCharge,
  information,
  orderDate,
  dispatchDate,
  deliveryDate,
}) => {
  const dateItems = [
    {
      label: 'Order Date',
      value: orderDate || '—',
      icon: 'thumb-up-outline',
    },
    {
      label: 'Dispatch By',
      value: dispatchDate || '—',
      icon: 'van-utility',
    },
    {
      label: 'Delivery By',
      value: deliveryDate || '—',
      icon: 'toolbox-outline',
    },
  ];

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.orderView}>
        <View style={styles.order}>
          {dateItems.map((item, index) => (
            <View key={index} style={styles.iconBlock}>
              <Text style={styles.iconLabel}>{item.label}</Text>
              <Text style={styles.iconSubLabel}>{item.value}</Text>
              <Icon source={item.icon} size={35} />
            </View>
          ))}
        </View>
      </View>

      <View style={styles.gift}>
        <Icon size={30} source="gift" />
        <Text>
          {isGiftWrapAvailable
            ? 'Gift Wrapping Available'
            : 'Gift wrap is not available'}
        </Text>
        <Text style={{color: colors.gray.dark}}>{giftWrapCharge}</Text>
      </View>

      {information && <InfoCard {...information} />}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  dropdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  orderView: {
    marginTop: 10,
    borderWidth: 1,
    borderColor: colors.gray.light,
    borderRadius: 20,
    padding: 16,
  },
  order: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  iconBlock: {
    width: '30%',
    minWidth: 100,
    alignItems: 'center',
    marginBottom: 12,
  },

  iconLabel: {
    fontSize: 13,
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },
  iconSubLabel: {
    fontSize: 12,
    textAlign: 'center',
    color: '#555',
    marginTop: 2,
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#0a0a5b',
    marginTop: 20,
    marginBottom: 8,
  },
  sectionBox: {
    backgroundColor: colors.gray.backGround,
    padding: 12,
    borderRadius: 12,
  },

  textArea: {
    marginTop: 8,
  },
  uploadBox: {
    backgroundColor: colors.gray.backGround,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 30,
    justifyContent: 'space-between',
  },
  typeView: {
    marginLeft: 6,
    marginTop: 17,
    borderWidth: 1,
    borderColor: colors.gray.light,
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 10,
    height: 100,
  },
  type: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  Customorder: {
    marginTop: 18,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  gift: {
    backgroundColor: colors.gray.backGround,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 19,
    borderRadius: 12,
    marginTop: 20,
    flexWrap: 'wrap',
  },
  description: {
    backgroundColor: colors.gray.backGround,
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-evenly',
    padding: 20,
    borderRadius: 12,
    marginBottom: 30,
  },
  descriptionText: {fontSize: 13},
  divider: {
    height: 1,
    backgroundColor: colors.gray.light,
    marginBottom: 20,
    width: 295,
    margin: 10,
  },
  formHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  imgText: {marginLeft: 10},
  card: {
    flex: 1,
    backgroundColor: colors.gray.backGround, // light gray background like the image
    borderRadius: 16,
    padding: 16,
    marginTop: 20,
  },
  text: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
  },
  // card: {
  //   padding: 12,
  //   backgroundColor: '#fff',
  //   borderRadius: 8,
  //   elevation: 2,
  //   marginVertical: 10,
  // },

  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },

  label: {
    fontWeight: '600',
    fontSize: 14,
    color: '#333',
    flex: 1.2,
  },

  value: {
    fontWeight: '400',
    fontSize: 14,
    color: '#555',
    flex: 1,
    textAlign: 'left',
  },
});

export default DeliveryDetails;
