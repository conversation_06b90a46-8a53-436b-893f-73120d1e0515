import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Icon} from 'react-native-paper';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';

type PaymentType = 'upi' | 'cod';

const PaymentOptions = ({
  selectedPayment,
  onChange,
}: {
  selectedPayment: PaymentType | null;
  onChange: (payment: PaymentType) => void;
}) => {
  const [selected, setSelected] = useState<PaymentType | null>(selectedPayment);

  useEffect(() => {
    if (selected) {
      onChange(selected);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected]);

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Payment Method</Text>

      {(['upi', 'cod'] as PaymentType[]).map(method => (
        <TouchableOpacity
          key={method}
          style={[styles.option, selected === method && styles.optionSelected]}
          onPress={() => setSelected(method)}>
          <Icon
            source={selected === method ? 'radiobox-marked' : 'radiobox-blank'}
            size={20}
            color={
              selected === method ? customColors.white : customColors.textBlack
            }
          />

          <Text
            style={[styles.text, selected === method && styles.textSelected]}>
            {method === 'upi' ? 'UPI Payment' : 'COD (Cash On Delivery)'}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default PaymentOptions;
const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 10,
    backgroundColor: 'white',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 12,
    color: '#000',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray.backGround,
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
  },
  optionSelected: {
    backgroundColor: customColors.primaryContainer,
  },
  iconSpacing: {
    marginHorizontal: 12,
  },
  text: {
    marginLeft: 10,
    fontSize: 15,
    color: customColors.textBlack,
    fontWeight: 'bold',
  },
  textSelected: {
    fontWeight: 'bold',
    color: customColors.white,
  },
});
