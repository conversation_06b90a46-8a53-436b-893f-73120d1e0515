import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
import {Icon} from 'react-native-paper';
import {fieldsExcludeMetaFields} from '../../../../types/api';
import {useLazyGetProductCustomizationsQuery} from '../../../../redux/product/productApiSlice';
import ProductCustomizationForm, {
  CustomizationField,
} from '../../Products/Components/ProductCustomization';
import {useUpdateProductCustomizationsMutation} from '../../../../redux/cart/cartApiSlice';
import Toast from 'react-native-toast-message';

interface ProductCardProps {
  productVariantId: string;
  productId: string;
  cartId: string;
  image: any;
  title: string;
  subtitle: string;
  price: string;
  mrp: string;
  quantity?: string;
  onAdd: (newQty: number) => void;
  onRemove: (newQty: number) => void;
  onDelete: () => void;
  isLoading: boolean;
  onPress: () => void;
  minDeliveryDate?: string;
  maxDeliveryDate?: string;
}

interface OrderCardOptionsProps {
  onRemove: (newQty: number) => void;
}
type OrderProductCardProps = ProductCardProps & OrderCardOptionsProps;
const OrderProductCard: React.FC<OrderProductCardProps> = ({
  productVariantId,
  productId,
  cartId,
  image,
  title,
  subtitle,
  price,
  mrp,
  quantity = '0',
  onAdd,
  onDelete,
  onRemove,
  onPress,
  minDeliveryDate = '—',
  maxDeliveryDate = '—',
}) => {
  const [isFormVisible, setFormVisible] = useState(false);
  const [qty, setQty] = useState(Number(quantity) || 1);
  const [updateCustomizations] = useUpdateProductCustomizationsMutation();

  const [triggerGetCustomizations, {data: customizationFields}] =
    useLazyGetProductCustomizationsQuery();

  const fetchCustomizationFields = useCallback(async () => {
    const res = await triggerGetCustomizations({
      where: {
        productVariantId,
      },
      include: [
        {
          relation: 'productCustomizationOptions',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'customizationValue',
          scope: {
            fields: fieldsExcludeMetaFields,
            where: {
              cartId,
            },
          },
        },
      ],
    }).unwrap();

    if (!res.length && productId) {
      await triggerGetCustomizations({
        where: {
          productId,
        },
        include: [
          {
            relation: 'productCustomizationOptions',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
          {
            relation: 'customizationValue',
            scope: {
              fields: fieldsExcludeMetaFields,
              where: {
                cartId,
              },
            },
          },
        ],
      });
    }
  }, [productVariantId, productId, cartId, triggerGetCustomizations]); // <-- all deps used inside

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    fetchCustomizationFields();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productId, productVariantId, cartId]);

  const customizationFormFields: CustomizationField[] = (
    customizationFields || []
  ).map(field => ({
    ...field,
    fieldType: field.fieldType as CustomizationField['fieldType'],
  }));

  const [customizationValuesState, setCustomizationValuesState] = useState<
    Record<string, string>
  >({});
  useEffect(() => {
    const initialState: Record<string, string> = {};

    customizationValueFromBackend.forEach(item => {
      initialState[item.customizationFieldId] = item.value;
    });

    setCustomizationValuesState(initialState);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customizationFields]);

  const handleQuantityUpdate = async () => {
    const customizationValues = customizationFormFields.map(field => {
      const selectedValue = customizationValuesState[field.name];

      let value = '';

      if (field.fieldType === 'file') {
        // Handle image upload values
        value = JSON.stringify(selectedValue || []);
      } else {
        const selectedOption = field.productCustomizationOptions?.find(
          option => option.value === selectedValue,
        );
        value = selectedOption?.value || '';
      }

      return {
        customizationFieldId: field.id,
        value,
        id: field.id, // or selectedOption?.id if applicable
      };
    });

    await updateCustomizations({
      cartId,
      data: customizationValues,
    }).unwrap();

    setFormVisible(false);
    await fetchCustomizationFields();

    Toast.show({
      type: 'success',
      text1: 'Updated Successfully',
    });
  };

  const customizationValueFromBackend = (customizationFields ?? [])
    .filter(field => field.customizationValue && field.customizationValue.value)
    .map(field => ({
      customizationFieldId: field.id,
      value: field.customizationValue?.value || '',
      fieldType: field.fieldType,
    }));

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <View style={styles.card}>
        <View style={styles.cardContainer}>
          <Image
            source={{uri: image}}
            style={styles.productImage}
            resizeMode="contain"
          />
          <View style={styles.details}>
            <View style={styles.headerRow}>
              <Text style={styles.title}>{title}</Text>
              {(customizationFields ?? []).length > 0 && (
                <TouchableOpacity
                  onPress={() => setFormVisible(!isFormVisible)}>
                  <Icon
                    source={
                      isFormVisible
                        ? 'arrow-down-drop-circle-outline'
                        : 'arrow-right-drop-circle-outline'
                    }
                    size={20}
                  />
                </TouchableOpacity>
              )}
            </View>
            <Text style={styles.subtitle} numberOfLines={2}>
              {subtitle}
            </Text>
            <Text style={styles.mrp}>{mrp}</Text>
            <Text style={styles.price}>{price}</Text>
            {minDeliveryDate && maxDeliveryDate && (
              <Text style={styles.delivery}>
                <Text style={styles.deliveryLabel}>
                  Estimated Delivery between:{' '}
                </Text>
                <Text style={styles.deliveryDate}>
                  {new Intl.DateTimeFormat('en-US', {
                    month: 'short',
                    day: 'numeric',
                  }).format(new Date(minDeliveryDate))}
                  {' – '}
                  {new Intl.DateTimeFormat('en-US', {
                    month: 'short',
                    day: 'numeric',
                  }).format(new Date(maxDeliveryDate))}
                </Text>
              </Text>
            )}

            <View style={styles.quantityControl}>
              <TouchableOpacity
                onPress={() => {
                  if (qty === 1) {
                    onDelete();
                  } else {
                    setQty(prev => prev - 1);
                    onRemove(qty - 1);
                  }
                }}>
                <Icon
                  source={qty === 1 ? 'delete' : 'minus-circle-outline'}
                  size={23}
                  color={customColors.textBlack}
                />
              </TouchableOpacity>

              <Text style={styles.quantityText}>{qty}</Text>

              <TouchableOpacity
                onPress={() => {
                  setQty(prev => prev + 1);
                  onAdd(qty + 1);
                }}>
                <Icon
                  source="plus-circle-outline"
                  size={23}
                  color={customColors.textBlack}
                />
              </TouchableOpacity>

              <TouchableOpacity onPress={onDelete}>
                <View style={styles.deleteButtonView}>
                  <View style={styles.leftVerticalLine} />

                  <Text style={{color: customColors.primaryContainer}}>
                    Delete
                  </Text>

                  <View style={styles.rightVerticalLine} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {isFormVisible &&
          customizationFields &&
          customizationFields.length > 0 && (
            <ProductCustomizationForm
              fields={customizationFormFields}
              onSubmit={handleQuantityUpdate}
              onChange={setCustomizationValuesState}
              prefillValues={customizationValueFromBackend}
            />
          )}
      </View>
    </TouchableOpacity>
  );
};

export default OrderProductCard;

const styles = StyleSheet.create({
  card: {
    marginBottom: 20,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
    borderColor: colors.gray.medium,
  },
  cardContainer: {flexDirection: 'row'},
  productImage: {width: 100, height: 100, borderRadius: 8},
  details: {flex: 1, marginLeft: 12},
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {fontWeight: 'bold', fontSize: 14},
  subtitle: {color: colors.gray.dark, fontSize: 13},
  delivery: {
    fontSize: 12,
    flexWrap: 'wrap',
  },
  deliveryLabel: {
    color: customColors.textBlack,
  },
  deliveryDate: {
    fontSize: 11,
    color: colors.tertiary,
    fontWeight: 'bold',
  },

  price: {marginTop: 4, fontSize: 12, color: customColors.successGreen},
  mrp: {
    textDecorationLine: 'line-through',
    color: colors.gray.dark,
    fontSize: 12,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  quantityInput: {
    marginHorizontal: 8,
    padding: 4,
    borderWidth: 1,
    width: 40,
    borderColor: colors.gray.medium,
    textAlign: 'center',
    borderRadius: 20,
  },
  editView: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  edit: {color: colors.gray.dark},
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 7,
    marginTop: 8,
  },
  quantityText: {
    fontSize: 16,
    fontWeight: 'bold',
    width: 30,
    textAlign: 'center',
  },
  leftVerticalLine: {
    width: 1,
    height: 16,
    backgroundColor: colors.gray.medium,
    marginRight: 8,
  },
  rightVerticalLine: {
    width: 1,
    height: 16,
    backgroundColor: colors.gray.dark,
    marginLeft: 8,
  },
  deleteButtonView: {flexDirection: 'row', alignItems: 'center'},
});
