import React from 'react';
import {
  FlatList,
  ViewStyle,
  StyleProp,
  StyleSheet,
  ImageStyle,
} from 'react-native';
import OfferCard from '../../Products/Components/OfferCard';

type OfferItem = {
  id: string;
  title?: string;
  subtitle?: string;
  image: string;
  facetValueIds?: string[];
};

type OfferListProps = {
  data: OfferItem[];
  overlayNames?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  onPress?: (item: OfferItem) => void;
  cardStyle?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
};

const OfferList: React.FC<OfferListProps> = ({
  data,
  containerStyle,
  onPress,
  overlayNames = false,
  imageStyle,
  cardStyle,
}) => {
  return (
    <FlatList
      data={data}
      keyExtractor={item => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[styles.flatListContent, containerStyle]}
      renderItem={({item}) => {
        return (
          <OfferCard
            imageStyle={imageStyle}
            cardStyle={cardStyle}
            overlayNames={overlayNames}
            category={item.title}
            subtitle={item.subtitle}
            image={item.image}
            onPress={() => onPress?.(item)}
          />
        );
      }}
    />
  );
};

export default OfferList;
export const styles = StyleSheet.create({
  flatListContent: {
    paddingVertical: 8,
  },
});
