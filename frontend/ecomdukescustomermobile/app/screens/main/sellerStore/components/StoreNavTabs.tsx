/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {Avatar} from 'react-native-paper';

interface StoreNavTabsProps {
  storeData: {
    banner: string;
    logo?: string;
    dp?: string;
    storeName?: string;
    description?: string;
    allowCategorisation?: boolean;
  };
  isLoading?: boolean;
  isError?: boolean;
  categories?: string[];
}

const StoreNavTabs = ({
  storeData,
  isLoading,
  isError,
  categories,
}: StoreNavTabsProps) => {
  if (isLoading) {
    return (
      <View style={{padding: 16}}>
        <Text>Loading store data...</Text>
      </View>
    );
  }

  if (isError || !storeData) {
    return (
      <View style={{padding: 16}}>
        <Text style={{color: 'red'}}>Error loading store data.</Text>
      </View>
    );
  }

  const bannerImageUrl =
    storeData.banner ||
    'https://getkuwa.com/cdn/shop/collections/Mama_Earth_fa698e64-340f-4feb-b363-29a311739041.png?v=1732790281';

  const logoImageUrl =
    storeData.logo ||
    'https://images.mamaearth.in/catalog/product/v/i/vit_c_glow_sunscreen1_white_bg_1.jpg?format=auto&width=300&height=300';

  return (
    <View>
      <Image source={{uri: bannerImageUrl}} style={styles.bannerImage} />
      <View style={styles.bannerOverlay}>
        <Text style={styles.storeName}>
          {storeData.storeName || 'Store Name Not Available'}
        </Text>
        <Text style={styles.storeDescription}>
          {storeData.description ||
            'Description not available. This brand finds and shares the energy of life from the pristine nature around the world.'}
        </Text>
      </View>

      <View style={styles.navContainer}>
        <View style={[styles.navContainer, styles.logoContainer]}>
          <View style={styles.logoSection}>
            <Image
              source={{uri: logoImageUrl}}
              style={styles.logoImage}
              // resizeMode="contain"
            />
            <Text style={styles.storeTitle}>
              {storeData.storeName || 'Nature Republic'}
            </Text>
          </View>
          <Image
            source={{
              uri:
                storeData.dp ||
                'https://cdn-icons-png.flaticon.com/512/149/149071.png',
            }}
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              resizeMode: 'contain',
              backgroundColor: '#eee',
            }}
          />
        </View>
        {storeData.allowCategorisation && categories && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabsContainer}>
            {categories.map(item => (
              <TouchableOpacity key={item} style={styles.tabItem}>
                <Text style={styles.tabText}>{item}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>
    </View>
  );
};

export default StoreNavTabs;
const styles = StyleSheet.create({
  bannerImage: {
    width: '100%',
    height: 200,
    resizeMode: 'contain',
  },
  bannerOverlay: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  storeName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0A0A5F',
    marginBottom: 6,
  },
  storeDescription: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  navContainer: {
    flexDirection: 'column',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
  },
  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    gap: 10,
  },
  logoImage: {
    width: 48,
    height: 48,
    resizeMode: 'contain',
    borderRadius: 20,
    backgroundColor: '#fff',
  },
  storeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0A0A5F',
  },
  tabsContainer: {
    flexDirection: 'row',
    gap: 12,
    paddingVertical: 8,
  },
  tabItem: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    backgroundColor: '#E0E0E0',
    borderRadius: 20,
  },
  tabText: {
    fontSize: 14,
    color: '#333',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
