import React, {useEffect, useMemo, useState} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>List,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {ActivityIndicator, Searchbar, Text} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import MyOrderCard from './components/MyOrderCard';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {
  useGetOrderLineQuery,
  useUpdateOrderItemStatusMutation,
} from '../../../redux/order/orderApiSlice';
import {OrderLineItem, OrderStatus} from '../../../redux/order/order';
import {useNavigation} from '@react-navigation/native';
import {MyOrderScreenNavigationProp} from '../../../navigations/types';
import {SCREEN_NAME} from '../../../constants/screenNames';
import Toast from 'react-native-toast-message';

const MyOrderScreen = () => {
  const navigation = useNavigation<MyOrderScreenNavigationProp>();

  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [updateOrderItemStatus] = useUpdateOrderItemStatusMutation();
  const [filteredOrders, setFilteredOrders] = useState<
    OrderLineItem[] | undefined
  >([]);
  const handleToggle = (index: number) => {
    setExpandedCard(prev => (prev === index ? null : index));
  };
  const handleOrderClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };
  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;

  const filter = {
    include: [
      {
        relation: 'order',
        required: true,
        scope: {
          where: {customerId},
          include: [
            {
              relation: 'promoCode',
            },
            {relation: 'shippingAddress'},
            {relation: 'billingAddress'},
          ],
        },
      },
      {
        relation: 'productVariant',
        required: true,
        scope: {
          where: {
            name: {ilike: `%${searchQuery}%`},
          },
          include: [{relation: 'product'}, {relation: 'featuredAsset'}],
        },
      },
      {
        relation: 'review',
      },
    ],
  };

  const {
    data: orderLineItems,
    isLoading: orderLoading,
    refetch,
  } = useGetOrderLineQuery({filter}, {skip: !customerId});
  console.log('ORDER LINE ITEMS', orderLineItems);

  useEffect(() => {
    if (orderLineItems) {
      const filtered = orderLineItems.filter(order =>
        order?.productVariant?.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()),
      );
      setFilteredOrders(filtered);
    }
  }, [searchQuery, orderLineItems]);

  const fullStepConfig = [
    {
      label: 'Order Placed',
      status: [OrderStatus.Pending, OrderStatus.Paid],
      message: 'Your order has been placed and is being processed.',
    },
    {
      label: 'Shipped',
      status: [OrderStatus.Picked],
      message: 'Your order has been picked and packed.',
    },
    {
      label: 'Out for Delivery',
      status: [OrderStatus.InTransit],
      message: 'Your order is on the way.',
    },
    {
      label: 'Delivered',
      status: [OrderStatus.Delivered],
      message: 'Your order has been delivered.',
    },
  ];

  const getActiveStepIndex = (status: OrderStatus): number => {
    return fullStepConfig.findIndex(step => step.status.includes(status));
  };
  const [selectedFilter, setSelectedFilter] = useState<
    'all' | 'inTransit' | 'delivered' | 'cancelledRefunded' | 'paid'
  >('all');

  const filteredByStatus = useMemo(() => {
    if (selectedFilter === 'all') return filteredOrders;
    if (selectedFilter === 'inTransit') {
      return filteredOrders?.filter(item =>
        [OrderStatus.InTransit, OrderStatus.Picked].includes(
          item?.status as OrderStatus,
        ),
      );
    }
    if (selectedFilter === 'delivered') {
      return filteredOrders?.filter(
        item => item?.status === OrderStatus.Delivered,
      );
    }
    if (selectedFilter === 'paid') {
      return filteredOrders?.filter(item => item?.status === OrderStatus.Paid);
    }
    if (selectedFilter === 'cancelledRefunded') {
      return filteredOrders?.filter(
        item =>
          item?.status === OrderStatus.Cancelled ||
          item?.status === OrderStatus.Refunded,
      );
    }
    return filteredOrders;
  }, [selectedFilter, filteredOrders]);
  console.log('FILTERED BY STATUS', filteredByStatus);

  useEffect(() => {
    if (orderLineItems) {
      const filtered = orderLineItems.filter(order =>
        order?.productVariant?.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()),
      );
      setFilteredOrders(filtered);
    }
  }, [searchQuery, orderLineItems]);

  const checkReturnWindow = (deliveredDate: any) => {
    const delivered = new Date(deliveredDate);
    const today = new Date();
    const diffDays =
      (today.getTime() - delivered.getTime()) / (1000 * 3600 * 24);

    return diffDays <= 7;
  };
  useEffect(() => {
    const filtered = orderLineItems?.filter(order =>
      order?.productVariant?.name
        .toLowerCase()
        .includes(searchQuery.toLowerCase()),
    );
    setFilteredOrders(filtered);
  }, [searchQuery, orderLineItems]);

  const handleReturnNavigation = (order: any) => {
    const isWithinReturnWindow = checkReturnWindow(order.deliveredDate);
    navigation.navigate(SCREEN_NAME.RETURN_REQUEST, {orderId: order.id});

    if (isWithinReturnWindow) {
      navigation.navigate(SCREEN_NAME.RETURN_REQUEST, {orderId: order.id});
    }
  };
  const handleCancelOrder = async (orderItemId: string) => {
    Alert.alert('Cancel Order', 'Are you sure you want to cancel this order?', [
      {text: 'No'},
      {
        text: 'Yes',
        onPress: async () => {
          try {
            await updateOrderItemStatus({
              orderItemId,
              newStatus: OrderStatus.Cancelled,
            }).unwrap();

            Toast.show({
              type: 'success',
              text1: 'Order cancelled successfully!',
            });

            setFilteredOrders(
              prev =>
                prev?.map(item =>
                  item.id === orderItemId
                    ? {
                        ...item,
                        order: {
                          ...(item.order || {}),
                          status: OrderStatus.Cancelled,
                        },
                      }
                    : item,
                ) as OrderLineItem[],
            );

            await refetch().then(res => {
              console.log('REFETCHED DATA', res.data);
            });
          } catch (error) {
            Toast.show({
              type: 'error',
              text1: 'Cancellation failed',
            });
          }
        },
      },
    ]);
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView contentContainerStyle={styles.container}>
        <Searchbar
          placeholder="Search Orders..."
          onChangeText={text => setSearchQuery(text)}
          onSubmitEditing={() => setSearchQuery(searchQuery)}
          onIconPress={() => setSearchQuery(searchQuery)}
          value={searchQuery}
          style={styles.searchbar}
          placeholderTextColor={colors.gray.medium}
        />

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.chipScrollContainer}>
          {['all', 'inTransit', 'paid', 'delivered', 'cancelledRefunded'].map(
            filter => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.chip,
                  selectedFilter === filter && styles.chipSelected,
                ]}
                onPress={() =>
                  setSelectedFilter(filter as typeof selectedFilter)
                }>
                <Text
                  style={[
                    styles.chipText,
                    selectedFilter === filter && styles.chipTextSelected,
                  ]}>
                  {filter === 'all'
                    ? 'All Orders'
                    : filter === 'inTransit'
                    ? 'In Transit'
                    : filter === 'paid'
                    ? 'Paid'
                    : filter === 'delivered'
                    ? 'Delivered'
                    : 'Cancelled / Refunded'}
                </Text>
              </TouchableOpacity>
            ),
          )}
        </ScrollView>
        {orderLoading && <ActivityIndicator />}

        <FlatList
          data={filteredByStatus}
          keyExtractor={(_, index) => index.toString()}
          renderItem={({index, item}) => {
            const activeStep = getActiveStepIndex(item.status as OrderStatus);
            const address =
              item.order?.shippingAddress || item.order?.billingAddress;

            const deliveryAddress = address
              ? `${address.name}\n${address.phoneNumber}\n${
                  address.addressLine1
                }${address.addressLine2 ? ', ' + address.addressLine2 : ''}\n${
                  address.locality
                }, ${address.city}\n${address.state}, ${address.country}\n${
                  address.zipCode
                }${address.landmark ? '\nLandmark: ' + address.landmark : ''}`
              : '';

            return (
              <MyOrderCard
                invoiceId={item.order?.invoiceId || ''}
                productImage={
                  item.productVariant?.featuredAsset?.previewUrl ?? ''
                }
                productName={item.productVariant?.name || 'Product Name'}
                deliveredOn={''}
                deliveryAddress={deliveryAddress || 'No address provided'}
                quantity={item.quantity || 1}
                price={item?.order?.totalAmount?.toString() || '0'}
                showDetails={expandedCard === index}
                onToggleDetails={() => handleToggle(index)}
                isDelivered={item?.status === OrderStatus.Delivered}
                status={item?.status as string}
                stepConfig={fullStepConfig}
                activeStep={activeStep}
                onPress={() => handleOrderClick(item.productVariantId)}
                isReviewed={true}
                handleReview={() => {}}
                reviewId={item?.review?.id || ''}
                productVariantId={item?.productVariant?.id || ''}
                orderLineItemId={item.id}
                customerId={customerId || ''}
                isReview={item.review?.id ? true : false}
                refetch={refetch}
                orderLineItems={orderLineItems!}
                onReturnPress={() => handleReturnNavigation(item.order)}
                onCancelOrder={() => handleCancelOrder(item.id)}
              />
            );
          }}
          // eslint-disable-next-line react/no-unstable-nested-components
          ItemSeparatorComponent={() => <View style={styles.cardItemStyle} />}
        />
      </ScrollView>
    </View>
  );
};
export default MyOrderScreen;
const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: customColors.white,
    padding: 15,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 15,
    padding: 16,
    marginBottom: 20,
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 6,
  },
  container: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: customColors.white,
  },
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: colors.gray.card,
    marginBottom: 20,
  },
  greetingCard: {
    backgroundColor: customColors.white,
    borderRadius: 10,
    marginBottom: 20,
    padding: 30,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardItemStyle: {height: 16},
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 10,
  },
  chip: {
    borderWidth: 1,
    borderColor: colors.gray.medium,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  chipSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  chipText: {
    color: colors.gray.dark,
  },
  chipTextSelected: {
    color: '#fff',
  },
  chipScrollContainer: {
    paddingVertical: 10,
    paddingHorizontal: 8,
  },
});
