import React, {useState} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Image,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {Icon, IconButton, Searchbar} from 'react-native-paper';
import {ChatNavigationProp} from '../../../navigations/types';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {useGetUserChatsQuery} from '../../../redux/chat/chatApiSlice';
import FullScreenLoader from '../../../components/FullScreenLoader';

dayjs.extend(relativeTime);

type ChatItem = {
  sellerId: string;
  name: string;
  avatarUrl: string | null;
  lastMessageAt: string;
};

type Props = {
  chats?: ChatItem[];
  onRefresh?: () => void;
};
dayjs.extend(relativeTime);
const ChatListScreen: React.FC<Props> = () => {
  const [debouncedSearch, setDebouncedSearch] = useState('');

  const {
    data: userChats = [],
    refetch,
    isLoading: userChatLoading,
  } = useGetUserChatsQuery({
    search: debouncedSearch,
  });
  console.log('userChats', userChats);

  const filteredChats = userChats.filter(chat => {
    const name = chat?.userData?.userTenant?.user?.firstName ?? '';
    return name.toLowerCase().includes(debouncedSearch.toLowerCase());
  });
  const isAnyLoading = userChatLoading;
  const navigation = useNavigation<ChatNavigationProp>();

  const renderChatItem = ({item}: any) => {
    const user = item?.userData?.userTenant?.user;
    const photoUrl = item?.userData?.presignedPhotoUrl;
    const sellerName = user
      ? [user.firstName, user.lastName].filter(Boolean).join(' ')
      : item?.name ?? 'Unknown';
    const initials = sellerName
      .split(' ')
      .map((n: any[]) => n[0])
      .join('')
      .toUpperCase();

    return (
      <TouchableOpacity
        style={styles.chatCard}
        onPress={() =>
          navigation.navigate(SCREEN_NAME.MESSAGE, {
            sellerId: item.sellerId ?? '',
            user: {
              ...user,
              photoUrl: photoUrl ?? null, // override with full presigned URL
            },
          })
        }>
        {photoUrl ? (
          <Image
            resizeMode="contain"
            source={{uri: photoUrl}}
            style={styles.profileImage} // Make sure this style gives circle shape
          />
        ) : (
          <View style={styles.initialsCircle}>
            <Text style={styles.initialsText}>{initials}</Text>
          </View>
        )}

        <View>
          <Text style={styles.chatName}>{sellerName}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.fullScreen}>
      <View style={styles.headerRow}>
        {' '}
        {isAnyLoading && <FullScreenLoader />}
        <IconButton
          icon="chevron-left"
          size={38}
          iconColor={colors.tertiary}
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        />
        <Text style={styles.headerText}>Messages</Text>
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{userChats.length}</Text>
        </View>
        <TouchableOpacity onPress={refetch}>
          <Icon source="refresh" size={20} color="#444" />
        </TouchableOpacity>
      </View>

      <ScrollView>
        <View style={styles.container}>
          <Searchbar
            placeholder="Search sellers..."
            onChangeText={text => setDebouncedSearch(text)}
            value={debouncedSearch}
            style={styles.searchbar}
            placeholderTextColor={colors.gray.medium}
          />

          <FlatList
            data={filteredChats}
            keyExtractor={item => item.sellerId}
            renderItem={renderChatItem}
            contentContainerStyle={styles.contentStyle}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ChatListScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: customColors.textBlack,
    flex: 1,
    marginLeft: 4,
  },
  badge: {
    backgroundColor: colors.tertiary,
    marginHorizontal: 8,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    padding: 10,
  },
  badgeText: {
    color: customColors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray.light,
    borderRadius: 26,
    paddingHorizontal: 14,
    paddingVertical: 6,
    marginBottom: 14,
  },

  chatCard: {
    backgroundColor: customColors.white,
    borderRadius: 18,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
  },
  initialsCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.tertiary,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: customColors.white,
  },
  chatName: {
    fontSize: 16,
    color: customColors.textBlack,
    fontWeight: 'bold',
  },
  chatTime: {
    fontSize: 13,
    color: customColors.textBlack,
    marginTop: 2,
  },
  searchbar: {
    marginHorizontal: 10,
    borderRadius: 25,
    marginTop: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
    height: 50,
  },
  fullScreen: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  contentStyle: {paddingBottom: 20},
  backButton: {
    margin: 0,
    padding: 0,
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#ccc',
    marginRight: 10,
  },
});
