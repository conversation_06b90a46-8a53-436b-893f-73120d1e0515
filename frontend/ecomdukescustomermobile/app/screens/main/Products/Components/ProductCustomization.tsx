import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import CustomButton from '../../../../components/CustomButton/CustomButton';
import CustomTextInput from '../../../../components/InputFields/CustomTextInput';
import {Menu, RadioButton, TextInput} from 'react-native-paper';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
import {launchImageLibrary} from 'react-native-image-picker';

export interface CustomizationOption {
  id: string;
  label: string;
  value: string;
}

export interface CustomizationField {
  id: string;
  name: string;
  label: string;
  placeholder: string;
  fieldType:
    | 'text'
    | 'number'
    | 'textarea'
    | 'dropdown'
    | 'checkbox'
    | 'radio'
    | 'file';
  isRequired: boolean;
  productCustomizationOptions?: CustomizationOption[];
}

interface Props {
  fields: CustomizationField[];
  onSubmit: (values: any) => void;
  onChange?: (values: any) => void;
  prefillValues?: {customizationFieldId: string; value: string}[];
  submitButtonTitle?: string;
  submitButtonMode?: 'default' | 'step'; // optional if you want dynamic behavior inside
  onSecondSubmit?: () => void; // only for 'step' mode
}

const ProductCustomizationForm: React.FC<Props> = ({
  fields,
  submitButtonMode,
  submitButtonTitle,
  onSecondSubmit,
  onSubmit,
  onChange,
  prefillValues = [],
}) => {
  const [menuVisibleMap, setMenuVisibleMap] = useState<Record<string, boolean>>(
    {},
  );

  const openMenu = (fieldId: string) =>
    setMenuVisibleMap(prev => ({...prev, [fieldId]: true}));

  const closeMenu = (fieldId: string) =>
    setMenuVisibleMap(prev => ({...prev, [fieldId]: false}));

  const getInitialValues = (
    // eslint-disable-next-line @typescript-eslint/no-shadow
    fields: CustomizationField[],
    // eslint-disable-next-line @typescript-eslint/no-shadow
    prefillValues: {customizationFieldId: string; value: string}[],
  ) => {
    const prefillMap = prefillValues.reduce((acc, item) => {
      acc[item.customizationFieldId] = item.value;
      return acc;
    }, {} as Record<string, string>);

    return fields.reduce((acc, field) => {
      if (field.fieldType === 'file') {
        const raw = prefillMap[field.id];
        try {
          const parsed = raw ? JSON.parse(raw) : [];
          acc[field.name] = parsed.map((file: any) => ({
            ...file,
            uri: file.uri || file.url || null,
          }));
        } catch (e) {
          acc[field.name] = [];
        }
      } else {
        acc[field.name] = prefillMap[field.id] || '';
      }

      return acc;
    }, {} as Record<string, any>);
  };

  const initialValues = getInitialValues(fields, prefillValues);
  const validationSchema = Yup.object(
    fields.reduce((acc, field) => {
      if (field.isRequired) {
        acc[field.name] = Yup.string().required(`${field.label} is required`);
      }
      return acc;
    }, {} as Record<string, Yup.StringSchema>),
  );
  const handleUpload = async (fieldName: string) => {
    const result = await launchImageLibrary({
      mediaType: 'photo',
      selectionLimit: 3,
    });

    if (!result.didCancel && result.assets) {
      const validFiles = result.assets.filter(asset => {
        if (asset.fileSize && asset.fileSize > 1 * 1024 * 1024) {
          Alert.alert(
            'File too large',
            `${
              asset.fileName || 'This file'
            } is larger than 1MB. Please select a smaller image.`,
          );
          return false;
        }
        return true;
      });

      const newFiles = validFiles.map(asset => ({
        uri: asset.uri!,
        name: asset.fileName,
        type: asset.type,
      }));

      const existing = formik.values[fieldName] || [];
      formik.setFieldValue(fieldName, [...existing, ...newFiles]);
    }
  };

  const handleRemove = (fieldName: string, index: number) => {
    const updated = [...formik.values[fieldName]];
    updated.splice(index, 1);
    formik.setFieldValue(fieldName, updated);
  };

  const renderField = (
    field: CustomizationField,
    handleChange: any,
    values: any,
    errors: any,
    touched: any,
    setFieldTouched: any,
  ) => {
    const error = touched[field.name] && errors[field.name];
    switch (field.fieldType) {
      case 'text':
      case 'number':
        return (
          <CustomTextInput
            key={field.id}
            placeholder={field.placeholder}
            keyboardType={field.fieldType === 'number' ? 'numeric' : 'default'}
            value={values[field.name]}
            onChangeText={handleChange(field.name)}
            onBlur={() => setFieldTouched(field.name)}
            title={''}
            style={styles.numberInput}
            error={!!error}
          />
        );
      case 'textarea':
        return (
          <CustomTextInput
            key={field.id}
            placeholder={field.placeholder}
            multiline
            numberOfLines={4}
            value={values[field.name]}
            onChangeText={handleChange(field.name)}
            onBlur={() => setFieldTouched(field.name)}
            title={''}
            error={!!error}
          />
        );
      case 'dropdown': {
        const isVisible = !!menuVisibleMap[field.id];
        return (
          <View key={field.id} style={styles.input}>
            <Menu
              visible={isVisible}
              onDismiss={() => closeMenu(field.id)}
              contentStyle={styles.menuContent}
              anchor={
                <CustomTextInput
                  onPress={() => openMenu(field.id)}
                  mode="outlined"
                  placeholder={field.placeholder || 'Choose an option'}
                  value={values[field.name]}
                  editable={false}
                  right={
                    <TextInput.Icon
                      icon="chevron-down"
                      forceTextInputFocus={false}
                      onPress={() => openMenu(field.id)}
                    />
                  }
                  error={!!error}
                  title={''}
                />
              }>
              {field.productCustomizationOptions?.map(option => (
                <Menu.Item
                  key={option.id}
                  onPress={() => {
                    handleChange(field.name)(option.value);
                    closeMenu(field.id);
                  }}
                  title={option.label}
                />
              ))}
            </Menu>
          </View>
        );
      }
      case 'radio':
        return (
          <RadioButton.Group
            key={field.id}
            onValueChange={handleChange(field.name)}
            value={values[field.name]}>
            {(field.productCustomizationOptions || []).map(option => (
              <RadioButton.Item
                key={option.id}
                label={option.label}
                value={option.value}
                mode="android"
                position="trailing"
              />
            ))}
          </RadioButton.Group>
        );
      case 'file': {
        return (
          <View key={field.id}>
            <View style={styles.uploadSection}>
              <TouchableOpacity
                style={styles.uploadButtonContainer}
                onPress={() => handleUpload(field.name)}>
                <Text style={{color: colors.tertiary}}>Upload image</Text>
              </TouchableOpacity>

              {formik.values[field.name]?.length > 0 && (
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={styles.imageselector}>
                  {formik.values[field.name]?.length} image(s) selected
                </Text>
              )}
            </View>

            <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 12}}>
              {formik.values[field.name]?.map((file: any, index: number) => {
                let imageUri = null;

                if (typeof file === 'string') {
                  imageUri = file;
                } else if (typeof file === 'object') {
                  imageUri = file?.uri || file?.url || null;
                }

                if (!imageUri) return null;
                return (
                  <View
                    key={file.url || file.uri}
                    style={{position: 'relative', width: 70, height: 70}}>
                    <Image
                      source={{uri: imageUri}}
                      style={{width: 70, height: 70, borderRadius: 8}}
                      resizeMode="cover"
                    />
                    <TouchableOpacity
                      onPress={() => handleRemove(field.name, index)}
                      style={styles.previewWrapper}>
                      <Text style={{color: 'white', fontWeight: 'bold'}}>
                        ×
                      </Text>
                    </TouchableOpacity>
                  </View>
                );
              })}
            </View>
          </View>
        );
      }

      default:
        return null;
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit,
  });
  useEffect(() => {
    if (onChange) {
      onChange(formik.values);
    }
  }, [formik.values, onChange]);

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {fields.map(field => (
        <View key={field.id} style={styles.field}>
          <Text style={styles.label}>{field.label}</Text>
          {renderField(
            field,
            formik.handleChange,
            formik.values,
            formik.errors,
            formik.touched,
            formik.setFieldTouched,
          )}
        </View>
      ))}
      <CustomButton
        title={submitButtonTitle || 'SAVE'}
        onPress={() => {
          if (submitButtonMode === 'step') {
            if (submitButtonTitle === 'GO TO CART') {
              onSecondSubmit?.();
            } else {
              formik.handleSubmit();
            }
          } else {
            formik.handleSubmit();
          }
        }}
      />{' '}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  field: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 4,
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
  input: {
    borderRadius: 8,
    padding: 0,
    textAlignVertical: 'top',
    height: 4 * 24,
  },
  numberInput: {
    borderRadius: 8,
    padding: 0,
    textAlignVertical: 'top',
    height: 3 * 24,
  },
  menuContent: {
    backgroundColor: customColors.white,
    borderRadius: 0,
  },
  uploadAttachments: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
    color: '#1A0D66',
  },
  uploadButtonContainer: {
    borderWidth: 1,
    borderColor: colors.tertiary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: 'white',
  },
  imageselector: {
    marginLeft: 10,
    color: '#999',
    flex: 1,
  },
  previewWrapper: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: '#555',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
    elevation: 2,
  },
  uploadSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F4F5F7',
    padding: 8,
    borderRadius: 12,
    marginBottom: 12,
  },
});

export default ProductCustomizationForm;
