import React from 'react';
import {View, Text, Image, StyleSheet, TouchableOpacity} from 'react-native';
import {Icon, IconButton} from 'react-native-paper';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';
import CustomButton from '../../../../components/CustomButton/CustomButton';
import {useTypedSelector} from '../../../../redux/store';

export type ProductCardProps = {
  id: number | string;
  image: string;
  name: string;
  shortDescription?: string;
  price: string;
  originalPrice?: string;
  discount?: string;
  rating?: number;
  ratingCount?: number;
  onPress?: () => void;
  onAddToCart?: () => void;
  onGoToCart?: () => void;
  onWishlistToggle?: () => void;
  isWishlisted?: boolean;
  onEdit?: () => void;
  onOpenMoreImages?: () => void;
  isAddedToCart?: boolean;
  isloading: boolean;
};

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  image,
  name,
  shortDescription,
  price,
  originalPrice,
  discount,
  rating,
  ratingCount,
  onPress,
  onAddToCart,
  onGoToCart,
  onWishlistToggle,
  isWishlisted,
  onOpenMoreImages,
  isloading,
}) => {
  const currencyCodeSymbolMap: Map<string, string> = new Map([
    ['INR', '₹'],
    ['USD', '$'],
  ]);
  const cartList = useTypedSelector(state => state.cart.cartList);
  const isProductInCart = cartList.some(item => item.productVariantId === id);

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.imageContainer}>
        <Image
          source={typeof image === 'string' ? {uri: image} : image}
          style={styles.image}
          resizeMode="cover"
        />

        <IconButton
          icon={'heart'}
          iconColor={isWishlisted ? colors.favourites : colors.gray.medium}
          size={15}
          style={styles.wishlistIcon}
          onPress={onWishlistToggle}
        />

        <IconButton
          icon="image-multiple"
          size={15}
          iconColor={colors.tertiary}
          style={styles.moreImagesIcon}
          onPress={onOpenMoreImages}
        />
      </View>

      <View style={styles.descriptionContainer}>
        <View style={styles.leftDescription}>
          <View>
            <Text
              numberOfLines={2}
              ellipsizeMode="tail"
              style={styles.productName}>
              {name}
            </Text>

            {shortDescription ? (
              <Text numberOfLines={1} style={styles.shortDescription}>
                {shortDescription}
              </Text>
            ) : null}

            {rating !== undefined && (
              <View style={styles.ratingContainer}>
                {Array.from({length: 5}).map((_, i) => (
                  <Icon
                    key={i}
                    source={
                      i < Math.floor(rating)
                        ? 'star'
                        : i < rating
                        ? 'star-half-full'
                        : 'star-outline'
                    }
                    size={13}
                    color={colors.tertiary}
                  />
                ))}
                <Text style={styles.ratingCount}> ({ratingCount ?? 0})</Text>
              </View>
            )}
          </View>

          <CustomButton
            mode="outlined"
            compact
            onPress={isProductInCart ? onGoToCart : onAddToCart}
            disabled={isloading}
            title={isProductInCart ? 'Go to cart' : 'Add to cart'}
            textColor={
              isProductInCart ? customColors.white : customColors.textBlack
            }
            style={[
              styles.veryTinyCart,
              {
                backgroundColor: isProductInCart
                  ? customColors.primaryContainer
                  : customColors.white,
                borderColor: isProductInCart
                  ? customColors.white
                  : colors.gray.medium,
              },
            ]}
            labelStyle={styles.veryTinyCartText}
          />
        </View>

        <View style={styles.rightDescription}>
          {discount && <Text style={styles.dealTag}> {discount} Off</Text>}

          <>
            {originalPrice && (
              <>
                <Text style={styles.originalPrice}>
                  {`${currencyCodeSymbolMap.get('INR') || ''}${originalPrice}`}
                </Text>
                <Text style={styles.price}>
                  {' '}
                  {`${currencyCodeSymbolMap.get('INR') || ''}${price}`}
                </Text>
              </>
            )}
            {`${currencyCodeSymbolMap.get('INR') || ''}${price}`}
          </>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '48%',
    backgroundColor: customColors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.gray.light,
    overflow: 'hidden',
    marginBottom: 3,
    elevation: 2,
    minHeight: 310,
    minWidth: 180,
  },
  imageContainer: {
    position: 'relative',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '90%',
    height: '90%',
    borderRadius: 10,
    objectFit: 'contain',
  },
  wishlistIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
    borderWidth: 1,
    borderColor: colors.gray.light,
    backgroundColor: customColors.white,
    borderRadius: 20,
  },
  moreImagesIcon: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: customColors.white,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.gray.light,
  },
  descriptionContainer: {
    flexDirection: 'row',
    padding: 8,
    gap: 8,
    marginLeft: 4,
    marginRight: 4,
  },
  leftDescription: {
    flex: 1,
    justifyContent: 'space-between',
  },
  rightDescription: {
    alignItems: 'flex-end',
    gap: 4,
  },
  productName: {
    fontSize: 12,
    fontWeight: '700',
    color: customColors.textBlack,
  },
  shortDescription: {
    fontSize: 10,
    color: colors.gray.medium,
    marginTop: 2,
  },

  ratingCount: {
    color: colors.gray.dark,
    fontSize: 12,
  },
  veryTinyCart: {
    marginTop: 4,
    borderRadius: 20,
    minWidth: 100,
  },
  veryTinyCartText: {
    fontSize: 12,
    fontWeight: '600',
  },
  editIcon: {
    alignSelf: 'flex-end',
    marginBottom: 4,
    backgroundColor: colors.gray.light,
  },
  dealTag: {
    fontSize: 11,
    color: colors.green.success,
    fontWeight: 'bold',
    marginBottom: 2,
    textAlign: 'right',
  },
  originalPrice: {
    fontSize: 10,
    textDecorationLine: 'line-through',
    color: colors.gray.dark,
    marginBottom: 2,
    textAlign: 'right',
  },
  price: {
    fontSize: 12,
    fontWeight: 'bold',
    color: customColors.textBlack,
    textAlign: 'right',
  },
  ratingContainer: {
    flexDirection: 'row',
    marginBottom: 2,
    marginTop: 5,
  },
});

export default ProductCard;
