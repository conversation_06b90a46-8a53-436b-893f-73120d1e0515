import React from 'react';
import {Image, StyleSheet} from 'react-native';
import {AuthStackParamList} from './types';
import {Images} from '../assets/images';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import LoginScreen from '../screens/auth/Login/LoginScreen';
import RegisterScreen from '../screens/auth/Register/RegisterScreen';
import {SCREEN_NAME} from '../constants/screenNames';
import ForgotPasswordScreen from '../screens/auth/ForgotPassword/ForgotPasswordScreen';
import CheckMailScreen from '../screens/auth/CheckMail/CheckMail';

const Stack = createNativeStackNavigator<AuthStackParamList>();

const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: true,
        headerBackVisible: true,
        headerTintColor: '#5847F9',
        headerBackTitle: '',
        headerBackButtonDisplayMode: 'minimal',
        // eslint-disable-next-line react/no-unstable-nested-components
        headerTitle: () => (
          <Image source={Images.newLogoWithTag} style={styles.image} />
        ),
        headerTitleAlign: 'center',
      }}
      initialRouteName={SCREEN_NAME.LOGIN}>
      <Stack.Screen name={SCREEN_NAME.LOGIN} component={LoginScreen} />
      <Stack.Screen name={SCREEN_NAME.REGISTER} component={RegisterScreen} />

      <Stack.Screen
        name={SCREEN_NAME.FORGOT_PASSWORD}
        component={ForgotPasswordScreen}
      />
      <Stack.Screen name={SCREEN_NAME.CHECK_MAIL} component={CheckMailScreen} />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
const styles = StyleSheet.create({
  image: {width: 120, height: 60},
});
