import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {SCREEN_NAME} from '../constants/screenNames';
import customColors from '../theme/customColors';
import HomeScreen from '../screens/main/home/<USER>';
import OrderConfirmation from '../screens/main/cart/OrderConfirmation';
import WishlistScreen from '../screens/main/wishlist/WishlistScreen';
import SidebarMenu from '../screens/sidebar/SidebarMenu';
import {colors} from '../theme/colors';
import CategoryScreen from '../screens/main/category/CategoryScreen';
import {Image} from 'react-native';
import {Images} from '../assets/images';

const Tab = createBottomTabNavigator();

const BottomTabNavigation = () => {
  return (
    <Tab.Navigator
      initialRouteName={SCREEN_NAME.HOME}
      screenOptions={({route}) => ({
        headerShown: false,
        headerBackButtonDisplayMode: 'minimal',
        // eslint-disable-next-line react/no-unstable-nested-components
        tabBarIcon: ({focused}) => {
          let imageSource;

          if (route.name === SCREEN_NAME.HOME) {
            imageSource = focused ? Images.home : Images.home;
          } else if (route.name === SCREEN_NAME.WISHLIST) {
            imageSource = focused ? Images.heart : Images.heart;
          } else if (route.name === SCREEN_NAME.CATEGORY) {
            imageSource = focused ? Images.category : Images.category;
          } else if (route.name === SCREEN_NAME.SIDE_BAR) {
            imageSource = focused ? Images.profile : Images.profile;
          } else if (route.name === SCREEN_NAME.CART) {
            imageSource = focused ? Images.cart : Images.cart;
          }

          return (
            <Image
              source={imageSource}
              // eslint-disable-next-line react-native/no-inline-styles
              style={{
                width: 20,
                height: 20,
                tintColor: focused ? undefined : colors.tertiary,
              }}
              resizeMode="contain"
            />
          );
        },

        tabBarActiveTintColor: colors.tertiary,
        tabBarActiveBadgeStyle: {fontWeight: 'ultralight'},
        tabBarInActiveBadgeStyle: {fontWeight: 'ultralight'},

        // tabBarActiveTintColor: colors.tertiaryBlue,

        tabBarInactiveTintColor: colors.gray.dark,
      })}>
      <Tab.Screen
        options={{title: 'Home'}}
        name={SCREEN_NAME.HOME}
        component={HomeScreen}
      />
      <Tab.Screen
        options={{title: 'Wishlist'}}
        name={SCREEN_NAME.WISHLIST}
        component={WishlistScreen}
      />
      <Tab.Screen
        name={SCREEN_NAME.CATEGORY}
        options={{title: 'Categories'}}
        component={CategoryScreen}
      />
      <Tab.Screen
        options={{title: 'Profile'}}
        name={SCREEN_NAME.SIDE_BAR}
        component={SidebarMenu}
      />
      <Tab.Screen
        options={{title: 'Cart'}}
        name={SCREEN_NAME.CART}
        component={OrderConfirmation}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigation;
