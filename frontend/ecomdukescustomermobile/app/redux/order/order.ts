import {PromoCode} from '../../types/cartApi';
import {Customer} from '../../types/customer';
import {AddressDto} from '../../types/customerApi';
import {ProductVariant} from '../product/product';

export interface Order {
  id: string;
  customerId: string;
  orderId?: string;
  orderReferenceId?: string;
  totalAmount: number;
  currency: string;
  status: OrderStatus;
  cartId: string;
  orderLineItems: OrderLineItem[];
  promoCodeId: string;
  promoCode: PromoCode;
  discount?: string;
  shippingAddressId: string;
  billingAddressId: string;
  shippingAddress: AddressDto;
  billingAddress: AddressDto;
  createdOn: string;
  customer?: Customer;
  invoiceId?: string;
  //   orderStatus: OrderStatus;
}
export interface OrderLineItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  orderId: string;
  productVariantId: string;
  warehouseId: string;
  productVariant?: ProductVariant;
  order?: Order;
  status: OrderStatus;
  review?: Review;
}
export interface Review {
  id?: string;
  rating: number;
  review?: string;
  reviewAssets?: string[];
  customerId: string;
  productVariantId: string;
  orderLineItemId: string;
  createdOn: string;
}
export enum OrderStatus {
  Pending = 'pending', // Order is placed but not yet processed
  Paid = 'paid', // Payment has been completed
  PaymentFailed = 'payment_failed', // Payment failed during transaction
  Failed = 'failed', // Generic failure (if needed for any other issue)
  Picked = 'picked', // Items have been picked by logistics
  InTransit = 'in_transit', // Order is in transit with the logistics provider
  Delivered = 'delivered', // Order has been delivered to the customer
  DeliveryFailed = 'delivery_failed', // Delivery failed (e.g., customer not available)
  Cancelled = 'cancelled', // Order was cancelled before shipment
  Returned = 'returned', // Order was returned by the customer
  Refunded = 'refunded', // Order was refunded
  RefundInitiated = 'refund_initiated', // Refund process has been started
  RefundFailed = 'refund_failed', // Refund attempt failed (e.g., gateway error)
}
