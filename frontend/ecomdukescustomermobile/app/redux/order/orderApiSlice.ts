import {ApiSliceIdentifier} from '../../constants/enums';
import {buildFilterParams} from '../../types/cartApi';
import {IFilter} from '../../Types/filter';
import {apiSlice} from '../apiSlice';
import {Order, OrderLineItem, OrderStatus} from './order';

export const orderApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getOrderById: builder.query<Order, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/orders/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getOrder: builder.query<Order[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/orders',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getOrderLine: builder.query<OrderLineItem[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/order-line-items',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    downloadInvoicePdf: builder.query<Blob, string>({
      query: invoiceId => ({
        url: `/invoices/${invoiceId}/download`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        responseHandler: async (response: Response) => {
          return await response.blob();
        },
      }),
    }),
    updateOrderItemStatus: builder.mutation<
      void,
      {orderItemId: string; newStatus: OrderStatus}
    >({
      query: ({orderItemId, newStatus}) => ({
        url: `/order-line-items/${orderItemId}/status/${newStatus}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useGetOrderByIdQuery,
  useGetOrderQuery,
  useGetOrderLineQuery,
  useDownloadInvoicePdfQuery,
  useLazyDownloadInvoicePdfQuery,
  useUpdateOrderItemStatusMutation,
} = orderApiSlice;
