import {ApiSliceIdentifier} from '../../constants/enums';
import {ChangePasswordRequest} from '../../types/auth';
import {apiSlice} from '../apiSlice';
import {getItem} from '../mmkvStorage';
import {
  ILoginForm,
  ISignupForm,
  ITokenExchangeRequest,
  ITokenResponse,
} from './types';
import {User} from './user.model';

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    login: builder.mutation({
      query: (body: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {
          ...body,
          client_id: 'email_password_client',
          client_secret:
            '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    exchangeToken: builder.mutation<ITokenResponse, ITokenExchangeRequest>({
      query: body => ({
        url: '/auth/token',
        method: 'POST',
        body: {
          clientId: 'email_password_client',
          code: body.code,
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        headers: {
          Authorization: `Bearer ${body.code}`,
        },
      }),
    }),
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/auth/logout',
        method: 'POST',
        body: {refreshToken},
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),

    signUp: builder.mutation({
      query: ({
        token,
        signupData,
      }: {
        token: string;
        signupData: ISignupForm;
      }) => ({
        url: '/auth/sign-up/create-user',
        method: 'POST',
        body: {
          firstName: signupData.firstName,
          lastName: signupData.lastName,
          password: signupData.password,
          email: signupData.email,
          phoneNumber: signupData.phoneNumber,
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    createSignUpToken: builder.mutation({
      query: (email: string) => ({
        url: '/auth/sign-up/create-token',
        method: 'POST',
        cache: 'no-cache',
        body: {
          email,
          data: {
            client_id: 'email_password_client',
            client_secret:
              '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
          },
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    getUser: builder.query<User, void>({
      query: () => ({
        url: '/auth/me',
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    updateUser: builder.mutation<void, {userId: string; body: FormData}>({
      query: ({userId, body}) => ({
        url: `/profile/${userId}`,
        method: 'PATCH',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    forgotPassword: builder.mutation({
      query: (email: string) => ({
        url: '/auth/forget-password',
        method: 'POST',
        body: {
          username: email,
          client_id: 'email_password_client',
          client_secret:
            '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    googleAuthCallback: builder.mutation<ITokenResponse, {code: string}>({
      query: ({code}) => ({
        url: '/auth/google/callback',
        method: 'POST',
        params: {
          state: 'client_id=google_seller_client',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        headers: {
          Authorization: `Bearer ${code}`,
        },
      }),
    }),
    createGuestToken: builder.mutation<ITokenResponse, void>({
      query: () => ({
        url: '/auth/guest',
        method: 'POST',

        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    updatePassword: builder.mutation<
      void,
      Omit<ChangePasswordRequest, 'refreshToken'>
    >({
      query: body => {
        const refreshToken = getItem('refreshToken');
        return {
          url: '/auth/change-password',
          method: 'PATCH',
          body: {
            ...body,
            refreshToken,
          },
          apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        };
      },
    }),
    createReferralCode: builder.mutation<
      {referralCode: string},
      {referrerId: string}
    >({
      query: ({referrerId}) => ({
        url: '/referrals',
        method: 'POST',
        body: {referrerId},
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    updateCustomerStatus: builder.mutation<
      void,
      {
        id: string;
        status: string;
        verificationCode?: string;
        rejectionReason?: string;
      }
    >({
      query: ({id, status, verificationCode, rejectionReason}) => ({
        url: `/customers/${id}/status`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {
          status,
          verificationCode,
          rejectionReason,
        },
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useExchangeTokenMutation,
  useCreateSignUpTokenMutation,
  useLogoutMutation,
  useSignUpMutation,
  useGetUserQuery,
  useLazyGetUserQuery,
  useForgotPasswordMutation,
  useGoogleAuthCallbackMutation,
  useCreateGuestTokenMutation,
  useUpdateUserMutation,
  useUpdatePasswordMutation,
  useCreateReferralCodeMutation,
  useUpdateCustomerStatusMutation,
} = authApiSlice;
