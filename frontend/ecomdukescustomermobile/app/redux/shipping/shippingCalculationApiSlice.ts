import {apiSlice} from '../apiSlice';
import {ApiSliceIdentifier} from '../../constants/enums';
import {
  PincodeShippingCalculationRequestDto,
  PincodeShippingCalculationResponseDto,
  Shipping,
} from '../../types/shipping';

type BulkShippingCalculationRequestDto = {
  cartItemIds: string[];
  shippingAddressId: string;
};

export const shippingApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    calculateBulkShipping: builder.mutation<
      Shipping[],
      BulkShippingCalculationRequestDto
    >({
      query: request => ({
        url: '/shipping-calculations/calculate-bulk',
        method: 'POST',
        body: request,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    calculateShippingByPincode: builder.mutation<
      PincodeShippingCalculationResponseDto,
      PincodeShippingCalculationRequestDto
    >({
      query: request => ({
        url: '/shipping-calculations/calculate-by-pincode',
        method: 'POST',
        body: request,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useCalculateBulkShippingMutation,
  useCalculateShippingByPincodeMutation,
} = shippingApiSlice;
