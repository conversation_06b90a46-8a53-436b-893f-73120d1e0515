import {ApiSliceIdentifier} from '../../constants/enums';
import {buildFilterParams, IFilter} from '../../types/api';
import {apiSlice} from '../apiSlice';
export interface Configuration {
  id: string;
  key: string;
  label: string;
  value: string;
  created_on?: string;
  modified_on?: string;
  created_by?: string;
  modified_by?: string;
}

export const configurationApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getConfigurations: builder.query<Configuration[], IFilter | void>({
      query: filter => ({
        url: '/configurations',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined),
        },
      }),
    }),
  }),
});
export const {useGetConfigurationsQuery} = configurationApi;
