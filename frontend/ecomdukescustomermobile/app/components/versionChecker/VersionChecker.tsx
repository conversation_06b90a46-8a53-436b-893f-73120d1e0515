import React, {useCallback, useEffect, useState} from 'react';
import {Linking, Platform} from 'react-native';
import UpdateModal from '../UpdateModal/UpdateModal';
import DeviceInfo from 'react-native-device-info';
import {useGetConfigurationsQuery} from '../../redux/configure/configureApiSlice';

const VersionChecker = () => {
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [isMandatoryUpdate, setIsMandatoryUpdate] = useState(false);
  const [updateUrl, setUpdateUrl] = useState('');
  const {data: configData} = useGetConfigurationsQuery();

  const currentVersion = DeviceInfo.getVersion();
  const getConfigValue = (key: string): string | undefined =>
    configData?.find(item => item.key === key)?.value;
  const platform = Platform.OS;

  const compareVersions = (v1: string, v2: string) => {
    const v1Parts = v1.split('.').map(Number);
    const v2Parts = v2.split('.').map(Number);

    const length = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < length; i++) {
      const part1 = v1Parts[i] || 0;
      const part2 = v2Parts[i] || 0;

      if (part1 < part2) return -1;
      if (part1 > part2) return 1;
    }

    return 0;
  };

  const checkVersion = useCallback(() => {
    const latest = getConfigValue(`${platform}.customerversion`); // e.g., "2.0.0"
    const forceUpdate =
      getConfigValue(`${platform}.customerforce_update`) === 'true';
    const url =
      platform === 'android'
        ? 'https://play.google.com/store/apps/details?id=com.yourapp'
        : 'https://apps.apple.com/app/idYOUR_APP_ID';

    if (!latest || !currentVersion) return;

    const versionCheck = compareVersions(currentVersion, latest);
    if (versionCheck < 0) {
      setIsMandatoryUpdate(forceUpdate);
      setUpdateUrl(url);
      setShowUpdateModal(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configData, platform, currentVersion]);

  useEffect(() => {
    if (configData) {
      checkVersion();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configData]);

  const handleUpdateNow = async () => {
    if (updateUrl) {
      Linking.openURL(updateUrl); // Open the update URL (Play Store / App Store)
    }
  };

  const handleUpdateLater = () => {
    setShowUpdateModal(false); // Close the modal when "Update Later" is pressed
  };

  return (
    <>
      {showUpdateModal && (
        <UpdateModal
          visible={showUpdateModal}
          mandatory={isMandatoryUpdate}
          onUpdateNow={handleUpdateNow}
          onUpdateLater={handleUpdateLater}
        />
      )}
    </>
  );
};

export default VersionChecker;
