import React from 'react';
import {StyleSheet} from 'react-native';
import {Modal, Portal, Text} from 'react-native-paper';
import CustomButton from '../CustomButton/CustomButton';

interface UpdateModalProps {
  visible: boolean;
  mandatory: boolean;
  onUpdateNow: () => void;
  onUpdateLater?: () => void;
}

const UpdateModal: React.FC<UpdateModalProps> = ({
  visible,
  mandatory,
  onUpdateNow,
  onUpdateLater,
}) => {
  return (
    <Portal>
      <Modal
        visible={visible}
        dismissable={!mandatory}
        onDismiss={mandatory ? undefined : onUpdateLater}
        contentContainerStyle={styles.container}>
        <Text style={styles.title}>
          {mandatory ? 'Update Required' : 'New Update Available'}
        </Text>
        <Text style={styles.message}>
          {mandatory
            ? 'Please update the app to continue using it.'
            : 'A newer version of the app is available. Would you like to update now?'}
        </Text>

        <CustomButton
          mode="contained"
          onPress={onUpdateNow}
          style={styles.button}
          title="Update Now"
        />
        {!mandatory && onUpdateLater && (
          <CustomButton
            mode="outlined"
            onPress={onUpdateLater}
            style={styles.button}
            title="Update Later"
          />
        )}
      </Modal>
    </Portal>
  );
};

export default UpdateModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    marginBottom: 24,
  },
  button: {
    marginVertical: 6,
  },
});
