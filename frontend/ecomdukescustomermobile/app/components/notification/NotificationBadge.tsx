import React, {useCallback, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {Text} from 'react-native-paper';
import customColors from '../../theme/customColors';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {OnboardStackParamList} from '../../navigations/types';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {SCREEN_NAME} from '../../constants/screenNames';
import {useGetUserNotificationsCountQuery} from '../../redux/notification/userNotificationApiSlice';
import {useGetUserQuery} from '../../redux/auth/authApiSlice';
import {Images} from '../../assets/images';
import {colors} from '../../theme/colors';
type props = {
  chatCount?: number;
  notificationCount?: number;
  coinCount?: number;
};
const NotificationBadge: React.FC<props> = ({
  chatCount = 0,
  notificationCount = 0,
  coinCount = 0,
}) => {
  const navigation =
    useNavigation<NativeStackNavigationProp<OnboardStackParamList>>();
  const [selectedTab, setSelectedTab] = useState<
    'chat' | 'bell' | 'coin' | null
  >(null);
  const {data: user} = useGetUserQuery();
  const userTenantId = user?.userTenantId;

  const {data: unreadCountData, refetch: refetchUnreadCount} =
    useGetUserNotificationsCountQuery(
      {userTenantId: userTenantId!, isRead: false},
      {
        skip: !userTenantId,
      },
    );
  useFocusEffect(
    useCallback(() => {
      if (userTenantId) {
        refetchUnreadCount();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userTenantId]),
  );

  const unreadCount = unreadCountData?.count || 0;
  const renderBadge = (count: number) => {
    if (count > 0) {
      return (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{count > 99 ? '99+' : count}</Text>
        </View>
      );
    }
    return null;
  };
  useFocusEffect(
    useCallback(() => {
      if (userTenantId) {
        refetchUnreadCount();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userTenantId]),
  );

  return (
    <View style={styles.container}>
      <View style={styles.iconRow}>
        <TouchableOpacity
          onPress={() => {
            setSelectedTab('bell');
            navigation.navigate(SCREEN_NAME.NOTIFICATION);
          }}
          style={[
            styles.iconButton,
            selectedTab === 'bell' && styles.selectedIconButton,
          ]}>
          <Image source={Images.notification} style={styles.iconImage} />
          {renderBadge(unreadCount || notificationCount)}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            setSelectedTab('chat');
            navigation.navigate(SCREEN_NAME.CHAT_LIST);
          }}
          style={[
            styles.iconButton,
            selectedTab === 'chat' && styles.selectedIconButton,
          ]}>
          <Image source={Images.message} style={styles.iconImage} />
          {renderBadge(chatCount)}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            setSelectedTab('coin');
            navigation.navigate(SCREEN_NAME.ECOM_COIN);
          }}
          style={[
            styles.iconButton,
            selectedTab === 'coin' && styles.selectedIconButton,
          ]}>
          <Image source={Images.ecomCoins} style={styles.coinIconImage} />
          {renderBadge(coinCount)}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default NotificationBadge;

const styles = StyleSheet.create({
  notificationCount: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: 'red',
    borderRadius: 8,
    width: 14,
    height: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconImage: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  coinIconImage: {
    width: 26,
    height: 26,
    resizeMode: 'contain',
  },
  notificationCountText: {
    color: customColors.white,
    fontSize: 8,
    fontWeight: 'bold',
  },
  iconView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  dialogBox: {
    borderRadius: 12,
    backgroundColor: customColors.white,
  },
  header: {fontSize: 16, marginLeft: 10},
  dialogActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },

  iconRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },

  iconButton: {
    marginHorizontal: 4,
    overflow: 'visible',
  },
  selectedIconButton: {
    padding: 5,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.gray.light,
    backgroundColor: 'white',
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 1,
    minWidth: 14,
    height: 14,
    borderRadius: 8,
    backgroundColor: customColors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
