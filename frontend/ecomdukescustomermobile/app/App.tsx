/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */
import 'react-native-reanimated';
import React, {useEffect} from 'react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {PaperProvider} from 'react-native-paper';
import {theme} from './theme/theme';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';
import AppNavigations from './navigations';
import {NavigationContainer} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import {toastConfig} from './utils/AlertConfig';
import {store} from './redux/store';
import {PaymentProvider} from './providers/PaymentProvider';
import SplashScreen from 'react-native-splash-screen';
import VersionChecker from './components/versionChecker/VersionChecker';

function App(): React.JSX.Element {
  const linking = {
    prefixes: ['ecomdukes-customer://', 'https://ecomdukes-customer.com'],
    config: {
      screens: {
        products: {
          path: 'product-details/:productId',
          parse: {
            productId: (id: string) => `${id}`,
          },
        },
      },
    },
  };
  useEffect(() => {
    SplashScreen.hide();
  }, []);

  return (
    <>
      <GestureHandlerRootView>
        <Provider store={store}>
          <PaymentProvider>
            <SafeAreaProvider>
              <PaperProvider theme={theme}>
                <NavigationContainer linking={linking}>
                  <AppNavigations />
                  <VersionChecker />
                </NavigationContainer>
                <Toast config={toastConfig} position="top" />
              </PaperProvider>
            </SafeAreaProvider>
          </PaymentProvider>
        </Provider>
      </GestureHandlerRootView>
    </>
  );
}

export default App;
