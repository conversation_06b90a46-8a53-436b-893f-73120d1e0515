info A dev server is already running for this project on port 8081.
info Found Xcode workspace "ecomdukescustomermobile.xcworkspace"
info Found booted iPhone 16 Pro Max
info Building (using "xcodebuild -workspace ecomdukescustomermobile.xcworkspace -configuration Debug -scheme ecomdukescustomermobile -destination id=F12A69BB-BFB7-4BE7-B8A1-27AD74DE5B6A")
- Building the app
- Building the app.......
- Building the app........
- Building the app.......
- Building the app
- Building the app........
- Building the app......
- Building the app
- Building the app......
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app
- Building the app....
- Building the app......
- Building the app.....
- Building the app......
- Building the app.
- Building the app..
- Building the app.....
- Building the app......
- Building the app.......
- Building the app.....
- Building the app......
- Building the app........
- Building the app.........
- Building the app.
- Building the app..
- Building the app
- Building the app.
- Building the app.....
- Building the app......
- Building the app......
- Building the app.......
- Building the app......
- Building the app.......
- Building the app........
- Building the app.........
- Building the app.....
- Building the app......
- Building the app.......
- Building the app......
- Building the app.......
- Building the app....
- Building the app.....
- Building the app.
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app
- Building the app....
- Building the app....
- Building the app......
- Building the app........
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app.........
- Building the app....
- Building the app.........
- Building the app
- Building the app........
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.
- Building the app...
- Building the app.
- Building the app..
- Building the app...
- Building the app.....
- Building the app
- Building the app..
- Building the app.......
- Building the app.....
- Building the app.......
- Building the app...
- Building the app.
- Building the app..
- Building the app...
- Building the app..
- Building the app....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app
- Building the app..
- Building the app....
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app......
- Building the app........
- Building the app
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app........
- Building the app.........
- Building the app.
- Building the app...
- Building the app........
- Building the app.........
- Building the app.
- Building the app...
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.........
- Building the app.
- Building the app...
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app...
- Building the app.....
- Building the app.......
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app........
- Building the app
- Building the app..
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.
- Building the app...
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app
- Building the app..
- Building the app....
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app
- Building the app..
- Building the app....
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app........
- Building the app.........
- Building the app.
- Building the app...
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app.........
- Building the app.
- Building the app...
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app..
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app.
- Building the app...
- Building the app........
- Building the app.........
- Building the app.
- Building the app...
- Building the app..
- Building the app....
- Building the app......
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.......
- Building the app.........
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app......
- Building the app........
- Building the app
- Building the app....
- Building the app......
- Building the app........
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app..
- Building the app....
- Building the app......
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app.
- Building the app...
- Building the app.....
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app........
- Building the app
- Building the app........
- Building the app.........
- Building the app..
- Building the app....
- Building the app
- Building the app........
- Building the app
- Building the app......
- Building the app......
- Building the app........
- Building the app........
- Building the app...
- Building the app.....
- Building the app......
- Building the app........
- Building the app..
- Building the app.........
- Building the app.
- Building the app.........
- Building the app..
- Building the app....
- Building the app
- Building the app..
- Building the app....
- Building the app..
- Building the app....
- Building the app......
- Building the app.
- Building the app..
- Building the app.
- Building the app...
- Building the app
- Building the app..
- Building the app........
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app...
- Building the app.....
- Building the app...
- Building the app.........
- Building the app.
- Building the app.
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app...
- Building the app....
- Building the app........
- Building the app
- Building the app....
- Building the app....
- Building the app......
- Building the app.......
- Building the app........
- Building the app.
- Building the app...
- Building the app.........
- Building the app.
- Building the app......
- Building the app.
- Building the app...
- Building the app.......
- Building the app.........
- Building the app......
- Building the app.......
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app..
- Building the app....
- Building the app..
- Building the app..
- Building the app....
- Building the app........
- Building the app
- Building the app..
- Building the app.........
- Building the app.
- Building the app..
- Building the app...
- Building the app..
- Building the app....
- Building the app
- Building the app.
- Building the app.......
- Building the app.........
- Building the app.....
- Building the app
- Building the app..
- Building the app...
- Building the app...
- Building the app.....
- Building the app......
- Building the app......
- Building the app........
- Building the app........
- Building the app......
- Building the app........
- Building the app
- Building the app.........
- Building the app.
- Building the app....
- Building the app......
- Building the app.
- Building the app...
- Building the app......
- Building the app........
- Building the app..
- Building the app....
- Building the app.....
- Building the app.......
- Building the app
- Building the app..
- Building the app
- Building the app.
- Building the app.....
- Building the app.......
- Building the app.
- Building the app..
- Building the app....
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app..
- Building the app...
- Building the app.....
- Building the app....
- Building the app.
- Building the app...
- Building the app.....
- Building the app....
- Building the app......
- Building the app.........
- Building the app.
- Building the app
- Building the app.
- Building the app....
- Building the app......
- Building the app..
- Building the app...
- Building the app
- Building the app..
- Building the app.
- Building the app....
- Building the app......
- Building the app...
- Building the app.........
- Building the app.
- Building the app..
- Building the app.....
- Building the app.......
- Building the app...
- Building the app....
- Building the app.........
- Building the app.
- Building the app...
- Building the app....
- Building the app.........
- Building the app.
- Building the app....
- Building the app....
- Building the app......
- Building the app......
- Building the app........
- Building the app.......
- Building the app.........
- Building the app........
- Building the app...
- Building the app.....
- Building the app.........
- Building the app......
- Building the app......
- Building the app
- Building the app........
- Building the app..
- Building the app....
- Building the app..
- Building the app....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app.
- Building the app...
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app..
- Building the app...
- Building the app.....
- Building the app.......
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app.........
- Building the app.
- Building the app...
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app....
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app........
- Building the app
- Building the app..
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app
- Building the app..
- Building the app....
- Building the app........
- Building the app
- Building the app..
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.
- Building the app...
- Building the app.....
- Building the app...
- Building the app.....
- Building the app.......
- Building the app......
- Building the app........
- Building the app
- Building the app....
- Building the app...
- Building the app.....
- Building the app.
- Building the app
- Building the app....
- Building the app..
- Building the app...
- Building the app..
- Building the app......
- Building the app........
- Building the app........
- Building the app........
- Building the app.
- Building the app...
- Building the app........
- Building the app
- Building the app.........
- Building the app.
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.
- Building the app...
- Building the app....
- Building the app.....
- Building the app.........
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app....
- Building the app......
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app........
- Building the app
- Building the app..
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.........
- Building the app.
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app.......
- Building the app...
- Building the app...
- Building the app.......
- Building the app.........
- Building the app.....
- Building the app.........
- Building the app.......
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app.........
- Building the app.
- Building the app.........
- Building the app.........
- Building the app.
- Building the app...
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app.......
- Building the app.....
- Building the app.......
- Building the app........
- Building the app....
- Building the app......
- Building the app.....
- Building the app
- Building the app..
- Building the app.
- Building the app...
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app.......
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app........
- Building the app.......
- Building the app..
- Building the app....
- Building the app
- Building the app........
- Building the app.......
- Building the app......
- Building the app........
- Building the app
- Building the app
- Building the app..
- Building the app.
- Building the app..
- Building the app...
- Building the app..
- Building the app
- Building the app..
- Building the app...
- Building the app...
- Building the app.....
- Building the app.........
- Building the app
- Building the app..
- Building the app.
- Building the app.......
- Building the app........
- Building the app.......
- Building the app..
- Building the app...
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app.......
- Building the app........
- Building the app.........
- Building the app.......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.....
- Building the app.....
- Building the app......
- Building the app......
- Building the app........
- Building the app.....
- Building the app..
- Building the app..
- Building the app...
- Building the app......
- Building the app.........
- Building the app.....
- Building the app.
- Building the app..
- Building the app........
- Building the app
- Building the app.
- Building the app...
- Building the app.....
- Building the app..
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app........
- Building the app.....
- Building the app........
- Building the app.......
- Building the app
- Building the app
- Building the app......
- Building the app.........
- Building the app.....
- Building the app.......
- Building the app....
- Building the app.....
- Building the app.........
- Building the app...
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app....
- Building the app....
- Building the app.........
- Building the app
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app....
- Building the app.
- Building the app...
- Building the app.
- Building the app...
- Building the app.......
- Building the app.........
- Building the app.........
- Building the app.
- Building the app
- Building the app..
- Building the app.......
- Building the app.
- Building the app......
- Building the app......
- Building the app.
- Building the app...
- Building the app.....
- Building the app.
- Building the app.
- Building the app...
- Building the app.......
- Building the app.
- Building the app..
- Building the app
- Building the app......
- Building the app.......
- Building the app.........
- Building the app
- Building the app...
- Building the app.
- Building the app...
- Building the app.....
- Building the app........
- Building the app......
- Building the app........
- Building the app.......
- Building the app....
- Building the app..
- Building the app....
- Building the app.......
- Building the app.........
- Building the app.....
- Building the app.......
- Building the app...
- Building the app.....
- Building the app.........
- Building the app.
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.....
- Building the app.......
- Building the app......
- Building the app........
- Building the app....
- Building the app..
- Building the app..
- Building the app...
- Building the app........
- Building the app..
- Building the app....
- Building the app...
- Building the app..
- Building the app....
- Building the app..
- Building the app..
- Building the app.....
- Building the app......
- Building the app
- Building the app.......
- Building the app........
- Building the app
- Building the app..
- Building the app..
- Building the app....
- Building the app......
- Building the app.
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.........
- Building the app.
- Building the app..
- Building the app...
- Building the app.....
- Building the app.......
- Building the app.......
- Building the app
- Building the app..
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app.
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app....
- Building the app.......
- Building the app
- Building the app....
- Building the app...
- Building the app......
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app.....
- Building the app......
- Building the app......
- Building the app....
- Building the app.........
- Building the app...
- Building the app.....
- Building the app........
- Building the app..
- Building the app....
- Building the app
- Building the app
- Building the app...
- Building the app........
- Building the app......
- Building the app....
- Building the app......
- Building the app......
- Building the app
- Building the app......
- Building the app.......
- Building the app.........
- Building the app.
- Building the app...
- Building the app..
- Building the app...
- Building the app..
- Building the app.........
- Building the app.
- Building the app.
- Building the app.........
- Building the app....
- Building the app.......
- Building the app.......
- Building the app...
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app..
- Building the app..
- Building the app....
- Building the app......
- Building the app........
- Building the app
- Building the app...
- Building the app......
- Building the app........
- Building the app...
- Building the app...
- Building the app.....
- Building the app...
- Building the app.......
- Building the app.....
- Building the app........
- Building the app..
- Building the app....
- Building the app
- Building the app.......
- Building the app........
- Building the app.
- Building the app...
- Building the app.........
- Building the app
- Building the app.....
- Building the app.
- Building the app.......
- Building the app..
- Building the app
- Building the app........
- Building the app..
- Building the app.

info 💡 Tip: Make sure that you have set up your development environment correctly, by running npx react-native doctor. To read more about doctor command visit: https://github.com/react-native-community/cli/blob/main/packages/cli-doctor/README.md#doctor 

error export CLANG_WARN_DOCUMENTATION_COMMENTS\=YES
error export CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER\=NO
error export GCC_WARN_INHIBIT_ALL_WARNINGS\=YES
error export VALIDATE_PRODUCT\=NO
error export GCC_WARN_UNDECLARED_SELECTOR\=YES
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDSignIn.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDSignIn.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/GoogleSignIn/GoogleSignIn/Sources/GIDSignIn.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDSignIn.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDSignIn.o
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDGoogleUser.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDGoogleUser.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/GoogleSignIn/GoogleSignIn/Sources/GIDGoogleUser.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDGoogleUser.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDGoogleUser.o
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDEMMSupport.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDEMMSupport.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/GoogleSignIn/GoogleSignIn/Sources/GIDEMMSupport.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDEMMSupport.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDEMMSupport.o
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDAuthStateMigration.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDAuthStateMigration.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/GoogleSignIn/GoogleSignIn/Sources/GIDAuthStateMigration.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDAuthStateMigration.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/GoogleSignIn.build/Objects-normal/arm64/GIDAuthStateMigration.o
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Target\ Support\ Files/FirebaseMessaging/FirebaseMessaging-dummy.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Target\ Support\ Files/FirebaseMessaging/FirebaseMessaging-dummy.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FirebaseMessaging-dummy.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FirebaseMessaging-dummy.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseInstallations/FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseInstallations/FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-CZPG3G1VBBJ5XF05TQDGT37FY.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/FirebaseInstallations-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/FirebaseInstallations-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseInstallations/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseInstallations -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseInstallations -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseInstallations -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/FirebaseInstallations-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/FirebaseInstallations-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FIRCurrentDateProvider.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseInstallations.build/Objects-normal/arm64/FIRCurrentDateProvider.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -include /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Target\ Support\ Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseCoreExtension/FirebaseCore/Extension/dummy.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseCoreExtension/FirebaseCore/Extension/dummy.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=UIKit\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/UIKit-18XQ63ECWG8DNG05JFCIC9MBX.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/FirebaseCoreExtension-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/FirebaseCoreExtension-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreExtension/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseCoreExtension -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreExtension -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/FirebaseCoreExtension-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/FirebaseCoreExtension-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name dummy.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -include /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Target\ Support\ Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCoreExtension.build/Objects-normal/arm64/dummy.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseCore/FirebaseCore/Sources/FIRAnalyticsConfiguration.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseCore/FirebaseCore/Sources/FIRAnalyticsConfiguration.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-65JF4HTJ1ORE1Q79IFOV5FDTP.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/FirebaseCore-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/FirebaseCore-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCore/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseCore -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseCore -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCore -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/FirebaseCore-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/FirebaseCore-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fno-autolink -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FIRAnalyticsConfiguration.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D Firebase_VERSION\=11.15.0 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRAnalyticsConfiguration.d -skip-unused-modulemap-deps
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_workgroup-DFPKWX988OBK6LW22CR5GAV1V.dia -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -fsystem-module -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/os.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/Darwin.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/DarwinBasic.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/c_standard_library.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/os_availability.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_workgroup-DFPKWX988OBK6LW22CR5GAV1V.pcm -disable-free -emit-module -x objective-c /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/os.modulemap -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modules-prune-non-affecting-module-map-files -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Darwin\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Darwin-9R1U4VIMLQCSR3FXL8S0V1CZY.pcm -fmodule-file\=_Builtin_stdbool\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdbool-EEMY63BJ1ZCDG84LLGVVO8B4G.pcm -fmodule-file\=_Builtin_stddef\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stddef-U6ZJS3I5N05WUGEKI7IGLMC3.pcm -fmodule-file\=_Builtin_stdint\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdint-6TEAJOYWGO1B74LYDT86RR3Y8.pcm -fmodule-file\=_stdlib\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_stdlib-CYOSLTIW382C0ZT03IV12NVM3.pcm -fmodule-file\=_string\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_string-9GF30UL5SE4I2REVNWQ5NU9YG.pcm -fmodule-file\=os_availability\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_availability-2MDRY44W4IX131IL7OFQ9GL8C.pcm -fmodule-file\=os_object\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_object-CXVAVXJ9JDEJ0SPT7VJRGFWYV.pcm -fmodule-file\=sys_types\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/sys_types-4RDU1QV76DH3OFE38MFXP1636.pcm -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -std\=gnu11 -fexceptions -fmodules -fmodule-name\=os_workgroup -fmodules-search-all -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -disable-objc-default-synthesize-properties -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=off -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fcommon -fno-sanitize-address-use-odr-indicator -fno-sanitize-memory-param-retval -fmerge-all-constants -fno-verbose-asm -fregister-global-dtors-with-atexit -fno-use-init-array -funique-basic-block-section-names -fsplit-dwarf-inlining -gno-codeview-command-line -gstrict-dwarf -gno-column-info -llvm-verify-each -no-struct-path-tbaa -fexperimental-assignment-tracking\=disabled -no-enable-noundef-analysis -stack-protector-buffer-size 0 -gpubnames -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D GID_SDK_VERSION\=7.1.0 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT moduledependenciestarget -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_workgroup-DFPKWX988OBK6LW22CR5GAV1V.d -skip-unused-modulemap-deps
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Dispatch-EGL108Y54ZSQHAKD5QYO6JBQN.dia -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -fsystem-module -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/os.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/Darwin.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/DarwinBasic.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/c_standard_library.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/DarwinFoundation.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/os_availability.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include/TargetConditionals.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Dispatch-EGL108Y54ZSQHAKD5QYO6JBQN.pcm -disable-free -emit-module -x objective-c /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/dispatch/module.modulemap -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modules-prune-non-affecting-module-map-files -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Darwin\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Darwin-9R1U4VIMLQCSR3FXL8S0V1CZY.pcm -fmodule-file\=DarwinFoundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/DarwinFoundation-3DFOE0RW4MPFLHO0BNMNGJ5QT.pcm -fmodule-file\=TargetConditionals\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/TargetConditionals-33KPCCAAXI1PKE11JAT8H5VD4.pcm -fmodule-file\=_Builtin_stdarg\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdarg-3FNUOPDCYSZPE2U6URS6XN7W5.pcm -fmodule-file\=_Builtin_stdbool\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdbool-EEMY63BJ1ZCDG84LLGVVO8B4G.pcm -fmodule-file\=_Builtin_stddef\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stddef-U6ZJS3I5N05WUGEKI7IGLMC3.pcm -fmodule-file\=_Builtin_stdint\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdint-6TEAJOYWGO1B74LYDT86RR3Y8.pcm -fmodule-file\=_signal\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_signal-DRORHP58GQZORBVZERIVM9JUW.pcm -fmodule-file\=_string\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_string-9GF30UL5SE4I2REVNWQ5NU9YG.pcm -fmodule-file\=os_availability\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_availability-2MDRY44W4IX131IL7OFQ9GL8C.pcm -fmodule-file\=os_object\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_object-CXVAVXJ9JDEJ0SPT7VJRGFWYV.pcm -fmodule-file\=os_workgroup\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/os_workgroup-DFPKWX988OBK6LW22CR5GAV1V.pcm -fmodule-file\=sys_types\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/sys_types-4RDU1QV76DH3OFE38MFXP1636.pcm -fmodule-file\=unistd\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/unistd-T4Y2O2H8M89E3Q79CTM8WOZS.pcm -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -std\=gnu11 -fexceptions -fmodules -fmodule-name\=Dispatch -fmodules-search-all -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -disable-objc-default-synthesize-properties -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=off -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fcommon -fno-sanitize-address-use-odr-indicator -fno-sanitize-memory-param-retval -fmerge-all-constants -fno-verbose-asm -fregister-global-dtors-with-atexit -fno-use-init-array -funique-basic-block-section-names -fsplit-dwarf-inlining -gno-codeview-command-line -gstrict-dwarf -gno-column-info -llvm-verify-each -no-struct-path-tbaa -fexperimental-assignment-tracking\=disabled -no-enable-noundef-analysis -stack-protector-buffer-size 0 -gpubnames -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D GID_SDK_VERSION\=7.1.0 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT moduledependenciestarget -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Dispatch-EGL108Y54ZSQHAKD5QYO6JBQN.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRHeartbeatLogger.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRHeartbeatLogger.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseCore/FirebaseCore/Sources/FIRHeartbeatLogger.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRHeartbeatLogger.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseCore.build/Objects-normal/arm64/FIRHeartbeatLogger.o
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Werror\=deprecated-objc-isa-usage -Werror\=objc-root-class -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Werror\=deprecated-objc-isa-usage -Werror\=objc-root-class -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include/module.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include/module.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include/module.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/c_standard_library.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/c_standard_library.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.o -disable-free -emit-obj -x c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=_Builtin_stdbool\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdbool-DIKXI9W18VMLSGN63KGEUAMA.pcm -fmodule-file\=_Builtin_stddef\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stddef-EB210LQO82FYUM4FLJB4T00HX.pcm -fmodule-file\=_Builtin_stdint\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_Builtin_stdint-D4Z36R6JFF4OCRK2RCCPFREHO.pcm -fmodule-file\=_stdlib\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_stdlib-2L1IVJH3J2CT2S6WLUNQ21YP9.pcm -fmodule-file\=_string\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/_string-46WG8TJIHS4MRMILG5GROVMFS.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fmodules -fno-implicit-modules -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name me.nanopb.c -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/me.nanopb.d -skip-unused-modulemap-deps
error +FIRMessaging.o /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/NSError+FIRMessaging.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler (in target 'FirebaseMessaging' from project 'Pods')
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/NSError+FIRMessaging.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/NSError+FIRMessaging.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name NSError+FIRMessaging.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSError+FIRMessaging.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name NSDictionary+FIRMessaging.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/NSDictionary+FIRMessaging.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/FIRMessagingUtilities.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/FIRMessagingUtilities.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=FirebaseCore\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/FirebaseCore-2A4ATKUFQ59VC5GW4ZEHQ7N0Z.pcm -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FIRMessagingUtilities.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingUtilities.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/FIRMessagingTopicOperation.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/FIRMessagingTopicOperation.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FIRMessagingTopicOperation.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTopicOperation.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FIRMessagingTokenStore.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenStore.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.o -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FBLPromises/PromisesObjC.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseCore/FirebaseCore.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap -fmodule-map-file\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/nanopb/nanopb.modulemap -fmodule-map-file\=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.o -disable-free -emit-obj -x objective-c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m -target-abi darwinpcs -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -triple arm64-apple-ios15.1.0-simulator -target-linker-version 1115.7.3 -target-sdk-version\=18.1 -fmodules-validate-system-headers -fno-modulemap-allow-subdirectory-search -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -fmodule-file\=Foundation\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/ExplicitPrecompiledModules/Foundation-1LGXMNVV1Y2AND06SAHKQHN1A.pcm -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-own-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-all-non-framework-target-headers.hmap -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging/include -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Private/FirebaseMessaging -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/Headers/Public/PromisesObjC -I /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Products/Debug-iphonesimulator/FirebaseMessaging -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-generated-files.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/FirebaseMessaging-project-headers.hmap -isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/local/include -isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -ivfsoverlay /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphonesimulator/all-product-headers.yaml -std\=gnu11 -fexceptions -fmodules -fno-implicit-modules -fobjc-exceptions -fmax-type-align\=16 -fpascal-strings -fvisibility-inlines-hidden-static-local-var -mdarwin-stkchk-strong-link -fno-odr-hash-protocols -pic-level 2 -fencode-extended-block-signature -stack-protector 1 -fobjc-runtime\=ios-15.1.0 -fobjc-arc -fobjc-runtime-has-weak -fobjc-weak -fgnuc-version\=4.2.1 -fblocks -ffp-contract\=on -fclang-abi-compat\=4.0 -fno-experimental-relative-c++-abi-vtables -fno-file-reproducible -clang-vendor-feature\=+disableNonDependentMemberExprInCurrentInstantiation -clang-vendor-feature\=+enableAggressiveVLAFolding -clang-vendor-feature\=+revert09abecef7bbf -clang-vendor-feature\=+thisNoAlignAttr -clang-vendor-feature\=+thisNoNullAttr -clang-vendor-feature\=+disableAtImportPrivateFrameworkInImplementationError -O0 -fdebug-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fcoverage-compilation-dir\=/Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods -fobjc-msgsend-selector-stubs -fregister-global-dtors-with-atexit -fno-strict-return -mrelax-all -dwarf-version\=4 -debugger-tuning\=lldb -disable-llvm-verifier -mframe-pointer\=non-leaf -funwind-tables\=1 -clear-ast-before-backend -discard-value-names -main-file-name FIRMessagingTokenOperation.m -debug-info-kind\=standalone -fdiagnostics-hotness-threshold\=0 -fdiagnostics-misexpect-tolerance\=0 -D COCOAPODS\=1 -D DEBUG\=1 -D OBJC_OLD_DISPATCH_PROTOTYPES\=0 -D PB_ENABLE_MALLOC\=1 -D PB_FIELD_32BIT\=1 -D PB_NO_PACKED_STRUCTS\=1 -D POD_CONFIGURATION_DEBUG\=1 -D __GCC_HAVE_DWARF2_CFI_ASM\=1 -MT dependencies -dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenOperation.d -skip-unused-modulemap-deps
error \=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenManager.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenManager.dia -c /Users/<USER>/Desktop/ecomdukes/frontend/ecomdukescustomermobile/ios/Pods/FirebaseMessaging/FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenManager.o -index-unit-output-path /Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenManager.o
error limit 19 -serialize-diagnostic-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ecomdukescustomermobile-eclttjmrvgdmfyfbeyebgayjezvw/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/FirebaseMessaging.build/Objects-normal/arm64/FIRMessagingTokenManager.dia -Wdeprecated-objc-isa-usage -Werror\=deprecated-objc-isa-usage -Werror\=implicit-function-declaration -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\=return-type -Wdocumentation -Wunreachable-code -Wno-implicit-atomic-properties -Werror\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -