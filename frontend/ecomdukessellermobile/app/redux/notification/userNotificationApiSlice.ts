import {ApiSliceIdentifier} from '../../constants/enums';
import {
  UpdateUserNotificationRequest,
  UserNotification,
  UserNotificationFilter,
} from '../../types/user-notification';
import {apiSlice} from '../apiSlice';
import {ApiTagTypes} from '../types';

export const userNotificationApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getUserNotifications: builder.query<
      UserNotification[],
      {userTenantId: string; filter?: UserNotificationFilter}
    >({
      query: ({userTenantId, filter}) => {
        const defaultFilter: UserNotificationFilter = {
          where: {userTenantId},
          order: ['createdOn DESC'],
          limit: 50,
          ...filter,
        };

        return {
          url: '/user-notifications',
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            filter: JSON.stringify(defaultFilter),
          },
        };
      },
      providesTags: [ApiTagTypes.UserNotification],
    }),

    getUserNotificationById: builder.query<UserNotification, string>({
      query: id => ({
        url: `/user-notifications/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      providesTags: [ApiTagTypes.UserNotification],
    }),

    updateUserNotificationById: builder.mutation<
      void,
      {id: string; data: UpdateUserNotificationRequest}
    >({
      query: ({id, data}) => ({
        url: `/user-notifications/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data,
      }),
      invalidatesTags: [ApiTagTypes.UserNotification],
    }),

    getUserNotificationsCount: builder.query<
      {count: number},
      {userTenantId: string; isRead?: boolean}
    >({
      query: ({userTenantId, isRead}) => {
        const where: any = {userTenantId};
        if (isRead !== undefined) {
          where.isRead = isRead;
        }

        return {
          url: '/user-notifications/count',
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            where: JSON.stringify(where),
          },
        };
      },
      providesTags: [ApiTagTypes.UserNotification],
    }),

    markNotificationAsRead: builder.mutation<void, string>({
      query: id => ({
        url: `/user-notifications/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {isRead: true},
      }),
      invalidatesTags: [ApiTagTypes.UserNotification],
    }),

    markAllNotificationsAsRead: builder.mutation<void, string>({
      query: userTenantId => ({
        url: '/user-notifications',
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {isRead: true},
        params: {
          where: JSON.stringify({userTenantId, isRead: false}),
        },
      }),
      invalidatesTags: [ApiTagTypes.UserNotification],
    }),
  }),
});

export const {
  useGetUserNotificationsQuery,
  useLazyGetUserNotificationsQuery,
  useGetUserNotificationByIdQuery,
  useUpdateUserNotificationByIdMutation,
  useGetUserNotificationsCountQuery,
  useLazyGetUserNotificationsCountQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,
} = userNotificationApiSlice;
