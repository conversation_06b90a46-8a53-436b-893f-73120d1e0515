import {ApiSliceIdentifier} from '../../constants/enums';
import {apiSlice} from '../apiSlice';

interface FileUploadOnboardRequest {
  file: FormData;
}
export interface Subscription {
  id?: string;
  subscriberId: string;
  startDate: string;
  endDate?: string;
  status?: SubscriptionStatus;
  planId: string;
}
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}
export const ecomApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    fileUploadOnboard: builder.mutation({
      query: ({
        fileUploadOnboard,
      }: {
        fileUploadOnboard: FileUploadOnboardRequest;
      }) => ({
        url: '/files/upload',
        method: 'POST',
        body: fileUploadOnboard.file,
        formData: true,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getSubscription: builder.query<Subscription[], string>({
      query: sellerId => ({
        url: '/subscriptions',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        params: {
          filter: JSON.stringify({
            where: {status: 'ACTIVE', subscriberId: sellerId},
            limit: 1,
          }),
        },
      }),
    }),
    createSubscription: builder.mutation<
      Subscription,
      {subscriberId: string; planId: string}
    >({
      query: ({subscriberId, planId}) => ({
        url: '/subscriptions',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {
          subscriberId,
          planId,
          startDate: new Date().toISOString(),
        },
      }),
    }),
  }),
});

export const {
  useFileUploadOnboardMutation,
  useGetSubscriptionQuery,
  useCreateSubscriptionMutation,
} = ecomApiSlice;
