import {ApiSliceIdentifier} from '../../constants/enums';
import {apiSlice} from '../apiSlice';
export interface PlanPricing {
  id: string;
  planId: string;
  minSalesThreshold: number;
  maxSalesThreshold: number;
  price: string; // You can change this to `number` if you plan to treat it numerically
  modifiedBy: string | null;
  modifiedOn: string; // ISO date string
}
export interface Plan {
  id: string;
  name: string;
  key: string;
  status: string;
  currency: string;
  amount: number;
  createdAt: string;
  modifiedAt: string;
  planFeatureValues: PlanFeatureValue[];
  planPricings: PlanPricing[];
}
interface PlanFeatureValue {
  id: string;
  planId: string;
  featureValueId: string;
  createdAt: string;
  modifiedAt: string;
  featureValue: FeatureValue;
}
interface FeatureValue {
  id: string;
  value: string;
  key: string;
  valueType: string;
  featureId: string;
  createdAt: string;
  modifiedAt: string;
  feature: Feature;
}

export interface Feature {
  id: string;
  name: string;
  key: string;
  createdAt: string;
  modifiedAt: string;
  category: string;
}
export interface Subscription {
  id?: string;
  subscriberId: string;
  startDate: string;
  endDate?: string;
  status?: SubscriptionStatus;
  planId: string;
}
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}
export const planApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPlans: builder.query<Plan[], void>({
      query: () => ({
        url: `/plans`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {status: 'ACTIVE'},
            include: [
              {
                relation: 'planFeatureValues',
                scope: {
                  include: [
                    {
                      relation: 'featureValue',
                      scope: {
                        include: ['feature'],
                      },
                    },
                  ],
                },
              },
              {
                relation: 'planPricings',
                scope: {
                  order: ['minSalesThreshold ASC'],
                },
              },
            ],
            skip: 0,
            order: 'name ASC',
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getFeatures: builder.query<Feature[], void>({
      query: () => ({
        url: '/features',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});
export const {useGetFeaturesQuery, useGetPlansQuery} = planApiSlice;
