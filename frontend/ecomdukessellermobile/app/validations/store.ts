import * as Yup from 'yup';
export const storeValidationSchema = Yup.object({
  description: Yup.string()
    .trim()
    .required('Description is required')
    .min(10, 'Description must be at least 10 characters'),
});
export const storeUpdateValidation = Yup.object({
  workingHours: Yup.string().required('Working hours is required'),
  legalName: Yup.string().trim().required('Company Name is required'),
  storeName: Yup.string().trim().required('Store Name is required'),
  pincode: Yup.string()
    .matches(/^\d{6}$/, 'Pincode must be exactly 6 digits')
    .required('Pincode is required'),
  // logo: Yup.string(),
  // banner: Yup.string(),
  // dp: Yup.string(),
  // signature: Yup.string(),
  description: Yup.string(),
  country: Yup.string().required().label('Country'),
  state: Yup.string().required().label('State'),
  city: Yup.string().required().label('City'),
  fbId: Yup.string()
    .trim()
    .url('Must be a valid URL')
    .test(
      'is-facebook-url',
      'Must be a Facebook link',
      value => !value || value.includes('facebook.com'),
    )
    .test(
      'fb-or-insta',
      'At least one social field is required: Facebook or Instagram',
      function (value) {
        const {instaId} = this.parent;
        return !!(value && value.trim()) || !!(instaId && instaId.trim());
      },
    ),

  instaId: Yup.string()
    .trim()
    .url('Must be a valid URL')
    .test(
      'is-instagram-url',
      'Must be an Instagram link',
      value => !value || value.includes('instagram.com'),
    )
    .test(
      'fb-or-insta',
      'At least one social field is required: Facebook or Instagram',
      function (value) {
        const {fbId} = this.parent;
        return !!(value && value.trim()) || !!(fbId && fbId.trim());
      },
    ),

  website: Yup.string()
    .trim()
    .url('Please enter a valid website URL')
    .nullable()
    .notRequired(),
});
