import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Formik, FormikProps} from 'formik';
import {Image as ImageType, openPicker} from 'react-native-image-crop-picker';
import Toast from 'react-native-toast-message';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import Screenwrapper from '../../../components/ScreenWrapper/Screenwrapper';
import {commonStyles, scrollViewProps} from '../../../constants/commonstyles';
import {storeUpdateValidation} from '../../../validations';
import CustomTextInput from '../../../components/InputFields/Textinput';
import {STRINGS} from '../../../constants';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import styleConstants from '../../../theme/styleConstants';
import {colors} from '../../../theme/colors';
import CustomDropDown from '../../../components/InputFields/Dropdown';
import {Country, State} from 'country-state-city';
import {Checkbox, HelperText, Text} from 'react-native-paper';
import {Images} from '../../../assets/images';
import {
  useLazyGetSellerStoreBySelleryIdQuery,
  useUpdateSellerStoreByIdMutation,
} from '../../../redux/store/storeApiSlice';
import {SellerStoreDto} from '../../../types/store';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import customColors from '../../../theme/customColors';
import dayjs from 'dayjs';
import moment from 'moment';
import DatePicker from 'react-native-date-picker';
import {FONTS} from '../../../theme/fonts';

type ImageValue =
  | string
  | {
      path: string;
      mime: string;
      filename: string;
    };

interface FileKey {
  logo: ImageType | null;
  banner: ImageType | null;
  signature: File | null;
}

const CompanyUpdateScreen = () => {
  const [selectedCountry, setSelectedCountry] = useState('IN');

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerType, setDatePickerType] = useState<null | string>(null);
  const countryList = Country.getAllCountries();
  const stateList = useMemo(
    () => State.getStatesOfCountry(selectedCountry),
    [selectedCountry],
  );
  const {data: user} = useGetUserQuery();
  const [updateSellerStoreById, {isLoading: updateLoading}] =
    useUpdateSellerStoreByIdMutation();
  const [triggerGetSellerDetails, {data: sellerStore}] =
    useLazyGetSellerStoreBySelleryIdQuery();
  const formikRef = useRef<FormikProps<SellerStoreDto>>(null);
  useEffect(() => {
    if (user?.profileId) {
      triggerGetSellerDetails(user.profileId);
    }
  }, []);
  useEffect(() => {
    if (sellerStore?.country) {
      const findCountryCode = countryList?.find(
        country => country?.name === sellerStore?.country,
      )?.isoCode;
      setSelectedCountry(findCountryCode || 'IN');
    }
  }, [sellerStore]);

  const onConfirmDate = useCallback(
    (date: any) => {
      if (datePickerType && formikRef.current) {
        formikRef.current.setFieldValue(datePickerType, moment(date).format());
        setShowDatePicker(false);
        setDatePickerType(null);
      }
    },
    [showDatePicker, datePickerType],
  );

  const handleImagePicker = async (
    fieldKey: keyof FileKey,
    setFieldValue: (field: string, value: any) => void,
  ) => {
    const image = await openPicker({
      multiple: false,
      compressImageQuality: 0.8,
      mediaType: 'photo',
    });

    if (!image) {
      return;
    }
    setFieldValue(fieldKey, image);
  };

  const handleSubmitForm = async (values: SellerStoreDto) => {
    const finalFormData = {
      logo: values.logo,
      banner: values.banner,
      signature: values.signature,
      addressLine1: values.addressLine1,
      addressLine2: values.addressLine2,
      storeName: values.storeName,
      description: values.description,
      country: values.country,
      state: values.state,
      city: values.city,
      pincode: values.pincode,
      fbId: values.fbId ?? '',
      instaId: values.instaId ?? '',
      legalName: values?.legalName ?? '',
      workingHours: values?.workingHours ? Number(values?.workingHours) : 0,
      hideWorkingHours: values?.hideWorkingHours,
      allowBulkOrder: values?.allowBulkOrder,
      allowCategorisation: values?.allowCategorisation,
      unavailabilityStartDate: values?.unavailabilityStartDate
        ? values?.unavailabilityStartDate.toISOString()
        : null,
      unavailabilityEndDate: values?.unavailabilityEndDate
        ? values?.unavailabilityEndDate.toISOString()
        : null,
    };

    await updateSellerStoreById({
      sellerStoreId: sellerStore?.id ?? '',
      body: finalFormData,
    }).unwrap();
    if (user?.profileId) {
      triggerGetSellerDetails(user.profileId);
    }
    Toast.show({
      type: 'success',
      text1: 'Store updated successfully!',
    });
  };
  const getImageSource = (value: ImageValue) => {
    if (typeof value === 'object' && value?.path) {
      return {uri: value.path};
    } else if (typeof value === 'string' && value) {
      return {uri: value};
    } else {
      return Images.placeholder;
    }
  };
  return (
    <SafeAreaView
      style={{
        backgroundColor: colors.surface,
        flex: 1,
        justifyContent: 'center',
      }}>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={{
          margin: styleConstants.spacing.x20,
          borderRadius: styleConstants.borderRadii.b10,
        }}
        contentContainerStyle={{
          backgroundColor: customColors.white,
          borderRadius: styleConstants.borderRadii.b10,
          padding: styleConstants.spacing.x20,
          flexGrow: 1,
        }}>
        <Formik<SellerStoreDto>
          initialValues={{
            description: sellerStore?.description ?? '',
            dp: sellerStore?.dp ?? '',
            logo: sellerStore?.logo ?? '',
            banner: sellerStore?.banner ?? '',
            signature: sellerStore?.signature ?? '',
            storeName: sellerStore?.storeName ?? '',
            addressLine1: sellerStore?.addressLine1 ?? '',
            addressLine2: sellerStore?.addressLine2 ?? '',
            pincode: sellerStore?.pincode ?? '',
            city: sellerStore?.city ?? '',
            state: sellerStore?.state ?? '',
            country: sellerStore?.country ?? 'India',
            fbId: sellerStore?.fbId ?? '',
            instaId: sellerStore?.instaId ?? '',
            website: sellerStore?.website,
            legalName: sellerStore?.legalName ?? '',
            workingHours: sellerStore?.workingHours
              ? sellerStore?.workingHours?.toString()
              : '0',
            hideWorkingHours: sellerStore?.hideWorkingHours ?? false,
            allowBulkOrder: sellerStore?.allowBulkOrder ?? false,
            allowCategorisation: sellerStore?.allowCategorisation ?? false,
            unavailabilityStartDate: sellerStore?.unavailabilityStartDate
              ? dayjs(sellerStore.unavailabilityStartDate)
              : null,
            unavailabilityEndDate: sellerStore?.unavailabilityEndDate
              ? dayjs(sellerStore.unavailabilityEndDate)
              : null,
          }}
          innerRef={formikRef}
          enableReinitialize
          validationSchema={storeUpdateValidation}
          onSubmit={handleSubmitForm}>
          {({
            values,
            handleChange,
            handleSubmit,
            handleBlur,
            setFieldValue,
            setFieldTouched,
            errors,
            touched,
          }) => {
            return (
              <>
                <View style={styles.logoContainer}>
                  <TouchableOpacity
                    onPress={() =>
                      handleImagePicker('logo', (field, value) =>
                        setFieldValue(field, value),
                      )
                    }>
                    <Image
                      source={getImageSource(values.logo)}
                      style={styles.image}
                    />
                    <Text variant="labelLarge" style={styles.uploadText}>
                      Logo
                    </Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.rowContainer}>
                  <TouchableOpacity
                    onPress={() =>
                      handleImagePicker('banner', (field, value) =>
                        setFieldValue(field, value),
                      )
                    }>
                    <Image
                      source={getImageSource(values.banner)}
                      style={styles.image}
                    />
                    <Text variant="labelLarge" style={styles.uploadText}>
                      Banner
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() =>
                      handleImagePicker('signature', (field, value) =>
                        setFieldValue(field, value),
                      )
                    }>
                    <Image
                      source={getImageSource(values.signature)}
                      style={styles.image}
                    />
                    <Text variant="labelLarge" style={styles.uploadText}>
                      Signature
                    </Text>
                  </TouchableOpacity>
                </View>

                <CustomTextInput
                  title={'Company Legal Name'}
                  value={values?.legalName}
                  onChangeText={handleChange('legalName')}
                  onBlur={handleBlur('legalName')}
                  touched={touched.legalName}
                  errors={errors.legalName}
                />
                <CustomTextInput
                  title={STRINGS.storeName}
                  value={values?.storeName}
                  onChangeText={handleChange('storeName')}
                  onBlur={handleBlur('storeName')}
                  touched={touched.storeName}
                  errors={errors.storeName}
                />
                <CustomTextInput
                  multiline
                  style={styles.description}
                  title={STRINGS.storeDescription}
                  value={values.description}
                  onChangeText={handleChange('description')}
                  onBlur={handleBlur('description')}
                  touched={touched.description}
                  errors={errors.description}
                />
                <CustomTextInput
                  title={'Facebook Store Link'}
                  value={values.fbId}
                  onChangeText={handleChange('fbId')}
                  onBlur={() => {
                    setFieldTouched('fbId', true);
                    handleBlur('fbId');
                  }}
                  touched={touched.fbId}
                  errors={errors.fbId}
                />
                <CustomTextInput
                  title={'Instagram Store Link'}
                  value={values.instaId}
                  onChangeText={handleChange('instaId')}
                  onBlur={() => {
                    setFieldTouched('instaId', true);
                    handleBlur('instaId');
                  }}
                  touched={touched.instaId}
                  errors={errors.instaId}
                />
                <CustomTextInput
                  title={STRINGS.website}
                  value={values.website}
                  onChangeText={handleChange('website')}
                  onBlur={() => {
                    setFieldTouched('website', true);
                    handleBlur('website');
                  }}
                  touched={touched.website}
                  errors={errors.website}
                />
                <CustomTextInput
                  title={STRINGS.addressLine1}
                  value={values.addressLine1}
                  onChangeText={handleChange('addressLine1')}
                  onBlur={handleBlur('addressLine1')}
                  touched={touched.addressLine1}
                  errors={errors.addressLine1}
                />
                <CustomTextInput
                  title={STRINGS.addressLine2}
                  value={values.addressLine2}
                  onChangeText={handleChange('addressLine2')}
                  onBlur={handleBlur('addressLine2')}
                  touched={touched.addressLine2}
                  errors={errors.addressLine2}
                />
                <CustomTextInput
                  title={STRINGS.pincode}
                  value={values.pincode}
                  onChangeText={handleChange('pincode')}
                  onBlur={handleBlur('pincode')}
                  touched={touched.pincode}
                  errors={errors.pincode}
                />
                <CustomDropDown
                  disable
                  title={STRINGS.country}
                  value={values.country}
                  touched={touched.country}
                  errors={errors.country}
                  placeholder=""
                  data={countryList}
                  labelField="name"
                  valueField="name"
                  onChange={(item: any) => {
                    setFieldValue('country', item.name);
                    setSelectedCountry(item.isoCode);
                  }}
                  onDropdownFocus={() => {
                    setFieldTouched('country', true);
                  }}
                  onDropdownBlur={() => {
                    handleBlur('country');
                  }}
                  onRemove={() => {
                    setFieldValue('country', '');
                  }}
                />

                <CustomDropDown
                  disable={!values?.country}
                  title={STRINGS.state}
                  value={values.state}
                  touched={touched.state}
                  errors={errors.state}
                  data={stateList}
                  labelField="name"
                  valueField="name"
                  placeholder=""
                  onChange={(item: any) => {
                    setFieldValue('state', item.name);
                  }}
                  onDropdownFocus={() => {
                    setFieldTouched('state', true);
                  }}
                  onDropdownBlur={() => {
                    handleBlur('state');
                  }}
                  onRemove={() => {
                    setFieldValue('state', '');
                  }}
                />
                <CustomTextInput
                  title={STRINGS.city}
                  value={values.city}
                  onChangeText={handleChange('city')}
                  onBlur={handleBlur('city')}
                  touched={touched.city}
                  errors={errors.city}
                />
                <CustomTextInput
                  title={'Working Hours'}
                  value={values.workingHours}
                  onChangeText={handleChange('workingHours')}
                  onBlur={handleBlur('workingHours')}
                  touched={touched.workingHours}
                  errors={errors.workingHours}
                />
                <TouchableOpacity
                  onPress={() => {
                    setShowDatePicker(true);
                    setDatePickerType('unavailabilityStartDate');
                  }}
                  style={styles.datePickerContainer}>
                  <Text variant="bodyMedium" style={styles.title}>
                    {'Unavailable From'}
                  </Text>
                  <View style={styles.dateTimeContainer}>
                    <Text variant="bodyMedium" style={styles.dateText}>
                      {values?.unavailabilityStartDate
                        ? moment(values?.unavailabilityStartDate).format(
                            'DD/MM/YYYY',
                          )
                        : 'DD/MM/YYYY'}
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setShowDatePicker(true);
                    setDatePickerType('unavailabilityEndDate');
                  }}
                  style={styles.datePickerContainer}>
                  <Text variant="bodyMedium" style={styles.title}>
                    {'Unavailable Till'}
                  </Text>
                  <View style={styles.dateTimeContainer}>
                    <Text variant="bodyMedium" style={styles.dateText}>
                      {values?.unavailabilityEndDate
                        ? moment(values?.unavailabilityEndDate).format(
                            'DD/MM/YYYY',
                          )
                        : 'DD/MM/YYYY'}
                    </Text>
                  </View>
                </TouchableOpacity>
                <View style={styles.row}>
                  <Checkbox.Android
                    status={values?.hideWorkingHours ? 'checked' : 'unchecked'}
                    onPress={() => {
                      setFieldValue(
                        'hideWorkingHours',
                        !values?.hideWorkingHours,
                      );
                    }}
                  />
                  <Text variant="bodyMedium" style={styles.flex1}>
                    {'Show/Hide Working hours'}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Checkbox.Android
                    status={values?.allowBulkOrder ? 'checked' : 'unchecked'}
                    onPress={() => {
                      setFieldValue('allowBulkOrder', !values?.allowBulkOrder);
                    }}
                  />
                  <Text variant="bodyMedium" style={styles.flex1}>
                    {'Allow Bulk Orders'}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Checkbox.Android
                    status={
                      values?.allowCategorisation ? 'checked' : 'unchecked'
                    }
                    onPress={() => {
                      setFieldValue(
                        'allowCategorisation',
                        !values?.allowCategorisation,
                      );
                    }}
                  />
                  <Text variant="bodyMedium" style={styles.flex1}>
                    {'Allow Categorisation'}
                  </Text>
                </View>
                <CustomButton
                  onPress={() => handleSubmit()}
                  title={STRINGS.update}
                  disabled={updateLoading}
                  loading={updateLoading}
                />
              </>
            );
          }}
        </Formik>
      </KeyboardAwareScrollView>
      <DatePicker
        modal
        mode="date"
        title={
          datePickerType == 'validTill'
            ? 'Select Start Date'
            : 'Select End Date'
        }
        open={showDatePicker}
        date={new Date()}
        onConfirm={date => {
          onConfirmDate(date);
        }}
        onCancel={() => {
          setShowDatePicker(false);
        }}
      />
    </SafeAreaView>
  );
};

export default CompanyUpdateScreen;

const styles = StyleSheet.create({
  buttonStyle: {marginTop: 0},
  description: {borderRadius: 20, height: 150},
  fileNameText: {
    color: colors.gray.medium,
    fontSize: 12,
    marginTop: 5,
  },
  firmDetailsUpload: {
    flex: 1,
  },
  helperText: {
    marginLeft: styleConstants.spacing.x36,
    marginVertical: styleConstants.spacing.x20,
  },
  image: {
    borderColor: colors.gray.light,
    borderRadius: 50,
    borderWidth: 1,
    height: 100,
    marginVertical: 10,
    width: 100,
  },
  imagePreview: {
    borderRadius: 5,
    height: 30,
    width: 30,
  },
  innerContainerStyle: {
    backgroundColor: 'transparent',
    justifyContent: 'center',
    margin: 0,
    padding: 0,
  },
  mainConatainer: {
    backgroundColor: colors.background,
    borderRadius: styleConstants.borderRadii.b10,
    flex: 1,
    margin: styleConstants.spacing.x20,
    padding: styleConstants.spacing.x20,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20,
  },
  safeAreaStyle: {
    padding: 0,
  },
  stepText: {
    marginLeft: styleConstants.spacing.x36,
  },
  logoContainer: {
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20,
    alignItems: 'center',
  },
  uploadContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: styleConstants.spacing.s10,
  },
  uploadLabel: {flex: 1},
  uploadText: {color: customColors.appBlue, textAlign: 'center', fontSize: 16},
  datePickerCloseButton: {
    alignSelf: 'flex-end',
    marginBottom: 15,
  },
  datePickerContainer: {
    marginBottom: 25,
  },
  dateText: {
    fontFamily: FONTS.regular,
    fontSize: 16,
  },
  dateTimeContainer: {
    borderColor: colors.gray.dark,
    borderRadius: 35,
    borderWidth: 1,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  title: {marginBottom: 5, marginLeft: 20},
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flex1: {
    flex: 1,
  },
});
