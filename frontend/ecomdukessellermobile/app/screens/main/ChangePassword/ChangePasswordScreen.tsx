import {<PERSON><PERSON>, SafeAreaView, StyleSheet, View} from 'react-native';
import React, {useState} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {colors} from '../../../theme/colors';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {MD3Colors, Text, TextInput} from 'react-native-paper';
import CustomTextInput from '../../../components/InputFields/Textinput';
import {
  useUpdatePasswordMutation,
  useUpdateSellerStatusMutation,
} from '../../../redux/auth/authApiSlice';
import {useFormik} from 'formik';
import {changePasswordValidationSchema} from '../../../validations/auth';
import {useTypedSelector} from '../../../redux/appstore';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import Toast from 'react-native-toast-message';
import {unsetCredentials} from '../../../redux/auth/authSlice';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {useDispatch} from 'react-redux';

type Props = {};
export enum SellerStatus {
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  INACTIVE = 'INACTIVE',
}
const ChangePasswordScreen = (props: Props) => {
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] =
    useState<boolean>(false);
  const [newPasswordVisible, setNewPasswordVisible] = useState<boolean>(false);
  const dispatch = useDispatch();
  const [updatePassword, {isLoading}] = useUpdatePasswordMutation();
  const [updateSellerStatus] = useUpdateSellerStatusMutation();

  const {userDetails} = useTypedSelector(state => state.auth);
  const signOutFromGoogle = async () => {
    try {
      const isSignedIn = await GoogleSignin.getCurrentUser(); // Check if the user is signed in

      if (isSignedIn) {
        await GoogleSignin.revokeAccess(); // Optionally revoke access (if required)
        await GoogleSignin.signOut(); // Sign out from Google
      } else {
        console.log('User is not signed in through Google');
      }
    } catch (error) {
      console.error('Error during Google sign-out: ', error);
    }
  };
  const _renderSeperator = () => {
    return (
      <View
        style={{
          borderBottomWidth: 1,
          borderBottomColor: customColors.borderGrey,
          marginBottom: styleConstants.spacing.l,
        }}
      />
    );
  };

  const formik = useFormik({
    initialValues: {
      old: '',
      password: '',
      confirm: '',
    },
    validationSchema: changePasswordValidationSchema,
    onSubmit: async () => {
      await updatePassword({
        username: userDetails?.username ?? '',
        oldPassword: formik.values.old,
        password: formik.values.password,
      }).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Password updated successfully',
      });
    },
  });
  const deletehandler = async () => {
    await updateSellerStatus({
      id: userDetails?.profileId ?? '',
      status: SellerStatus.INACTIVE,
    }).unwrap();
    Toast.show({
      type: 'error',
      text1: 'Account deactivated successfully',
    });

    dispatch(unsetCredentials());
    signOutFromGoogle();

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  };

  const onPressDeactivateAccount = async () => {
    Alert.alert(
      'Deactivate Account',
      `Are you sure you want to deactivate your account? You can reactivate anytime by logging in again. \n\nThis action is allowed only if all orders are fulfilled and the refund window is closed.`,
      [
        {
          text: 'Cancel',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'Confirm Deactivation',
          onPress: () => {
            deletehandler();
          },
        },
      ],
    );
  };

  return (
    <SafeAreaView style={styles.mainContainer}>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={styles.scrolllViewStyle}
        contentContainerStyle={styles.scrollviewContainer}>
        <View style={styles.card}>
          <Text variant="titleMedium" style={styles.header}>
            {'Change Password'}
          </Text>
          {_renderSeperator()}
          <CustomTextInput
            title="Current Password*"
            value={formik.values.old}
            onChangeText={formik.handleChange('old')}
            onBlur={formik.handleBlur('old')}
            touched={formik.touched.old}
            errors={formik.errors.old}
            secureTextEntry={!passwordVisible}
            right={
              <TextInput.Icon
                icon={!passwordVisible ? 'eye-off' : 'eye'}
                onPress={() => setPasswordVisible(!passwordVisible)}
              />
            }
          />
          <CustomTextInput
            title="New Password*"
            value={formik.values.password}
            onChangeText={formik.handleChange('password')}
            onBlur={formik.handleBlur('password')}
            touched={formik.touched.password}
            errors={formik.errors.password}
            secureTextEntry={!newPasswordVisible}
            right={
              <TextInput.Icon
                icon={!newPasswordVisible ? 'eye-off' : 'eye'}
                onPress={() => setNewPasswordVisible(!newPasswordVisible)}
              />
            }
          />
          <CustomTextInput
            title="Confirm Password*"
            value={formik.values.confirm}
            onChangeText={formik.handleChange('confirm')}
            onBlur={formik.handleBlur('confirm')}
            touched={formik.touched.confirm}
            errors={formik.errors.confirm}
            secureTextEntry={!confirmPasswordVisible}
            right={
              <TextInput.Icon
                icon={!confirmPasswordVisible ? 'eye-off' : 'eye'}
                onPress={() =>
                  setConfirmPasswordVisible(!confirmPasswordVisible)
                }
              />
            }
          />
          <CustomButton
            title="Update Password"
            disabled={isLoading}
            loading={isLoading}
            onPress={() => formik.handleSubmit()}
          />
        </View>
        <View
          style={[
            styles.card,
            {
              backgroundColor: 'rgb(245, 190, 190)',
              borderColor: 'rgb(231, 103, 103)',
              borderWidth: 1,
              marginTop: 25,
            },
          ]}>
          <Text
            variant="titleMedium"
            style={[
              styles.header,
              {
                color: MD3Colors.error50,
              },
            ]}>
            {'Account Deactivation'}
          </Text>
          {_renderSeperator()}
          <View
            style={{
              gap: 10,
            }}>
            <Text variant="bodyMedium">
              {`${'\u2022'} Your products will be blocked from listing and will no longer be visible to customers.`}
            </Text>
            <Text variant="bodyMedium">
              {`${'\u2022'} You can reactivate your account anytime by logging in again.`}
            </Text>
            <Text variant="bodyMedium">
              {`${'\u2022'} All your data and settings will be preserved during deactivation`}
            </Text>
            <CustomButton
              buttonStyle={{
                backgroundColor: MD3Colors.error50,
                marginTop: styleConstants.spacing.x20,
              }}
              title="Deactivate Account"
              onPress={() => onPressDeactivateAccount()}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ChangePasswordScreen;

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.surface,
    flex: 1,
    justifyContent: 'center',
  },
  scrolllViewStyle: {
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  scrollviewContainer: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    flexGrow: 1,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  header: {
    padding: styleConstants.spacing.x20,
    paddingTop: 0,
    fontSize: 18,
    paddingLeft: 0,
  },
});
