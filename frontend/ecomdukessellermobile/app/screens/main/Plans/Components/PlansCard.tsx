import {StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Card, Icon, RadioButton, Text} from 'react-native-paper';
import styleConstants from '../../../../theme/styleConstants';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';
import {PlanPricing} from '../../../../redux/plans/planApiSlice';

type Props = {
  title: string;
  onValueChange: Function;
  selectedValue: string;
  value: string;
  subtitle: string;
  features: {name: string; isAvailable: boolean; value: string | undefined}[];
  active: boolean;
  planPricings: PlanPricing[];
  currency: string;
};
const currencySymbols: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  INR: '₹',
};
const PlansCard = ({
  title = '',
  onValueChange,
  selectedValue,
  value,
  subtitle = '',
  features = [],
  active = true,
  planPricings = [],
  currency = '',
}: Props) => {
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const visibleFeatures = showAllFeatures ? features : features.slice(0, 3);
  return (
    <Card
      contentStyle={styles.cardContentStyle}
      style={[
        styles.cardContainer,
        selectedValue === value && styles.selectedCard,
      ]}>
      <View style={styles.headerRow}>
        <Text variant="labelLarge" style={styles.title}>
          {title}
        </Text>
        <View style={styles.statusRow}>
          {active && (
            <Text
              style={[
                styles.statusText,
                {
                  backgroundColor: colors.green.success,
                },
              ]}>
              Active
            </Text>
          )}
          <RadioButton.Android
            hitSlop={{
              left: 15,
              right: 15,
              top: 15,
              bottom: 15,
            }}
            value={value}
            status={selectedValue === value ? 'checked' : 'unchecked'}
            onPress={() => onValueChange(value)}
          />
        </View>
      </View>
      <View>
        {planPricings.map(pricing => (
          <View
            style={{
              marginTop: 10,
            }}>
            <Text variant="bodyMedium">
              {currencySymbols[currency] ?? currency} {pricing.price} for{' '}
              {pricing.minSalesThreshold}–{pricing.maxSalesThreshold} sales
            </Text>
          </View>
        ))}
      </View>
      <View
        style={{
          borderTopWidth: 0.3,
          marginTop: 15,
          borderTopColor: colors.primary,
          paddingTop: 10,
        }}>
        {visibleFeatures.map((feature, index) => (
          <View key={index} style={styles.featureRow}>
            <Text style={styles.featureText} variant="bodyMedium">
              {feature.name}
            </Text>
            {feature?.value === 'true' || feature?.value === 'false' ? (
              <Icon
                source={
                  feature.isAvailable
                    ? 'check-circle-outline'
                    : 'close-circle-outline'
                }
                size={18}
                color={
                  feature.isAvailable
                    ? colors.green.success
                    : customColors.error
                }
              />
            ) : (
              <Text variant="bodySmall">{feature?.value}</Text>
            )}
          </View>
        ))}

        {features.length > 3 && (
          <TouchableOpacity
            onPress={() => setShowAllFeatures(!showAllFeatures)}>
            <Text style={styles.seeMoreText}>
              {showAllFeatures ? 'See Less' : 'See More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );
};

export default PlansCard;

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.background,
    borderRadius: 10,
    marginBottom: styleConstants.spacing.x20,
  },
  cardContentStyle: {
    padding: styleConstants.spacing.s10,
  },
  featureRow: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: styleConstants.spacing.s,
    gap: 10,
  },
  featureText: {
    flex: 1,
    marginLeft: styleConstants.spacing.s10,
  },
  featuresContainer: {
    marginTop: styleConstants.spacing.s10,
  },
  headerRow: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  seeMoreText: {
    color: colors.primary,
    fontWeight: 'bold',
    marginTop: styleConstants.spacing.s,
    textAlign: 'left',
  },
  selectedCard: {
    borderColor: colors.primary,
    borderWidth: 1.5,
  },
  statusRow: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  statusText: {
    borderRadius: 10,
    color: customColors.white,
    fontWeight: 'bold',
    paddingHorizontal: 10,
    paddingVertical: 2,
  },
  title: {
    fontWeight: 'bold',
  },
});
