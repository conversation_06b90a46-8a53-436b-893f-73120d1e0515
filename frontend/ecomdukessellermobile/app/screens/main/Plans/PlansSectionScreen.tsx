import React, {useEffect, useState} from 'react';
import {FlatList, SafeAreaView, StyleSheet, View} from 'react-native';
import {Text} from 'react-native-paper';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import PlansCard from './Components/PlansCard';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import {
  useGetFeaturesQuery,
  useGetPlansQuery,
} from '../../../redux/plans/planApiSlice';
import {
  useCreateSubscriptionMutation,
  useGetSubscriptionQuery,
} from '../../../redux/ecom/ecomApiSclice';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import Toast from 'react-native-toast-message';
const PlansSectionScreen = () => {
  const {data: plans = []} = useGetPlansQuery();
  console.log('plans', plans);

  const {data: features = []} = useGetFeaturesQuery();
  const {data: user} = useGetUserQuery();
  const {data: subscription, refetch: refetchSubscription} =
    useGetSubscriptionQuery(user?.profileId ?? '', {
      skip: !user?.profileId,
    });
  const [createSubscriptionApi] = useCreateSubscriptionMutation();

  useEffect(() => {
    if (user?.profileId) {
      refetchSubscription();
    }
  }, [user?.profileId, refetchSubscription]);

  const activeSubscription = subscription?.find(sub => sub.status === 'ACTIVE');
  const activePlanId = activeSubscription?.planId;

  const [selectedPlan, setSelectedPlan] = useState<string>(
    activePlanId || plans[0]?.name || '',
  );

  useEffect(() => {
    if (activePlanId) {
      const activePlan = plans.find(plan => plan.id === activePlanId);
      if (activePlan) {
        setSelectedPlan(activePlan.name);
      }
    }
  }, [activePlanId, plans]);
  const handleChange = (newValue: string) => {
    setSelectedPlan(newValue);
  };
  const currentPlan = plans.find(plan => plan.name === selectedPlan);
  features.map(feature => {
    const matchedFeatureValue = currentPlan?.planFeatureValues?.find(
      fValue => fValue.featureValue?.feature?.id === feature.id,
    );

    return {
      name: feature.name,
      value: matchedFeatureValue?.featureValue?.value || 'N/A',
      isAvailable: matchedFeatureValue?.featureValue?.value === 'true',
    };
  });
  const handleUpgrade = async () => {
    if (!user?.profileId) return;

    const selectedPlanObj = plans.find(plan => plan.name === selectedPlan);
    if (!selectedPlanObj) return;

    await createSubscriptionApi({
      subscriberId: user.profileId,
      planId: selectedPlanObj.id,
    }).unwrap();
    refetchSubscription();
    Toast.show({
      type: 'success',
      text1: 'Plans Upgrade successfully',
    });
  };
  console.log('features plans', features);
  console.log('subscription plans', subscription);

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text variant="titleMedium" style={styles.titleText}>
          Upgrade your Plan
        </Text>
        <FlatList
          style={{
            flex: 1,
          }}
          data={plans}
          contentContainerStyle={styles.flatListContent}
          keyExtractor={item => item.id}
          renderItem={({item}) => (
            <PlansCard
              title={item.name}
              onValueChange={handleChange}
              selectedValue={selectedPlan}
              value={item.name}
              subtitle={`$${item.amount}`}
              planPricings={item?.planPricings}
              features={features.map(feature => {
                const featureValue = item.planFeatureValues.find(
                  fValue => fValue.featureValue?.feature?.id === feature.id,
                );

                return {
                  name: feature.name,
                  isAvailable: featureValue?.featureValue?.value === 'true',
                  value: featureValue?.featureValue?.value,
                };
              })}
              active={activePlanId === item.id}
              currency={item?.currency ?? ''}
            />
          )}
        />
        <CustomButton
          buttonStyle={styles.updatePlanButtonStyle}
          title="Update Plan"
          onPress={() => {
            handleUpgrade();
          }}
        />
      </View>
    </SafeAreaView>
  );
};
export default PlansSectionScreen;
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flatListContent: {
    padding: styleConstants.spacing.x20,
  },
  safeArea: {
    backgroundColor: colors.surface,
    flex: 1,
  },
  titleText: {
    color: customColors.appBlue,
    fontSize: 20,
    marginBottom: styleConstants.spacing.x20,
    padding: styleConstants.spacing.x20,
    paddingBottom: 0,
  },
  updatePlanButtonStyle: {
    margin: styleConstants.spacing.x20,
    marginBottom: 30,
  },
});
