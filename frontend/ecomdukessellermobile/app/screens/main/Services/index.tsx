import {SafeAreaView, StyleSheet, View} from 'react-native';
import React from 'react';
import customColors from '../../../theme/customColors';
import styleConstants from '../../../theme/styleConstants';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import CustomDropDown from '../../../components/InputFields/Dropdown';
import {
  useCreatePaymentLinkMutation,
  useCreateServiceRequestMutation,
  useGetServicesQuery,
} from '../../../redux/service-request/requestApislice';
import {useFormik} from 'formik';
import {Text} from 'react-native-paper';
import CustomTextInput from '../../../components/InputFields/Textinput';
import CustomButton from '../../../components/CustomButton/ContainedButton';

type Props = {};

const ServicesScreen = (props: Props) => {
  const {data: services, isLoading} = useGetServicesQuery();
  const [createRequest] = useCreateServiceRequestMutation();
  const [createPaymentLink] = useCreatePaymentLinkMutation();
  const formik = useFormik({
    initialValues: {
      ecomdukeserviceId: '',
      notes: '',
    },
    validate: values => {
      const errors: any = {};
      if (!values.ecomdukeserviceId) {
        errors.ecomdukeserviceId = 'Service is required';
      }
      return errors;
    },
    onSubmit: async (values, {resetForm}) => {
      await handleFormSubmit(values, resetForm);
    },
  });

  const handleFormSubmit = async (values: any, resetForm: any) => {
    const selectedService = services?.find(
      s => s.id === values.ecomdukeserviceId,
    );
    if (!selectedService) {
      return;
    }
    const payload = {
      ecomdukeserviceId: values.ecomdukeserviceId,
      notes: values.notes,
      paidAmount: Number(selectedService?.price),
      status: 'PENDING',
    };
    const request = await createRequest(payload).unwrap();

    if (!request?.id) {
      return;
    }
    const paymentResponse = await createPaymentLink({
      linkAmount: Number(selectedService.price),
      linkNotes: {
        ecomdukeserviceRequestId: request.id,
      },
    }).unwrap();
    if (paymentResponse?.link_url) {
      // window.open(paymentResponse.link_url, '_blank');
    }

    resetForm();
  };

  const selectedService = services?.find(
    s => s.id === formik.values.ecomdukeserviceId,
  );

  return (
    <SafeAreaView style={styles.screenContainer}>
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollViewContainer}>
        <CustomDropDown
          title={'Select a Service'}
          value={formik.values.ecomdukeserviceId}
          //   touched={formik.touched.type}
          //   errors={formik.errors.type}
          data={Array.isArray(services) ? services : []}
          labelField="name"
          placeholder="Choose Service"
          valueField="id"
          onChange={(item: any) => {
            formik.setFieldValue('ecomdukeserviceId', item?.id);
          }}
          onRemove={() => {
            formik.setFieldValue('ecomdukeserviceId', '');
          }}
          onDropdownFocus={() => {
            formik.setFieldTouched('ecomdukeserviceId');
          }}
          onDropdownBlur={() => {
            formik.handleBlur('ecomdukeserviceId');
          }}
        />
        {selectedService && (
          <Text variant="labelLarge">
            {`Payable Amount: ₹${selectedService?.price}`}
          </Text>
        )}
        <CustomTextInput
          title={'Notes'}
          placeholder="Enter Notes"
          onChangeText={formik.handleChange('notes')}
          value={formik.values.notes?.toString()}
          onBlur={formik.handleBlur('notes')}
          touched={formik.touched.notes}
          errors={formik.errors.notes}
          multiline={true}
          containerStyle={{marginTop: 20}}
          textInputStyle={{
            height: 150,
          }}
        />
        <CustomButton
          title={'Submit Request'}
          onPress={() => formik.handleSubmit()}
        />
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ServicesScreen;

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  scrollViewContainer: {
    backgroundColor: customColors.white,
    padding: styleConstants.spacing.x20,
    paddingBottom: 100,
  },
});
