import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>reaView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {
  useGetUserNotificationsCountQuery,
  useGetUserNotificationsQuery,
  useMarkAllNotificationsAsReadMutation,
  useMarkNotificationAsReadMutation,
} from '../../../redux/notification/userNotificationApiSlice';
import {useTypedSelector} from '../../../redux/appstore';
import customColors from '../../../theme/customColors';
import {Text} from 'react-native-paper';
import styleConstants from '../../../theme/styleConstants';
import {colors} from '../../../theme/colors';
import {UserNotification} from '../../../types/user-notification';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import {subscribeToTopic} from '@react-native-firebase/messaging';
import Toast from 'react-native-toast-message';
import {useSubscribeToTopicMutation} from '../../../redux/subscription/subscriptionApiSlice';

type Props = {};

const NotificationScreen = (props: Props) => {
  const {userDetails} = useTypedSelector(state => state.auth);
  const userTenantId = userDetails?.userTenantId;
  const [subscriptionDialogOpen, setSubscriptionDialogOpen] = useState(false);
  const [selectedNotification, setSelectedNotification] =
    useState<UserNotification | null>(null);
  const {
    data: notifications = [],
    isLoading: notificationsLoading,
    refetch: refetchNotifications,
  } = useGetUserNotificationsQuery(
    {
      userTenantId: userTenantId!,
      filter: {
        order: ['createdOn DESC'],
        limit: 20,
      },
    },
    {
      skip: !userTenantId,
    },
  );
  // Get unread count
  const {data: unreadCountData, refetch: refetchUnreadCount} =
    useGetUserNotificationsCountQuery(
      {userTenantId: userTenantId!, isRead: false},
      {
        skip: !userTenantId,
      },
    );

  const [markAsRead] = useMarkNotificationAsReadMutation();
  const [markAllAsRead] = useMarkAllNotificationsAsReadMutation();
  const [subscribeToTopic, {isLoading: isSubscribing}] =
    useSubscribeToTopicMutation();

  const unreadCount = unreadCountData?.count || 0;
  const handleMarkAllAsRead = async () => {
    if (!userTenantId) return;

    try {
      await markAllAsRead(userTenantId).unwrap();
      // Refresh both notifications and unread count
      refetchNotifications();
      refetchUnreadCount();
    } catch {
      // Failed to mark all notifications as read
    }
  };

  const handleNotificationClick = (notification: UserNotification) => {
    if (!notification.isRead) {
      setSelectedNotification(notification);
      setSubscriptionDialogOpen(true);
    }
  };
  const handleSubscribe = async () => {
    if (!selectedNotification || !userTenantId) return;

    try {
      await subscribeToTopic({
        topic: selectedNotification.topicId,
        // fcmToken: fcmToken || undefined,
        fcmToken: undefined,
        listKey: selectedNotification.listKey,
        groupId: selectedNotification.groupId,
        userTenantId: userTenantId,
      }).unwrap();

      // Mark notification as read after successful subscription
      if (selectedNotification.id) {
        await markAsRead(selectedNotification.id).unwrap();
      }
      Toast.show({
        type: 'success',
        text1: 'Successfully subscribed to notifications!',
      });
      // Show success message
      // Close dialog and refresh data
      setSubscriptionDialogOpen(false);
      setSelectedNotification(null);
      refetchNotifications();
      refetchUnreadCount();
    } catch {
      Toast.show({
        type: 'error',
        text1: 'Failed to subscribe. Please try again.',
      });
    }
  };

  const handleMarkAsReadOnly = async () => {
    if (!selectedNotification?.id) return;

    try {
      await markAsRead(selectedNotification.id).unwrap();

      // Close dialog and refresh data
      setSubscriptionDialogOpen(false);
      setSelectedNotification(null);
      refetchNotifications();
      refetchUnreadCount();
    } catch {
      Toast.show({
        type: 'error',
        text1: 'Failed to mark as read. Please try again.',
      });
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      ' ' +
      date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})
    );
  };
  return (
    <SafeAreaView style={styles.screenContainer}>
      <FlatList
        data={notifications}
        ListHeaderComponent={() => {
          return (
            <View
              style={{
                margin: styleConstants.spacing.x20,
                marginBottom: 0,
                paddingBottom: styleConstants.spacing.x20,
                backgroundColor: customColors.white,
                borderBottomWidth: 1,
                borderBottomColor: customColors.borderGrey,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Text variant={'titleLarge'}>Notifications</Text>
              {unreadCount > 0 && (
                <TouchableOpacity onPress={handleMarkAllAsRead}>
                  <Text
                    style={{
                      color: colors.primary,
                    }}
                    variant="titleMedium">
                    Mark All as Read
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          );
        }}
        renderItem={({item, index}) => {
          return (
            <TouchableOpacity
              onPress={() => {
                handleNotificationClick(item);
              }}
              disabled={item.isRead}
              style={{
                backgroundColor: item.isRead
                  ? 'transparent'
                  : 'rgba(0, 0, 0, 0.04)',
                borderBottomWidth: 1,
                borderBottomColor: customColors.borderGrey,
                padding: styleConstants.spacing.x20,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  gap: styleConstants.spacing.s10,
                  alignItems: 'flex-start',
                  width: '90%',
                }}>
                <Text variant={!item.isRead ? 'labelLarge' : 'bodyMedium'}>
                  {item.notification?.subject ?? 'Notification'}
                </Text>
                <Text variant={'bodyMedium'}>
                  {item.notification?.body ?? 'No content'}
                </Text>
                <Text variant={'bodyMedium'}>{formatDate(item.createdOn)}</Text>
              </View>
              {!item.isRead && (
                <View
                  style={{
                    height: 15,
                    width: 15,
                    borderRadius: 7.5,
                    backgroundColor: colors.primary,
                  }}
                />
              )}
            </TouchableOpacity>
          );
        }}
      />
      <Modal
        animationType="slide"
        transparent={true}
        visible={subscriptionDialogOpen}
        onRequestClose={() => {
          setSubscriptionDialogOpen(!subscriptionDialogOpen);
        }}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text
              variant="titleMedium"
              style={{
                marginBottom: 10,
                borderBottomColor: customColors.borderGrey,
                borderBottomWidth: 1,
                paddingBottom: 10,
              }}>
              Notification Subscription
            </Text>
            <Text variant={'titleMedium'}>
              {selectedNotification?.notification?.subject ?? 'Notification'}
            </Text>
            <Text variant={'bodyLarge'}>
              {selectedNotification?.notification?.body ??
                'No content available'}
            </Text>
            <View
              style={{
                borderWidth: 1,
                borderColor: customColors.borderGrey,
                padding: 10,
                borderRadius: 10,
                gap: 10,
              }}>
              <Text variant="bodyMedium">
                Would you like to subscribe to receive updates and latest
                information about this topic?
              </Text>
              <Text
                style={{color: customColors.textLightGrey}}
                variant="bodyMedium">
                By subscribing, you&apos;ll receive notifications about similar
                topics and stay updated with the latest information.
              </Text>
            </View>
            <View>
              <CustomButton
                mode="outlined"
                title={'No, Just Mark as Read'}
                onPress={() => {
                  handleMarkAsReadOnly();
                }}
              />
              <CustomButton
                title={'Yes, Subscribe'}
                onPress={() => {
                  handleSubscribe();
                }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default NotificationScreen;

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalView: {
    margin: 10,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '85%',
    gap: 10,
  },
});
