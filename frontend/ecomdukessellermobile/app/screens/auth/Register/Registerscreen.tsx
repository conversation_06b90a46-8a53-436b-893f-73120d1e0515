import React, {Dispatch, SetStateAction, useRef, useState} from 'react';
import {
  KeyboardTypeOptions,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import {
  Checkbox,
  Divider,
  HelperText,
  Text,
  TextInput,
} from 'react-native-paper';
import {Formik, FormikHelpers} from 'formik';
import styleConstants from '../../../theme/styleConstants';
import {registerSchema} from '../../../validations';
import Screenwrapper from '../../../components/ScreenWrapper/Screenwrapper';
import CustomTextInput from '../../../components/InputFields/Textinput';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import customColors from '../../../theme/customColors';
import {SCREEN_NAME, STRINGS} from '../../../constants';
import {
  ILoginForm,
  useCreateSignUpTokenMutation,
  useExchangeTokenMutation,
  useLazyGetUserQuery,
  useLoginMutation,
  useSignUpMutation,
} from '../../../redux/auth/authApiSlice';
import {colors} from '../../../theme/colors';
import {RegisterScreenNavigationProp} from '../../../navigations/types';
import PhoneInput from 'react-native-phone-number-input';
import Toast from 'react-native-toast-message';
import {FONTS} from '../../../theme/fonts';
import {commonStyles, scrollViewProps} from '../../../constants/commonstyles';
import {setCredentials, setUserDetails} from '../../../redux/auth/authSlice';
import {useDispatch} from 'react-redux';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';

interface FormValues {
  lastName: string;
  firstName: string;
  phone: string;
  email: string;
  password: string;
  confirmPassword: string;
  subscribeToNewsletter: boolean;
}

interface FormField {
  name: string;
  label: string;
  placeholder: string;
  keyboardType?: KeyboardTypeOptions;
  secure?: boolean;
  visibility?: boolean;
  setVisibility?: Dispatch<SetStateAction<boolean>>;
  isPhone?: boolean;
}
interface ScreenProps {
  navigation: RegisterScreenNavigationProp;
}

const RegisterScreen: React.FC<ScreenProps> = ({navigation}) => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const phoneInput = useRef<PhoneInput>(null);
  const [isAgreed, setIsAgreed] = useState(false);

  const [createSignUpToken] = useCreateSignUpTokenMutation();

  const [signUp, {isLoading: signupLoading}] = useSignUpMutation();
  const formFields: FormField[] = [
    {
      name: 'firstName',
      label: '* First Name',
      placeholder: 'First Name',
      keyboardType: 'default',
    },
    {
      name: 'lastName',
      label: '* Last Name',
      placeholder: 'Last Name',
      keyboardType: 'default',
    },

    {
      name: 'phone',
      label: 'Phone Number',
      placeholder: 'Phone',
      keyboardType: 'phone-pad',
      isPhone: true,
    },
    {
      name: 'email',
      label: '* Email',
      placeholder: 'Email',
      keyboardType: 'email-address',
    },
    {
      name: 'password',
      label: '* Password',
      placeholder: 'Password',
      secure: true,
      visibility: passwordVisible,
      setVisibility: setPasswordVisible,
    },
    {
      name: 'confirmPassword',
      label: '* Confirm Password',
      placeholder: 'Confirm password',
      secure: true,
      visibility: confirmPasswordVisible,
      setVisibility: setConfirmPasswordVisible,
    },
  ] as const;

  const handleRegister = async (
    values: FormValues,
    actions: FormikHelpers<FormValues>,
  ) => {
    const {resetForm} = actions;
    try {
      const tokenResponse = await createSignUpToken(values.email).unwrap();
      const token = tokenResponse.code;
      const signupData = {
        firstName: values.firstName,
        lastName: values.lastName,
        phoneNumber: values.phone,
        email: values.email,
        password: values.password,
      };
      await signUp({
        token,
        signupData: {
          ...signupData,
          phoneNumber: signupData.phoneNumber.replace('+', ''),
        },
      }).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Registered successfully!',
      });
      navigation.replace(SCREEN_NAME.REGISTRATION_SUCCESS, {
        username: values.email,
        password: values.password,
      });
      // navigation.pop();
      resetForm();
    } catch (error) {}
  };

  return (
    <SafeAreaView style={styles.screenContainer}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.ScrollView}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets={true}
        style={styles.scrollViewContainer}>
        <View style={commonStyles.mainContainer}>
          <Text variant="titleMedium" style={styles.heading}>
            {STRINGS.registration}
          </Text>
          <Formik
            initialValues={{
              firstName: '',
              lastName: '',
              phone: '',
              email: '',
              password: '',
              confirmPassword: '',
              subscribeToNewsletter: false,
            }}
            validationSchema={registerSchema}
            onSubmit={handleRegister}>
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              setFieldTouched,
              setFieldValue,
            }) => (
              <>
                {formFields.map(field =>
                  field.isPhone ? (
                    <View key={field?.name} style={styles.phoneInputView}>
                      <Text
                        variant="labelMedium"
                        style={styles.phoneNumberTitle}>
                        * Phone Number
                      </Text>
                      <PhoneInput
                        disableArrowIcon
                        ref={phoneInput}
                        placeholder=" "
                        defaultValue={values.phone}
                        defaultCode="IN"
                        layout="second"
                        onChangeFormattedText={text => {
                          handleChange('phone')(text);
                          setFieldTouched('phone', true);
                        }}
                        containerStyle={styles.phoneContainer}
                        textContainerStyle={styles.phoneTextContainer}
                        codeTextStyle={styles.codeText}
                        textInputStyle={styles.codeText}
                        flagButtonStyle={styles.flagButton}
                      />

                      <HelperText
                        style={styles.helperText}
                        type="error"
                        padding="none"
                        visible={Boolean(
                          touched.phone &&
                            errors.phone &&
                            touched[field.name as keyof FormValues] &&
                            (errors[field.name as keyof FormValues]?.length ??
                              0) > 0,
                        )}>
                        {errors[field.name as keyof FormValues]}
                      </HelperText>
                    </View>
                  ) : (
                    <CustomTextInput
                      keyboardType={
                        field?.keyboardType ? field?.keyboardType : 'default'
                      }
                      key={field?.name}
                      id={field?.name}
                      title={field.label}
                      placeholder={field.placeholder}
                      value={values[field.name as keyof FormValues]}
                      onChangeText={handleChange(field.name)}
                      onBlur={handleBlur(field.name)}
                      errors={errors[field.name as keyof FormValues]}
                      touched={touched[field.name as keyof FormValues]}
                      secureTextEntry={field.secure ? !field.visibility : false}
                      right={
                        field.secure ? (
                          <TextInput.Icon
                            icon={field.visibility ? 'eye' : 'eye-off'}
                            onPress={() =>
                              field.setVisibility?.(!field.visibility)
                            }
                          />
                        ) : null
                      }
                    />
                  ),
                )}
                <View style={[styles.row, {marginBottom: 15}]}>
                  <Checkbox.Android
                    status={
                      values?.subscribeToNewsletter ? 'checked' : 'unchecked'
                    }
                    onPress={() => {
                      setFieldValue(
                        'subscribeToNewsletter',
                        !values?.subscribeToNewsletter,
                      );
                    }}
                  />
                  <Text variant="labelMedium" style={styles.flex1}>
                    {
                      'Keep me informed with the latest news, new products, deals, and updates'
                    }
                  </Text>
                </View>
                <View style={styles.row}>
                  <Checkbox.Android
                    status={isAgreed ? 'checked' : 'unchecked'}
                    onPress={() => {
                      setIsAgreed(!isAgreed);
                    }}
                  />
                  <Text variant="labelMedium" style={styles.flex1}>
                    {'I agree to the '}
                    <Text
                      onPress={() => {
                        navigation.push(SCREEN_NAME.TERMS_AND_CONDITION);
                      }}
                      variant="labelMedium"
                      style={styles.termsConditionText}>
                      {'Terms & Conditions'}
                    </Text>
                    {' and '}
                    <Text
                      onPress={() => {
                        navigation.push(SCREEN_NAME.PRIVACY_POLICY);
                      }}
                      variant="labelMedium"
                      style={styles.termsConditionText}>
                      {'Privacy Policy'}
                    </Text>
                  </Text>
                </View>
                <CustomButton
                  title="Register"
                  mode="contained"
                  contentStyle={styles.ContinueButtonContent}
                  style={styles.button}
                  onPress={() => handleSubmit()}
                  loading={signupLoading}
                  disabled={!isAgreed || signupLoading}
                />
              </>
            )}
          </Formik>
          <Divider style={styles.divider} />
          <CustomButton
            title={STRINGS.haveAnAccountMsg}
            onPress={() => navigation.pop()}
            textColor={customColors.textButtonBlue}
            style={styles.cantLogin}
            mode="text"
          />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default RegisterScreen;

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: colors.surface,
    flex: 1,
  },
  scrollViewContainer: {
    backgroundColor: colors.background,
    borderRadius: styleConstants.borderRadii.b10,
    margin: styleConstants.spacing.x20,
  },
  ContinueButtonContent: {
    paddingHorizontal: styleConstants.spacing.x20,
  },
  ScrollView: {flexGrow: 1},
  button: {
    alignSelf: 'center',
    marginTop: 20,
    paddingVertical: 5,
    width: 200,
  },
  buttonLabel: {
    fontSize: 16,
  },
  cantLogin: {
    marginTop: styleConstants.spacing.s10,
  },
  card: {
    backgroundColor: colors.onPrimary,
    borderRadius: 20,
    elevation: 3,
    flex: 1,
    justifyContent: 'center',
  },
  codeText: {
    fontFamily: FONTS.regular,
    fontSize: 14,
  },
  container: {
    backgroundColor: colors.surface,
    flex: 1,
    padding: 30,
  },
  divider: {
    alignSelf: 'center',
    height: 2,
    marginTop: styleConstants.spacing.x20,
    width: '80%',
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginLeft: 5,
    marginTop: 5,
  },
  flagButton: {
    backgroundColor: customColors.borderGrey,
    borderBottomLeftRadius: 20,
    borderTopLeftRadius: 20,
    marginRight: styleConstants.spacing.s10,
  },
  heading: {
    marginVertical: '5%',
    textAlign: 'center',
  },
  helperText: {
    marginLeft: 20,
  },
  innerContainerStyle: {
    backgroundColor: 'transparent',
    justifyContent: 'center',
    margin: 0,
    padding: 0,
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    color: colors.gray.dark,
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    marginLeft: 19,
  },
  linkButton: {
    alignSelf: 'center',
    marginTop: 5,
  },
  mainConatainer: {
    backgroundColor: colors.background,
    borderRadius: styleConstants.borderRadii.b10,
    margin: styleConstants.spacing.x20,
    padding: styleConstants.spacing.x20,
  },
  orText: {alignSelf: 'center', marginTop: styleConstants.spacing.x20},
  phoneContainer: {
    backgroundColor: colors.onPrimary,
    borderColor: colors.navy.deep,
    borderRadius: 20,
    borderWidth: 1,
    height: 40,
    width: '100%',
  },
  phoneInputView: {width: '100%'},
  phoneNumberTitle: {marginBottom: 5, marginLeft: styleConstants.spacing.x20},
  phoneTextContainer: {
    backgroundColor: colors.onPrimary,
    borderRadius: 20,
    marginLeft: 0,
    paddingHorizontal: 0,
    paddingLeft: 0,
    paddingVertical: 0,
  },
  safeArea: {
    backgroundColor: colors.surface,
    flex: 1,
  },
  safeAreaStyle: {
    padding: 0,
  },
  socialButton: {
    borderColor: colors.outlineVariant,
    marginTop: 5,
  },
  socialIcon: {height: 20, marginRight: 8, width: 20},
  stepText: {
    marginLeft: styleConstants.spacing.x36,
  },
  textInput: {
    borderRadius: 8,
    height: 45,
  },
  termsConditionText: {
    color: '#0000EE',
  },
  flex1: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
