import React, {useCallback, useRef, useState} from 'react';
import {
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {TextInput, Text, Divider} from 'react-native-paper';
import {Formik, FormikHelpers, FormikProps} from 'formik';
import CustomTextInput from '../../../components/InputFields/Textinput';
import Screenwrapper from '../../../components/ScreenWrapper/Screenwrapper';
import {STRINGS} from '../../../constants/strings';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import {loginValidationSchema} from '../../../validations/auth';
import customColors from '../../../theme/customColors';
import styleConstants from '../../../theme/styleConstants';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {
  ILoginForm,
  useExchangeTokenMutation,
  useGoogleAuthCallbackMutation,
  useLazyGetUserQuery,
  useLoginMutation,
} from '../../../redux/auth/authApiSlice';
import {LoginScreenNavigationProp} from '../../../navigations/types';
import {useDispatch} from 'react-redux';
import {
  AuthResData,
  setCredentials,
  setUserDetails,
} from '../../../redux/auth/authSlice';
import {commonStyles, scrollViewProps} from '../../../constants/commonstyles';
import {
  GoogleSignin,
  isErrorWithCode,
  isSuccessResponse,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {useFocusEffect} from '@react-navigation/native';
import {Images} from '../../../assets/images';
import {colors} from '../../../theme/colors';
interface ScreenProps {
  navigation: LoginScreenNavigationProp;
}

const LoginScreen: React.FC<ScreenProps> = ({navigation}) => {
  const dispatch = useDispatch();

  // Hooks for handling api error
  // const handleError = useApiErrorHandler();

  // states
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);

  // useref
  const formikRef = useRef<FormikProps<ILoginForm>>(null);

  // loginapi
  const [loginApi] = useLoginMutation();
  const [googleLoginApi] = useGoogleAuthCallbackMutation();
  // token exchange api
  const [exchangeToken] = useExchangeTokenMutation();

  // user details api
  const [getUserDetails] = useLazyGetUserQuery();

  // login function
  const handleLogin = async (
    values: ILoginForm,
    actions: FormikHelpers<ILoginForm>,
  ) => {
    const {setSubmitting, resetForm} = actions;

    try {
      const response = await loginApi(values).unwrap(); // ✅ Unwrap ensures promise rejection is caught
      const {code} = response;

      const tokenResponse = await exchangeToken({code}).unwrap();
      if (tokenResponse) {
        dispatch(setCredentials(tokenResponse));
        const userDetails = await getUserDetails().unwrap();
        dispatch(setUserDetails(userDetails));
        resetForm();
      }
      setSubmitting(false);
    } catch (error) {
      setSubmitting(false);
    } finally {
      setSubmitting(false);
    }
  };

  // google login function
  const signInWithGoogle = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      if (isSuccessResponse(response)) {
        const tokens = await GoogleSignin.getTokens();
        const loginResponse = await googleLoginApi({code: tokens.accessToken});
        const token = loginResponse.data;
        if (token) {
          dispatch(setCredentials(token as AuthResData));
        }

        const userDetails = await getUserDetails().unwrap();
        dispatch(setUserDetails(userDetails));
      }
    } catch (error) {
      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.IN_PROGRESS:
            // operation (eg. sign in) already in progress
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            // Android only, play services not available or outdated
            break;
          default:
          // some other error happened
        }
      } else {
        // an error that's not related to google sign in occurred
      }
    }
  };

  const signInWithFacebook = () => {};

  useFocusEffect(
    useCallback(() => {
      return () => {
        formikRef?.current?.resetForm(); // ✅ Now it works fine!
        setPasswordVisible(false);
      };
    }, []),
  );
  return (
    <Screenwrapper>
      <ScrollView {...scrollViewProps}>
        <View style={commonStyles.mainContainer}>
          <Text variant="titleMedium" style={styles.titleText}>
            {STRINGS.logintoyourAccount}
          </Text>

          <Formik
            innerRef={formikRef}
            initialValues={{
              username: '<EMAIL>',
              password: 'Test@123',
            }}
            validationSchema={loginValidationSchema}
            onSubmit={(values, actions) => handleLogin(values, actions)}>
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              isSubmitting,
            }) => (
              <>
                <CustomTextInput
                  title={STRINGS.emailId}
                  value={values.username}
                  onChangeText={handleChange('username')}
                  onBlur={handleBlur('username')}
                  touched={touched.username}
                  errors={errors.username}
                />
                <CustomTextInput
                  value={values.password}
                  title={STRINGS.password}
                  onChangeText={handleChange('password')}
                  onBlur={handleBlur('password')}
                  secureTextEntry={!passwordVisible}
                  touched={touched.password}
                  errors={errors.password}
                  right={
                    <TextInput.Icon
                      icon={!passwordVisible ? 'eye-off' : 'eye'}
                      onPress={() => setPasswordVisible(!passwordVisible)}
                    />
                  }
                />

                <CustomButton
                  style={styles.forgotPassword}
                  onPress={() => {
                    navigation.push(SCREEN_NAME.FORGOT_PASSWORD);
                  }}
                  title={STRINGS.forgotPassword}
                  mode="text"
                  textColor={customColors.textLightGrey}
                />

                <CustomButton
                  title={STRINGS.submit}
                  mode="contained"
                  onPress={() => handleSubmit()}
                  style={styles.submitButton}
                  contentStyle={styles.submitButtonContent}
                  disabled={isSubmitting}
                  loading={isSubmitting}
                />
              </>
            )}
          </Formik>
          <Text style={styles.orText} variant="bodySmall">
            {STRINGS.or}
          </Text>
          <TouchableOpacity
            onPress={() => {
              signInWithGoogle();
            }}
            style={styles.socialLoginButton}>
            <Image source={Images.google} style={styles.socialIcon} />
            <Text variant="titleSmall" style={styles.socialButtonText}>
              {STRINGS.continuewithGoogle}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              signInWithGoogle();
            }}
            style={styles.socialLoginButton}>
            <Image source={Images.facebook} style={styles.socialIcon} />
            <Text variant="titleSmall" style={styles.socialButtonText}>
              {STRINGS.continuewithFacebook}
            </Text>
          </TouchableOpacity>

          <Divider style={styles.divider} />
          <CustomButton
            style={styles.cantLogin}
            onPress={() => {
              navigation.push(SCREEN_NAME.REGISTER);
            }}
            title={STRINGS.CantLogin}
            mode="text"
            textColor={customColors.textButtonBlue}
          />
        </View>
      </ScrollView>
    </Screenwrapper>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  cantLogin: {
    marginTop: styleConstants.spacing.s10,
  },
  divider: {
    alignSelf: 'center',
    height: 2,
    marginTop: styleConstants.spacing.x20,
    width: '80%',
  },
  forgotPassword: {
    alignSelf: 'flex-start',
  },
  orText: {alignSelf: 'center', marginTop: styleConstants.spacing.x20},
  submitButton: {
    alignSelf: 'center',
    marginTop: styleConstants.spacing.x36,
  },
  submitButtonContent: {
    paddingHorizontal: styleConstants.spacing.x20,
  },
  titleText: {
    marginBottom: styleConstants.spacing.x36,
    textAlign: 'center',
  },
  socialLoginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.gray.dark,
    borderWidth: 1,
    padding: 10,
    borderRadius: 20,
    marginTop: 20,
  },
  socialIcon: {
    height: 20,
    width: 20,
    marginRight: 10,
  },
  socialButtonText: {
    color: customColors.outlineButtonTextGrey,
  },
});
