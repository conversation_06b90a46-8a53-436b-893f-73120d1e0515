import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useMemo, useRef, useState} from 'react';
import {STRINGS} from '../../constants/strings';
import {Icon, MD3Colors, Text} from 'react-native-paper';
import CustomTextInput from '../../components/InputFields/Textinput';
import {Formik, FormikHelpers} from 'formik';
import styleConstants from '../../theme/styleConstants';

import CustomButton from '../../components/CustomButton/ContainedButton';
import BottomSheet, {BottomSheetView} from '@gorhom/bottom-sheet';
import {colors} from '../../theme/colors';
import ImageCropPicker from 'react-native-image-crop-picker';
import customColors from '../../theme/customColors';
import {useLazyGetUserQuery} from '../../redux/auth/authApiSlice';
import {SellerFormScreenNavigationProp} from '../../navigations/types';
import {SCREEN_NAME} from '../../constants';
import Toast from 'react-native-toast-message';
import {useDispatch} from 'react-redux';
import {setUserDetails} from '../../redux/auth/authSlice';
import {SampleProduct, StoreCreateDto} from '../../types/onboard';
import {useSellerOnboardMutation} from '../../redux/store/storeApiSlice';
import {sellerFormValidation} from '../../validations/auth';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';

interface ScreenProps {
  navigation: SellerFormScreenNavigationProp;
}

const SellerFormScreen: React.FC<ScreenProps> = ({navigation}) => {
  const [product, setProduct] = useState<SampleProduct>({
    productName: '',
    images: [],
  });
  const [sampleProducts, setSampleProducts] = useState<SampleProduct[]>([]);

  const [sellerOnboard, {isLoading: sellerOnboardLoading}] =
    useSellerOnboardMutation();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);
  const dispatch = useDispatch();
  const [getUserDetails] = useLazyGetUserQuery();

  const handleSnapPress = useCallback((index: number) => {
    bottomSheetRef.current?.snapToIndex(index);
  }, []);

  const handleImagePicker = () => {
    ImageCropPicker.openPicker({
      multiple: true,
    }).then(imgs => {
      const duplicateImages = [...product.images, ...imgs];
      setProduct(prevValue => ({...prevValue, images: duplicateImages}));
    });
  };
  const handleDeleteImage = (index: number) => {
    setProduct(prevValue => ({
      ...prevValue,
      images: prevValue.images.filter((_, i) => i !== index),
    }));
  };

  const handleSampleProducts = (index: number) => {
    setSampleProducts(preValue => preValue.filter((_, i) => i !== index));
  };

  const onSaveProducts = useCallback(() => {
    setSampleProducts([...sampleProducts, product]);
    setProduct({
      productName: '',
      images: [],
    });
    bottomSheetRef?.current?.close();
  }, [product, sampleProducts]);

  const handleSubmitSeller = async (
    values: StoreCreateDto,
    actions: FormikHelpers<StoreCreateDto>,
  ) => {
    const {resetForm} = actions;
    if (!sampleProducts?.length) {
      Toast.show({
        type: 'error',
        text1: 'Please add atleast on sample product',
      });
      return;
    }

    await sellerOnboard({
      sellerOnboardData: {
        ...values,
        fbId: values.fbId,
        instaId: values.instaId,
        website: values.website,
        sampleProductImages: sampleProducts,
      },
    }).unwrap();
    const userDetail = await getUserDetails().unwrap();
    dispatch(setUserDetails(userDetail));
    Toast.show({
      type: 'success',
      text1: 'Registration successfully!',
    });
    navigation.replace(SCREEN_NAME.PRE_VERIFY);

    resetForm();
    setSampleProducts([]);
  };
  return (
    <SafeAreaView
      style={{
        backgroundColor: colors.surface,
        flex: 1,
        justifyContent: 'center',
      }}>
      <View>
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            margin: styleConstants.spacing.x20,
            borderRadius: styleConstants.borderRadii.b10,
          }}
          contentContainerStyle={{
            backgroundColor: customColors.white,
            borderRadius: styleConstants.borderRadii.b10,
            padding: styleConstants.spacing.x20,
            flexGrow: 1,
          }}>
          <Text variant="titleMedium" style={styles.title}>
            {STRINGS.registration}
          </Text>
          <Formik<StoreCreateDto>
            initialValues={{
              fbId: '',
              instaId: '',
              website: '',
              sampleProductImages: [],
            }}
            validationSchema={sellerFormValidation}
            onSubmit={handleSubmitSeller}>
            {({
              handleChange,
              handleBlur,
              values,
              errors,
              touched,
              setFieldTouched,
              handleSubmit,
            }) => (
              <>
                <CustomTextInput
                  title={'Facebook Store Link'}
                  value={values.fbId}
                  onChangeText={handleChange('fbId')}
                  onBlur={() => {
                    setFieldTouched('fbId', true);
                    handleBlur('fbId');
                  }}
                  touched={touched.fbId}
                  errors={errors.fbId}
                />
                <CustomTextInput
                  title={'Instagram Store Link'}
                  value={values.instaId}
                  onChangeText={handleChange('instaId')}
                  onBlur={() => {
                    setFieldTouched('instaId', true);
                    handleBlur('instaId');
                  }}
                  touched={touched.instaId}
                  errors={errors.instaId}
                />
                <CustomTextInput
                  title={STRINGS.website}
                  value={values.website}
                  onChangeText={handleChange('website')}
                  onBlur={() => {
                    setFieldTouched('website', true);
                    handleBlur('website');
                  }}
                  touched={touched.website}
                  errors={errors.website}
                />

                <View style={styles.uploadSampleImage}>
                  <Text variant="labelLarge">
                    {STRINGS.uploadSampleProducts}
                  </Text>
                  <CustomButton
                    onPress={() => {
                      handleSnapPress(2);
                    }}
                    title={STRINGS.add}
                    style={styles.continueButton}
                  />
                </View>

                <ScrollView
                  style={{
                    marginTop: styleConstants.spacing.s10,
                  }}
                  horizontal
                  showsHorizontalScrollIndicator={false}>
                  {sampleProducts?.map((item, index) => {
                    return (
                      <View
                        key={index?.toString()}
                        style={[styles.productcontainer]}>
                        <Image
                          source={{uri: item?.images[0]?.path}}
                          resizeMode="cover"
                          style={styles.sampleProductImageContainer}
                        />
                        <View style={[styles.productNameList]}>
                          <Text
                            variant="bodyMedium"
                            style={{
                              width: 100,
                            }}>
                            {item?.productName}
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              handleSampleProducts(index);
                            }}>
                            <Icon
                              source="delete"
                              color={MD3Colors.error50}
                              size={20}
                            />
                          </TouchableOpacity>
                        </View>
                      </View>
                    );
                  })}
                </ScrollView>
                <CustomButton
                  onPress={() => handleSubmit()}
                  title={STRINGS.continue}
                  loading={sellerOnboardLoading}
                  disabled={sellerOnboardLoading}
                />
              </>
            )}
          </Formik>
        </KeyboardAwareScrollView>
        <Text style={styles.helperText} variant="bodySmall">
          Step 2/5
        </Text>
      </View>

      <BottomSheet
        snapPoints={snapPoints}
        ref={bottomSheetRef}
        enablePanDownToClose
        index={-1}
        handleIndicatorStyle={{
          backgroundColor: customColors.appBlue,
        }}
        handleStyle={styles.bottomSheet}>
        <BottomSheetView style={styles.contentContainer}>
          <Text variant="titleMedium" style={styles.addSampleProduct}>
            {'Add your sample product'}
          </Text>
          <View style={styles.productName}>
            <CustomTextInput
              title={'Product Name'}
              value={product?.productName}
              onChangeText={text => {
                setProduct(prevValue => ({
                  ...prevValue,
                  productName: text,
                }));
              }}
            />
            <View style={styles.productImageView}>
              <Text variant="labelLarge">{'Product Images'}</Text>
              <CustomButton
                onPress={() => {
                  handleImagePicker();
                }}
                title={STRINGS.add}
                style={styles.continueButton}
              />
            </View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {product?.images?.map((img, index) => (
                <View key={index?.toString()} style={styles.coverImageView}>
                  <View style={styles.imageContainer}>
                    <Image
                      source={{uri: img?.path}}
                      resizeMode="cover"
                      style={styles.imageStyle}
                    />
                  </View>
                  <TouchableOpacity
                    onPress={() => {
                      handleDeleteImage(index);
                    }}
                    style={styles.deleteIcon}>
                    <Icon source="delete" color={MD3Colors.error50} size={25} />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
            <CustomButton
              onPress={() => {
                if (!product?.productName || !product?.images?.length) {
                  Toast.show({
                    type: 'error',
                    text1: 'Please enter product name and atleast one image',
                  });
                } else {
                  onSaveProducts();
                }
              }}
              title={'Save'}
            />
          </View>
        </BottomSheetView>
      </BottomSheet>
    </SafeAreaView>
  );
};

export default SellerFormScreen;

const styles = StyleSheet.create({
  addSampleProduct: {
    color: customColors.appBlue,
    marginBottom: '10%',
  },
  bottomSheet: {
    backgroundColor: colors.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    backgroundColor: colors.background,
    flex: 1,
    padding: 36,
  },
  continueButton: {
    backgroundColor: customColors.appBlue,
    marginTop: 0,
    paddingHorizontal: styleConstants.spacing.x20,
  },
  coverImageView: {
    marginRight: 15,
    marginTop: 20,
  },
  deleteIcon: {
    position: 'absolute',
    right: 0,
    top: 0,
  },
  flex1: {
    flex: 1,
  },
  helperText: {
    marginHorizontal: styleConstants.spacing.x36,
  },
  imageContainer: {
    backgroundColor: colors.surface,
    borderRadius: styleConstants.spacing.s10,
    padding: styleConstants.spacing.s10,
  },
  imageStyle: {
    borderRadius: styleConstants.spacing.s10,
    height: 80,
    width: 90,
  },
  innerContainerStyle: {
    justifyContent: 'flex-start',
  },
  productImageView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  productName: {
    width: '100%',
  },
  productNameList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  productNames: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    marginTop: styleConstants.spacing.s10,
  },
  productcontainer: {
    backgroundColor: colors.surface,
    borderRadius: styleConstants.spacing.s10,
    marginRight: styleConstants.spacing.s10,
    padding: styleConstants.spacing.s10,
  },
  sampleProductImageContainer: {
    borderRadius: styleConstants.spacing.s10,
    height: 120,
  },
  stepText: {
    marginLeft: 10,
    marginRight: styleConstants.spacing.x20,
  },

  title: {marginBottom: '10%'},
  uploadSampleImage: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
