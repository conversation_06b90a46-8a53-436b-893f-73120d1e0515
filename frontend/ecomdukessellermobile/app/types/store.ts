import {Dayjs} from 'dayjs';

export type ImageValue =
  | string
  | {path: string; mime: string; filename: string};
export interface ISellerStore {
  id?: string;
  storeName: string;
  website?: string;
  signature?: ImageValue;
  description?: string;
  fbId?: string;
  instaId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  dp?: ImageValue;
  banner?: ImageValue;
  logo?: ImageValue;
  sellerId: string;
  storeDescription?: string;
  legalName?: string;
  workingHours?: string | number;
  unavailabilityStartDate?: Date | Dayjs | null;
  unavailabilityEndDate?: Date | Dayjs | null;
  hideWorkingHours?: boolean;
  allowBulkOrder?: boolean;
  allowCategorisation?: boolean;
}

export interface SellerStoreDto {
  description: string;
  logo: ImageValue;
  banner: ImageValue;
  dp: ImageValue;
  signature: ImageValue;
  storeName: string;
  addressLine1: string;
  addressLine2: string;
  pincode: string;
  city: string;
  state: string;
  country: string;
  fbId: string;
  instaId: string;
  website?: string;
  legalName?: string;
  workingHours?: string;
  unavailabilityStartDate?: Date | Dayjs | null;
  unavailabilityEndDate?: Date | Dayjs | null;
  hideWorkingHours?: boolean;
  allowBulkOrder?: boolean;
  allowCategorisation?: boolean;
}
