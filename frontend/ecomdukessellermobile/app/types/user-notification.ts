export interface UserNotification {
  id?: string;
  userTenantId: string;
  notificationId: string;
  isRead: boolean;
  groupId: string;
  topicId: string;
  listKey: string;
  listName: string;
  createdOn?: string;
  modifiedOn?: string;
  createdBy?: string;
  modifiedBy?: string;
  notification?: Notification;
}

export interface Notification {
  id?: string;
  subject?: string;
  body: string;
  receiver?: {
    to: Array<{
      id: string;
      name?: string;
      email?: string;
    }>;
  };
  type: number;
  sentDate?: string;
  options?: Record<string, any>;
  isDraft?: boolean;
  groupKey?: string;
  isCritical?: boolean;
}

export interface UserNotificationFilter {
  where?: {
    userTenantId?: string;
    isRead?: boolean;
    [key: string]: any;
  };
  order?: string[];
  limit?: number;
  skip?: number;
  include?: Array<{
    relation: string;
    scope?: {
      fields?: string[];
    };
  }>;
}

export interface UpdateUserNotificationRequest {
  isRead?: boolean;
  [key: string]: any;
}
