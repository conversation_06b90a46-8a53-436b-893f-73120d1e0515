'use client';

import {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {useTheme} from '@mui/material/styles';
import {
  AppBar,
  ClickAwayListener,
  Paper,
  Popper,
  Toolbar,
  Box,
  OutlinedInput,
  InputAdornment,
  Avatar,
  Button,
  ButtonBase,
  List,
  ListItemButton,
  ListItemText,
  Grid,
  Typography,
} from '@mui/material';

import IconButton from 'components/@extended/IconButton';
import Transitions from 'components/@extended/Transitions';
import {ThemeMode} from 'config';
import {
  MoreSquare,
  Notification,
  SearchNormal1,
  ShoppingCart,
} from 'iconsax-react';
import {useRouter, useSearchParams} from 'next/navigation';
import {useLazyGetSearchSuggestionsQuery} from 'redux/search/searchApiSlice';
import {AutoCompleteOption} from 'types/shared';
import MainCard from 'components/MainCard';
import {CardContent} from '@mui/material';
import {Stack} from '@mui/material';
import ProfileTab from './Profile/ProfileTab';
import {unsetCredentials} from 'redux/auth/authSlice';
import {useAppDispatch, useAppSelector} from 'redux/hooks';
import {unsetMonitor} from 'redux/apimonitor/apiMonitorSlice';
import {apiSlice} from 'redux/apiSlice';
import {ApiTagTypes} from 'redux/types';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';

interface TabPanelProps {
  children?: ReactNode;
  dir?: string;
  index: number;
  value: number;
}

function TabPanel({children, value, index, ...other}: TabPanelProps) {
  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
      sx={{p: 1}}
    >
      {value === index && children}
    </Box>
  );
}
export default function MobileSection() {
  const theme = useTheme();
  const router = useRouter();
  const search = useSearchParams();
  const dispatch = useAppDispatch();
  const [profileOpen, setProfileOpen] = useState(false);
  const avatarRef = useRef<HTMLButtonElement | null>(null);

  const anchorRef = useRef<any>(null);
  const [open, setOpen] = useState(false);
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);

  const [searchQuery, setSearchQuery] = useState(search.get('search') ?? '');
  const [getSearchSuggestions, {data}] = useLazyGetSearchSuggestionsQuery();
  const {data: user} = useGetUserQuery(undefined, {
    skip: !isLoggedIn,
  });
  const [value] = useState(0);

  const handleToggle = () => setOpen(prev => !prev);
  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) return;
    setOpen(false);
  };

  const onSubmitSearch = (query: string) => {
    if (query.trim() !== '') {
      router.push(`/listing?search=${query.trim()}`);
      setOpen(false);
    }
  };

  const loadSearchSuggestions = useCallback(() => {
    if (searchQuery.trim() !== '') {
      getSearchSuggestions(searchQuery);
    }
  }, [searchQuery, getSearchSuggestions]);

  useEffect(() => {
    const timeout = setTimeout(loadSearchSuggestions, 500);
    return () => clearTimeout(timeout);
  }, [searchQuery, loadSearchSuggestions]);

  const suggestions: AutoCompleteOption[] = useMemo(() => {
    if (!data) return [];
    return data.map(item => ({
      value: item.id,
      label: item.name.split(' ').slice(0, 4).join(' '),
    }));
  }, [data]);
  const handleLogout = async () => {
    dispatch(unsetCredentials());
    dispatch(unsetMonitor());
    dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
    router.push('/login');
  };
  const iconBackColorOpen =
    theme.palette.mode === ThemeMode.DARK
      ? 'background.paper'
      : 'secondary.200';
  const iconBackColor =
    theme.palette.mode === ThemeMode.DARK
      ? 'background.default'
      : 'secondary.100';

  return (
    <>
      <Box sx={{flexShrink: 0, ml: 0.75}}>
        <IconButton
          ref={anchorRef}
          aria-label="open more menu"
          aria-controls={open ? 'menu-list-grow' : undefined}
          aria-haspopup="true"
          onClick={handleToggle}
          color="secondary"
          variant="light"
          size="large"
          sx={{
            color: 'secondary.main',
            bgcolor: open ? iconBackColorOpen : iconBackColor,
            p: 1,
          }}
        >
          <MoreSquare
            size={28}
            variant="Bulk"
            style={{transform: 'rotate(90deg)'}}
          />
        </IconButton>
      </Box>

      <Popper
        placement="bottom-end"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        sx={{width: '100%'}}
        popperOptions={{
          modifiers: [{name: 'offset', options: {offset: [0, 9]}}],
        }}
      >
        {({TransitionProps}) => (
          <Transitions type="fade" in={open} {...TransitionProps}>
            <Paper sx={{boxShadow: theme.customShadows.z1}}>
              <ClickAwayListener onClickAway={handleClose}>
                <AppBar color="inherit">
                  <Toolbar
                    sx={{flexDirection: 'column', alignItems: 'stretch', p: 2}}
                  >
                    <OutlinedInput
                      value={searchQuery}
                      onChange={e => {
                        setSearchQuery(e.target.value);
                        if (!open) setOpen(true);
                      }}
                      onKeyDown={e => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          onSubmitSearch(searchQuery);
                        }
                      }}
                      placeholder="Search for shop"
                      sx={{height: '50px', fontSize: '1rem', mb: 1}}
                      endAdornment={
                        <InputAdornment position="end">
                          <SearchNormal1
                            size="20"
                            onClick={() => onSubmitSearch(searchQuery)}
                            style={{cursor: 'pointer', color: '#888'}}
                          />
                        </InputAdornment>
                      }
                    />

                    {suggestions.length > 0 && (
                      <List dense>
                        {suggestions.map(suggestion => (
                          <ListItemButton
                            key={suggestion.value}
                            onClick={() => onSubmitSearch(suggestion.label)}
                          >
                            <ListItemText primary={suggestion.label} />
                          </ListItemButton>
                        ))}
                      </List>
                    )}

                    <Box sx={{display: 'flex', alignItems: 'center', mt: 2}}>
                      <Button
                        variant="outlined"
                        sx={{
                          mr: 2,
                          color: '#00004F',
                          borderColor: '#00004F',
                          '&:hover': {
                            borderColor: '#00004F',
                          },
                        }}
                      >
                        Sell on Ecomdukes
                      </Button>
                      <ButtonBase
                        onClick={() => router.push('/cart')}
                        sx={{
                          p: 0.25,
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor:
                              theme.palette.mode === ThemeMode.DARK
                                ? 'secondary.light'
                                : 'secondary.lighter',
                          },
                          '&:focus-visible': {
                            outline: `2px solid ${theme.palette.secondary.dark}`,
                            outlineOffset: 2,
                          },
                        }}
                        aria-label="open cart"
                      >
                        <ShoppingCart
                          size="30"
                          color="#00004F"
                          variant="Bold"
                        />
                      </ButtonBase>

                      <ButtonBase
                        onClick={() => router.push('/cart')}
                        sx={{
                          p: 0.25,
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor:
                              theme.palette.mode === ThemeMode.DARK
                                ? 'secondary.light'
                                : 'secondary.lighter',
                          },
                          '&:focus-visible': {
                            outline: `2px solid ${theme.palette.secondary.dark}`,
                            outlineOffset: 2,
                          },
                        }}
                        aria-label="open wishlist"
                      >
                        <FavoriteBorderIcon
                          sx={{
                            size: '30',
                            color: '#00004F',
                            variant: 'Bold',
                          }}
                        />
                      </ButtonBase>

                      <ButtonBase
                        onClick={() => router.push('/cart')}
                        sx={{
                          p: 0.25,
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor:
                              theme.palette.mode === ThemeMode.DARK
                                ? 'secondary.light'
                                : 'secondary.lighter',
                          },
                          '&:focus-visible': {
                            outline: `2px solid ${theme.palette.secondary.dark}`,
                            outlineOffset: 2,
                          },
                        }}
                        aria-label="open notifications"
                      >
                        <Notification
                          size="30"
                          color="#00004F"
                          variant="Bold"
                        />
                      </ButtonBase>
                      <ButtonBase
                        ref={avatarRef}
                        onClick={() => setProfileOpen(prev => !prev)}
                        sx={{
                          p: 0.25,
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor:
                              theme.palette.mode === ThemeMode.DARK
                                ? 'secondary.light'
                                : 'secondary.lighter',
                          },
                          '&:focus-visible': {
                            outline: `2px solid ${theme.palette.secondary.dark}`,
                            outlineOffset: 2,
                          },
                        }}
                        aria-label="open profile"
                        aria-haspopup="true"
                      >
                        <Avatar
                          sx={{
                            color: '#fff',
                          }}
                          src={user?.photoUrl || undefined}
                        >
                          {!user?.photoUrl &&
                            user?.firstName?.charAt(0)?.toUpperCase()}
                        </Avatar>{' '}
                      </ButtonBase>
                      <Popper
                        placement="bottom-end"
                        open={profileOpen}
                        anchorEl={anchorRef.current}
                        role={undefined}
                        transition
                        disablePortal
                        popperOptions={{
                          modifiers: [
                            {
                              name: 'offset',
                              options: {
                                offset: [0, 9],
                              },
                            },
                          ],
                        }}
                      >
                        {({TransitionProps}) => (
                          <Transitions
                            type="grow"
                            position="top-right"
                            in={open}
                            {...TransitionProps}
                          >
                            <ClickAwayListener
                              onClickAway={() => setProfileOpen(false)}
                            >
                              <Box>
                                <Paper
                                  sx={{
                                    boxShadow: theme.customShadows.z1,
                                    width: 290,
                                    minWidth: 240,
                                    maxWidth: 290,
                                    [theme.breakpoints.down('md')]: {
                                      maxWidth: 250,
                                    },
                                    borderRadius: 1.5,
                                  }}
                                >
                                  <MainCard border={false} content={false}>
                                    <CardContent sx={{px: 2.5, pt: 3}}>
                                      <Grid
                                        container
                                        justifyContent="space-between"
                                        alignItems="center"
                                      >
                                        <Grid item>
                                          <Stack
                                            direction="row"
                                            spacing={1.25}
                                            alignItems="center"
                                          >
                                            <Avatar
                                              sx={{
                                                color: '#fff',
                                              }}
                                              src={user?.photoUrl || undefined}
                                            >
                                              {!user?.photoUrl &&
                                                user?.firstName
                                                  ?.charAt(0)
                                                  ?.toUpperCase()}
                                            </Avatar>
                                            <Stack>
                                              <Typography variant="subtitle1">
                                                Hello
                                              </Typography>

                                              <Typography
                                                variant="h5"
                                                color="textPrimary"
                                              >
                                                {`${user?.firstName} ${user?.lastName}`}
                                              </Typography>
                                            </Stack>
                                          </Stack>
                                        </Grid>
                                      </Grid>
                                    </CardContent>
                                  </MainCard>
                                </Paper>
                                <Paper
                                  sx={{
                                    boxShadow: theme.customShadows.z1,
                                    width: 290,
                                    minWidth: 240,
                                    maxWidth: 290,
                                    [theme.breakpoints.down('md')]: {
                                      maxWidth: 250,
                                      mt: 1,
                                    },
                                    [theme.breakpoints.up('md')]: {
                                      mt: 2,
                                    },
                                    borderRadius: 1.5,
                                    maxHeight: '60vh',
                                    overflowY: 'auto',
                                  }}
                                >
                                  <MainCard border={false} content={false}>
                                    <CardContent>
                                      <TabPanel
                                        value={value}
                                        index={0}
                                        dir={theme.direction}
                                      >
                                        <ProfileTab
                                          handleLogout={handleLogout}
                                        />
                                      </TabPanel>
                                    </CardContent>
                                  </MainCard>
                                </Paper>
                              </Box>
                            </ClickAwayListener>
                          </Transitions>
                        )}
                      </Popper>
                    </Box>
                  </Toolbar>
                </AppBar>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </>
  );
}
