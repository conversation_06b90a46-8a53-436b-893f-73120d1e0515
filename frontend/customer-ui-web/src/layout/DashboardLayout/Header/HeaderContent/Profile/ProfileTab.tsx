import {useState, MouseEvent} from 'react';

// material-ui
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import SwitchAccountIcon from '@mui/icons-material/SwitchAccount';
// assets
import {Card, Logout, Profile} from 'iconsax-react';
import {useRouter} from 'next/navigation';

// ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //

interface Props {
  handleLogout: () => void;
  onClose?: () => void;
}

export default function ProfileTab({handleLogout, onClose}: Props) {
  const [selectedIndex, setSelectedIndex] = useState<number>();
  const router = useRouter();

  const handleListItemClick = (
    event: MouseEvent<HTMLDivElement>,
    index: number,
  ) => {
    setSelectedIndex(index);
  };

  const getItemSx = (index: number) => {
    const isSelected = selectedIndex === index;
    return {
      backgroundColor: isSelected ? '#E6EAF5' : 'transparent',
      color: isSelected ? '#00004F' : 'inherit',
      '&.Mui-selected': {
        backgroundColor: '#E6EAF5 !important',
        color: '#00004F !important',
        '& .MuiListItemIcon-root': {
          color: '#00004F !important', // override icon color in selected state
        },
        '& .MuiListItemText-primary': {
          color: '#00004F !important', // override text color in selected state
        },
      },
      '&.Mui-selected:hover': {
        backgroundColor: '#E6EAF5 !important',
      },
      '& .MuiListItemIcon-root': {
        color: isSelected ? '#00004F' : 'inherit', // fallback if selected class isn't applied yet
      },
      '& .MuiListItemText-primary': {
        color: isSelected ? '#00004F' : 'inherit',
      },
    };
  };

  return (
    <List
      component="nav"
      sx={{p: 0, '& .MuiListItemIcon-root': {minWidth: 32}}}
    >
      <ListItemButton
        selected={selectedIndex === 0}
        onClick={event => {
          handleListItemClick(event, 0);
          router.push('/profile');
          onClose?.();
        }}
        sx={getItemSx(0)}
      >
        <ListItemIcon>
          <Profile variant="Bulk" size={18} />
        </ListItemIcon>
        <ListItemText primary="My Profile" />
      </ListItemButton>

      <ListItemButton
        selected={selectedIndex === 1}
        onClick={event => {
          handleListItemClick(event, 1);
          router.push('/orders');
          onClose?.();
        }}
        sx={getItemSx(1)}
      >
        <ListItemIcon>
          <Card variant="Bulk" size={18} />
        </ListItemIcon>
        <ListItemText primary="My Order" />
      </ListItemButton>

      <ListItemButton
        selected={selectedIndex === 2}
        onClick={event => {
          handleListItemClick(event, 2);
          handleLogout();
          onClose?.();
        }}
        sx={getItemSx(2)}
      >
        <ListItemIcon>
          <Logout variant="Bulk" size={18} />
        </ListItemIcon>
        <ListItemText primary="Logout" />
      </ListItemButton>

      <ListItemButton
        selected={selectedIndex === 3}
        onClick={event => {
          handleListItemClick(event, 3);
          router.push('/switch-account');
        }}
        sx={getItemSx(3)}
      >
        <ListItemIcon>
          <SwitchAccountIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText primary="Switch Account" />
      </ListItemButton>
    </List>
  );
}
