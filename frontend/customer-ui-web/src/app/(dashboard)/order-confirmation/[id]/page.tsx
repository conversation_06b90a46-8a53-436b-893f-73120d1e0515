'use client';

import {useParams, useRouter} from 'next/navigation';
import OrderConfirmation from 'views/order-confirmation/OrderConfirmation';
import OrderDetails from 'views/order-confirmation/OrderProcessing';
import OrderSummary from 'views/order-confirmation/OrderSummary';
import {useGetOrderByIdQuery} from 'redux/order/orderApiSlice';
import {IFilter} from 'types/filter';
import {fieldsExcludeMetaFields} from 'types/api';
import {Box, Button, Link, Typography} from '@mui/material';
import {OrderStatus} from 'enums/orderStatus';

export default function ThankYouPage() {
  const params = useParams();
  const orderId =
    typeof params.id === 'string' ? params.id : (params.id?.[0] ?? '');

  const filter: IFilter = {
    include: [
      {
        relation: 'orderLineItems',
        scope: {
          fields: fieldsExcludeMetaFields,
          include: [
            {
              relation: 'productVariant',
              scope: {
                fields: fieldsExcludeMetaFields,
                include: [
                  {
                    relation: 'featuredAsset',
                    scope: {
                      fields: fieldsExclude<PERSON>eta<PERSON>ields,
                    },
                  },
                ],
              },
            },
            {
              relation: 'shipping',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
          ],
        },
      },
      {
        relation: 'customer',
      },
      {
        relation: 'shippingAddress',
      },
      {
        relation: 'billingAddress',
      },
    ],
  };

  const {
    data: order,
    isLoading,
    isError,
  } = useGetOrderByIdQuery({id: orderId, filter});

  if (isLoading) return <p>Loading...</p>;
  if (isError || !order) return <p>Failed to load order.</p>;

  const formattedDate = new Date(order.createdOn).toLocaleDateString('en-GB');
  const formattedTime = new Date(order.createdOn).toLocaleTimeString('en-GB');

  const router = useRouter();

  if (order.status === OrderStatus.PaymentFailed) {
    return (
      <Box
        minHeight="60vh"
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        textAlign="center"
        gap={2}
        mt={3}
      >
        <Typography variant="h4" color="error">
          Payment Failed
        </Typography>
        <Typography variant="body1">
          Unfortunately, your payment could not be processed.
        </Typography>
        <Typography variant="body2">
          Please try again or choose a different payment method.
        </Typography>

        <Button
          component={Link}
          onClick={() => router.push('/cart')}
          variant="contained"
          sx={{mt: 2, borderRadius: 2}}
        >
          Return to Cart
        </Button>
      </Box>
    );
  }

  return (
    <>
      <OrderConfirmation
        orderId={order.orderId as string}
        email={order.customer?.userTenant.user.email as string}
        date={formattedDate}
        time={formattedTime}
      />
      <OrderDetails order={order} />
      <OrderSummary order={order} />
    </>
  );
}
