import {useFormik} from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Divider,
  MenuItem,
  TextField,
  Typography,
} from '@mui/material';
import {ProductCustomField} from 'types/product';
import {
  useAddItemToCartMutation,
  useUpdateProductCustomizationsMutation,
} from 'redux/ecom/cartApiSlice';
import {SnackbarProps} from 'types/snackbar';
import {openSnackbar} from 'api/snackbar';
import {useEffect, useState, Fragment} from 'react';
import {useRouter} from 'next/navigation';
import {productCustomizationsAtom} from 'utils/atoms/productCustomizationAtom';
import {useSetAtom} from 'jotai';
import {useAtomValue} from 'jotai';

type Props = {
  fields: ProductCustomField[];
  productVariantId: string;
  isInCart?: boolean;
  cartId?: string;
};

const ProductCustomizationForm: React.FC<Props> = ({
  fields,
  productVariantId,
  isInCart,
  cartId,
}) => {
  const setProductCustomizations = useSetAtom(productCustomizationsAtom);
  const existingCustomizations = useAtomValue(productCustomizationsAtom);
  const router = useRouter();
  const [addItemToCart, {isLoading}] = useAddItemToCartMutation();
  const [updateCustomizations, {isLoading: isSaving}] =
    useUpdateProductCustomizationsMutation();

  const [isAddedToCart, setIsAddedToCart] = useState(false);

  const initialValues = fields.reduce(
    (acc, field) => {
      const storedValue =
        existingCustomizations?.[productVariantId]?.[field.id];
      acc[field.name] =
        storedValue ??
        (field.fieldType === 'checkbox'
          ? false
          : field.fieldType === 'file'
            ? ''
            : '');
      return acc;
    },
    {} as Record<string, any>,
  );

  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };

  const validationSchema = Yup.object(
    fields.reduce(
      (schema, field) => {
        if (field.fieldType === 'file') return schema;

        let validator: Yup.AnySchema;
        if (field.fieldType === 'number') {
          validator = Yup.number()
            .typeError(`${field.label} must be a number`)
            .transform((value, originalValue) =>
              String(originalValue).trim() === '' ? undefined : value,
            );
        } else if (field.fieldType === 'checkbox') {
          validator = field.isRequired
            ? Yup.boolean().oneOf([true], `${field.label} is required`)
            : Yup.boolean();
        } else {
          let stringValidator = Yup.string();
          if (field.validationRegex) {
            stringValidator = stringValidator.matches(
              new RegExp(field.validationRegex),
              'Invalid format',
            );
          }
          if (field.isRequired) {
            stringValidator = stringValidator.required(
              `${field.label} is required`,
            );
          }
          validator = stringValidator;
        }

        if (field.isRequired && field.fieldType !== 'checkbox') {
          validator = validator.required(`${field.label} is required`);
        }
        schema[field.name] = validator;
        return schema;
      },
      {} as Record<string, Yup.AnySchema>,
    ),
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => {
      const customizationValues = await Promise.all(
        fields.map(async field => {
          let value: string;

          if (field.fieldType === 'file') {
            const files: File[] | string = values[field.name];

            if (Array.isArray(files)) {
              const base64Strings = await Promise.all(
                files.map(file => {
                  return new Promise<string>((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result as string);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                  });
                }),
              );
              value = JSON.stringify(base64Strings);
            } else {
              value = files;
            }
          } else {
            value = String(values[field.name]);
          }

          return {
            customizationFieldId: field.id,
            value,
          };
        }),
      );

      const customizationValuesById = fields.reduce(
        (acc, field) => {
          acc[field.id] = values[field.name];
          return acc;
        },
        {} as Record<string, any>,
      );

      setProductCustomizations(prev => ({
        ...prev,
        [productVariantId]: customizationValuesById,
      }));

      if (isInCart && cartId && productVariantId) {
        await updateCustomizations({
          cartId,
          data: customizationValues,
        }).unwrap();
        openSnackbar({
          open: true,
          message: 'Customizations saved successfully!',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      } else {
        const cartItem = {
          productVariantId,
          quantity: 1,
          customizationValues,
        };
        await addItemToCart(cartItem).unwrap();
        setIsAddedToCart(true);
        openSnackbar({
          open: true,
          message: 'Product added to Cart',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      }
    },
  });

  useEffect(() => {
    const customizationValuesById = fields.reduce(
      (acc, field) => {
        acc[field.id] = formik.values[field.name];
        return acc;
      },
      {} as Record<string, any>,
    );

    setProductCustomizations(prev => ({
      ...prev,
      [productVariantId]: customizationValuesById,
    }));
  }, [formik.values, productVariantId, setProductCustomizations]);

  return (
    <form onSubmit={formik.handleSubmit}>
      <Box
        display="flex"
        flexDirection="column"
        sx={{background: '#f3f4f6', borderRadius: 4, px: 3, py: 4}}
      >
        {fields.map((field, index) => {
          const error =
            formik.touched[field.name] &&
            typeof formik.errors[field.name] === 'string'
              ? (formik.errors[field.name] as string)
              : undefined;

          return (
            <Fragment key={field.id}>
              {(() => {
                switch (field.fieldType) {
                  case 'text':
                  case 'number':
                    return (
                      <Box sx={{mb: 0}}>
                        <TextField
                          name={field.name}
                          type={field.fieldType}
                          fullWidth
                          value={formik.values[field.name]}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={Boolean(error)}
                          helperText={error}
                          placeholder={field.placeholder}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '25px',
                              backgroundColor: 'white',
                              height: 40,
                              fontSize: 14,
                              paddingX: 1.5,
                            },
                            '& input': {
                              padding: '10px 14px',
                            },
                          }}
                          FormHelperTextProps={{
                            sx: {
                              marginLeft: 1.5,
                              marginTop: 0.5,
                            },
                          }}
                        />
                      </Box>
                    );

                  case 'select':
                  case 'dropdown':
                    return (
                      <Box sx={{mb: 0}}>
                        <Typography
                          sx={{
                            mb: 0.5,
                            fontWeight: 420,
                            color: '#5d6d7e',
                            ml: 2,
                          }}
                          component="label"
                          htmlFor={field.name}
                        >
                          {field.placeholder}
                        </Typography>
                        <TextField
                          select
                          name={field.name}
                          fullWidth
                          value={formik.values[field.name]}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={Boolean(error)}
                          helperText={error}
                          sx={{
                            borderRadius: '25px',
                            background: 'white',
                          }}
                        >
                          {(field.productCustomizationOptions || []).map(
                            option => (
                              <MenuItem key={option.id} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ),
                          )}
                        </TextField>
                      </Box>
                    );

                  default:
                    return null;
                }
              })()}
              {index !== fields.length - 1 && (
                <Divider sx={{my: 2, borderColor: '#d2d6dc'}} />
              )}
            </Fragment>
          );
        })}
        <Box display="flex" justifyContent="flex-end" mt={2}>
          {isAddedToCart ? (
            <Button
              onClick={handleGoToCart}
              variant="contained"
              sx={{
                backgroundColor: '#07074f',
                '&:hover': {backgroundColor: '#07074f'},
                borderRadius: 6,
                px: 4,
                fontWeight: 'bold',
                width: '140px',
              }}
            >
              GO TO CART
            </Button>
          ) : (
            <Button
              type="submit"
              variant="contained"
              disabled={isLoading || isSaving}
              sx={{
                backgroundColor: '#00004F',
                '&:hover': {backgroundColor: '#07074f'},
                borderRadius: 6,
                px: 4,
                fontWeight: 'bold',
                width: '140px',
              }}
            >
              {isInCart
                ? isSaving
                  ? 'Saving...'
                  : 'Save'
                : isLoading
                  ? 'Adding...'
                  : 'Add to Cart'}
            </Button>
          )}
        </Box>
      </Box>
    </form>
  );
};

export default ProductCustomizationForm;
