'use client';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  IconButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import Image from 'next/image';
import {
  ArrowDown2,
  BoxTick,
  Information,
  Like1,
  Location,
  TickCircle,
  TruckFast,
} from 'iconsax-react';
import {ProductVariant} from 'types/product';
import {useState} from 'react';
import {useAddItemToCartMutation} from 'redux/ecom/cartApiSlice';
import {CartItem} from 'types/cart';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {useRouter} from 'next/navigation';
import {productCustomizationsAtom} from 'utils/atoms/productCustomizationAtom';
import {useAtomValue} from 'jotai';
import {
  useAddItemToWishlistMutation,
  useRemoveItemFromWishlistMutation,
} from 'redux/ecom/wishlistApiSlice';
import {ImageZoomHover} from './ImageZoomHover';
import AllReviewsModal from 'views/review/AllReviewsModal';
import LazyVideo from './LazyVideo';
import {useCalculateShippingByPincodeMutation} from 'redux/ecom/shippingCalculationApiSlice';
import {PincodeShippingCalculationResponseDto} from 'types/shipping';
import draftToHtml from 'draftjs-to-html';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import FavoriteIcon from '@mui/icons-material/Favorite';

interface Props {
  productVariant: ProductVariant;
}
const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);

// Safe HTML conversion
const getHtmlContent = (content: string | undefined) => {
  if (!content) return '<p>No content available.</p>';
  try {
    return isHtml(content) ? content : draftToHtml(JSON.parse(content));
  } catch (err) {
    console.error('Failed to parse content:', err);
    return '<p>Error rendering content.</p>';
  }
};

export default function ProductPreviewCard({productVariant}: Props) {
  const router = useRouter();

  const [isAddedToCart, setIsAddedToCart] = useState(false);
  const [addItemToCart, {isLoading: isAddingToCart}] =
    useAddItemToCartMutation();

  const [selectedImage, setSelectedImage] = useState<string>(
    productVariant.featuredAsset?.previewUrl || '',
  );

  const [isWishlisted, setIsWishlisted] = useState(
    productVariant.wishlist?.id ? true : false,
  );

  const [wishlistId, setWishlistId] = useState<string | undefined>(
    productVariant.wishlist?.id,
  );

  const [modalOpen, setModalOpen] = useState(false);
  const reviews = productVariant?.reviews || [];
  const firstThree = reviews.slice(0, 3);

  const thumbnails =
    (productVariant.productVariantAssets?.length
      ? [...productVariant.productVariantAssets]
          .sort((a, b) => a.position - b.position)
          .map(asset => ({
            url: asset?.asset?.previewUrl,
            mimeType: asset?.asset?.mimeType,
          }))
      : productVariant.featuredAsset
        ? [
            {
              url: productVariant.featuredAsset.previewUrl,
              mimeType: productVariant.featuredAsset.mimeType,
            },
          ]
        : []) ?? [];

  const productCustomizations = useAtomValue(productCustomizationsAtom);

  const [pincode, setPincode] = useState('');
  const [shippingData, setShippingData] =
    useState<PincodeShippingCalculationResponseDto | null>(null);

  const [calculateShipping, {isLoading: isCalculating}] =
    useCalculateShippingByPincodeMutation();

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();

    const customizationForThisVariant =
      productCustomizations[productVariant.id];
    const customizationValues = customizationForThisVariant
      ? Object.entries(customizationForThisVariant).map(([fieldId, value]) => ({
          customizationFieldId: fieldId,
          value: String(value),
        }))
      : undefined;
    const cartItem: Partial<CartItem> = {
      productVariantId: productVariant.id,
      quantity: 1,
      ...(customizationValues ? {customizationValues} : {}),
    };
    await addItemToCart(cartItem).unwrap();
    setIsAddedToCart(true);
    openSnackbar({
      open: true,
      message: 'Product added to Cart',
      variant: 'alert',
      alert: {color: 'success'},
    } as SnackbarProps);
  };

  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };

  const [addItemToWishlist, {isLoading: isAddingToWishlist}] =
    useAddItemToWishlistMutation();

  const [removeItemFromWishlist, {isLoading: isRemovingFromWishlist}] =
    useRemoveItemFromWishlistMutation();

  const handleToggleWishlist = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (isWishlisted && wishlistId) {
      await removeItemFromWishlist(wishlistId).unwrap();
      openSnackbar({
        open: true,
        message: 'Removed from wishlist',
        variant: 'alert',
        alert: {color: 'info'},
      } as SnackbarProps);
      setIsWishlisted(false);
      setWishlistId(undefined);
    } else {
      const result = await addItemToWishlist(productVariant.id).unwrap();
      openSnackbar({
        open: true,
        message: 'Added to wishlist',
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
      setIsWishlisted(true);
      setWishlistId(result.id);
    }
  };

  const formatDate = (isoString: string): string => {
    return new Date(isoString).toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  const handlePincodeCheck = async () => {
    if (pincode.length === 6) {
      const data = await calculateShipping({
        productVariantId: productVariant.id,
        pincode,
      }).unwrap();
      setShippingData(data);
    } else {
      openSnackbar({
        open: true,
        message: 'Please enter a valid 6-digit pincode.',
        variant: 'alert',
        alert: {color: 'warning'},
      } as SnackbarProps);
    }
  };
  const productDisclaimerContent =
    productVariant.productDisclaimer?.disclaimer ||
    productVariant.product?.productDisclaimer?.disclaimer ||
    null;

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#f9f9f9',
        borderRadius: 4,
        p: 3,

        width: '100%',
        maxWidth: {xs: '100%', sm: 700},
        mx: 'auto',
      }}
    >
      {/* Product Image */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: 2,
          width: '100%',
          alignItems: 'center',
        }}
      >
        {/* Thumbnails */}
        <Stack spacing={2}>
          {thumbnails.map((thumb, index) => (
            <Box
              key={index}
              onClick={() => setSelectedImage(thumb.url ?? '')}
              sx={{
                width: 60,
                height: 60,
                borderRadius: 2,
                overflow: 'hidden',
                position: 'relative',
                cursor: 'pointer',
                border:
                  selectedImage === thumb.url
                    ? '2px solid #1976d2'
                    : '2px solid transparent',
              }}
            >
              {thumb.mimeType?.startsWith('image') ? (
                <Image
                  src={thumb.url ?? ''}
                  alt={`Thumbnail ${index + 1}`}
                  fill
                  style={{objectFit: 'cover'}}
                />
              ) : thumb.mimeType?.startsWith('video') ? (
                <video
                  key={thumb.url}
                  src={thumb.url ?? ''}
                  style={{width: '100%', height: '100%', objectFit: 'cover'}}
                  muted
                />
              ) : null}
            </Box>
          ))}
        </Stack>

        {/* Main Image with Hover Zoom */}
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            aspectRatio: '4 / 3',
            borderRadius: 4,
            overflow: 'hidden',
            backgroundColor: '#f4f4f4',
            cursor: 'zoom-in',
            '&:hover img': {
              transform: 'scale(1.02)',
              transition: 'transform 0.3s ease',
            },
          }}
        >
          {(() => {
            const selectedAsset = thumbnails.find(t => t.url === selectedImage);
            if (!selectedAsset) return null;

            if (selectedAsset.mimeType?.startsWith('image')) {
              return (
                <ImageZoomHover src={selectedAsset.url ?? ''} zoomScale={2.5} />
              );
            } else if (selectedAsset.mimeType?.startsWith('video')) {
              return (
                <LazyVideo
                  src={selectedAsset.url ?? ''}
                  width="100%"
                  height="100%"
                  autoPlay={true}
                  muted={true}
                  loop={true}
                  controls={true}
                  style={{
                    objectFit: 'contain',
                  }}
                />
              );
            } else {
              return null;
            }
          })()}

          <IconButton
            onClick={handleToggleWishlist}
            disabled={isAddingToWishlist || isRemovingFromWishlist}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              backgroundColor: '#fff',
              borderRadius: '50%',
              boxShadow: 1,
              color: isWishlisted ? '#9A2D8E' : 'inherit',
            }}
          >
            {isWishlisted ? (
              <FavoriteIcon sx={{fontSize: 32, color: '#FF0000 !important'}} />
            ) : (
              <FavoriteBorderIcon sx={{fontSize: 32}} />
            )}
          </IconButton>
        </Box>
      </Box>
      {/* Review Text */}
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{mt: 3, textAlign: 'center'}}
      >
        {productVariant?.reviews?.find(
          review => review.rating > 4 && !!review.review?.trim(),
        )?.review ?? ''}
      </Typography>
      {/* Action Buttons */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: {xs: 'column', sm: 'row'},
          gap: 2,
          mt: 3,
          backgroundColor: 'transparent',
          borderRadius: 3,
          p: 3,
          width: {xs: '100%', sm: '100%', md: '100%', lg: '650px'},
          position: 'fixed',
          bottom: 16,
          left: {xs: '50%', md: '25%'},
          transform: 'translateX(-50%)',
          zIndex: 1300,
          boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
          justifyContent: 'center',
        }}
      >
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#832980',
            '&:hover': {backgroundColor: '#6c1f6b'},
            borderRadius: 6,
            px: 5,
            py: 1.3,

            fontWeight: 'bold',
            width: {xs: '100%', sm: 'auto'},
          }}
        >
          BUY NOW
        </Button>
        {isAddedToCart ? (
          <Button
            onClick={handleGoToCart}
            variant="contained"
            sx={{
              backgroundColor: '#0d0d7b',
              '&:hover': {backgroundColor: '#07074f'},
              borderRadius: 6,
              px: 4,
              fontWeight: 'bold',
              width: {xs: '100%', sm: 'auto'},
            }}
          >
            GO TO CART
          </Button>
        ) : (
          <Button
            onClick={handleAddToCart}
            disabled={isAddingToCart}
            variant="contained"
            sx={{
              backgroundColor: '#00004F',
              '&:hover': {backgroundColor: '#07074f'},
              borderRadius: 6,
              px: 4,
              fontWeight: 'bold',
              width: {xs: '100%', sm: 'auto'},
            }}
          >
            {isAddingToCart ? 'Adding...' : 'ADD TO CART'}
          </Button>
        )}
      </Box>
      <Typography variant="h5" sx={{color: '#00004F'}}>
        Delivery
      </Typography>
      {/* Pincode Input */}
      <TextField
        variant="outlined"
        size="small"
        placeholder="Enter Delivery Pincode"
        onChange={e => setPincode(e.target.value)}
        InputProps={{
          startAdornment: <Location color="#00004F" />,
          endAdornment: isCalculating ? (
            <Box sx={{px: 2}}>
              <CircularProgress size={20} color="secondary" />
            </Box>
          ) : (
            <span
              onClick={handlePincodeCheck}
              style={{
                color: '#9E3393',
                padding: '8px 16px',
                borderRadius: '8px',
                cursor: 'pointer',
                display: 'inline-block',
                fontWeight: 500,
                fontSize: '14px',
              }}
            >
              Check
            </span>
          ),
        }}
        sx={{
          mt: 4,
          width: '100%',
          borderRadius: '25px',
          background: 'white',
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'lightgray',
            borderRadius: '25px',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {borderColor: 'gray'},
        }}
      />
      {/* Delivery Info */}
      <Box sx={{my: 4, bgcolor: '#f9f9f9'}}>
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            alignItems: 'center',
            gap: 2,
            mb: 3,
          }}
        >
          <Typography>
            <strong>Delivery:</strong>{' '}
            <Typography component="span" color="green" sx={{ml: 1}}>
              {shippingData
                ? shippingData.shippingCost === 0
                  ? 'Free'
                  : `₹${shippingData.shippingCost}`
                : '—'}
            </Typography>
          </Typography>
          <Typography>
            <strong>COD:</strong>{' '}
            <Typography component="span" sx={{ml: 1}}>
              {shippingData?.isServiceable ? 'Available' : 'Not Available'}
            </Typography>
          </Typography>

          <Typography>
            <strong>Refund Policy</strong>
          </Typography>
          <Tooltip title="View our refund policy">
            <IconButton size="small">
              <Information />
            </IconButton>
          </Tooltip>
        </Box>
        <Typography variant="h5" sx={{mb: 2, color: '#00004F'}}>
          Estimated Delivery
        </Typography>
        <Box
          display="flex"
          flexDirection={{xs: 'column', sm: 'row'}}
          gap={2}
          border="1px solid #E0E0E0"
          borderRadius="8px"
          p={2}
          sx={{background: 'white'}}
        >
          {[
            {
              label: 'Order Date',
              value: formatDate(new Date().toISOString()),
              icon: <Like1 color="#00004F" />,
            },
            {
              label: 'Dispatch By',
              value: '—',
              icon: <TruckFast color="#00004F" />,
            },
            {
              label: 'Delivery By',
              value: shippingData?.expectedDeliveryDate
                ? formatDate(shippingData.expectedDeliveryDate)
                : '—',
              icon: <BoxTick color="#00004F" />,
            },
          ].map((item, i) => (
            <Box key={i} flex={1} textAlign="center">
              <Typography mb={1} variant="body2">
                {item.label}
              </Typography>
              <Typography>{item.value}</Typography>
              <Box mt={2} sx={{'& svg': {width: 40, height: 40}}}>
                {item.icon}
              </Box>
            </Box>
          ))}
        </Box>

        {/* Offers */}
        <Typography
          mt={2}
          variant="h5"
          sx={{fontWeight: 'bold', mb: 2, color: '#00004F'}}
        >
          Offers
        </Typography>
        <Card variant="outlined" sx={{mb: 2, borderRadius: 3}}>
          <CardContent
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography>Discount of 40%</Typography>
            <ArrowDown2 />
          </CardContent>
        </Card>
        <Card variant="outlined" sx={{borderRadius: 3}}>
          <CardContent>
            <Typography>
              Install our app and get off on first purchase
            </Typography>
          </CardContent>
        </Card>
      </Box>
      {/* Reviews */}
      <Box sx={{my: 4}}>
        <Typography variant="h5" sx={{mb: 2, color: '#00004F'}}>
          Reviews
        </Typography>

        <Box sx={{background: 'white', borderRadius: 2, p: 2}}>
          {reviews.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No reviews available.
            </Typography>
          ) : (
            <>
              {firstThree.map((review, index) => {
                const user = review.customer?.userTenant?.user;
                const firstName = user?.firstName?.trim();
                const lastName = user?.lastName?.trim();

                const avatarText =
                  firstName && lastName
                    ? `${firstName[0]}${lastName[0]}`.toUpperCase()
                    : 'U';

                const displayName =
                  firstName && lastName
                    ? `${firstName} ${lastName}`
                    : 'Verified Buyer';

                return (
                  <Box key={review.id} sx={{mb: 3}}>
                    <Typography paragraph>{review.review}</Typography>
                    <Stack
                      direction="row"
                      spacing={1}
                      mb={3}
                      mt={3}
                      flexWrap="wrap"
                    >
                      {review.previewAssets?.map((url, i) => (
                        <Box
                          key={i}
                          component="img"
                          src={url}
                          sx={{
                            width: 64,
                            height: 64,
                            objectFit: 'cover',
                            borderRadius: 1,
                            cursor: 'pointer',
                          }}
                          onClick={() => window.open(url, '_blank')}
                        />
                      ))}
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Avatar sx={{bgcolor: '#9E3393', width: 32, height: 32}}>
                        {avatarText}
                      </Avatar>
                      <Typography variant="subtitle2" component="span">
                        <strong>{displayName}</strong>
                      </Typography>
                      <TickCircle size="18" color="#9E3393" variant="Bold" />
                      <Typography variant="caption" color="#00004F">
                        {formatDate(review.createdOn)}
                      </Typography>
                      <Rating
                        name={`review-rating-${review.id}`}
                        value={review.rating}
                        readOnly
                        precision={0.5}
                        size="small"
                        sx={{
                          ml: 2,
                          '& .MuiRating-iconFilled': {color: '#FDCC0D'},
                          '& .MuiRating-iconEmpty': {color: '#ccc'},
                        }}
                      />
                    </Stack>
                    {index < firstThree.length - 1 && <Divider sx={{my: 3}} />}
                  </Box>
                );
              })}

              {reviews.length > 3 && (
                <Box sx={{textAlign: 'center', mt: 3}}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => setModalOpen(true)}
                  >
                    See more Reviews
                  </Button>
                </Box>
              )}
              <AllReviewsModal
                open={modalOpen}
                onClose={() => setModalOpen(false)}
                reviews={reviews}
              />
            </>
          )}
        </Box>
      </Box>

      <Box maxWidth="sm" sx={{mt: 4}}>
        <Typography variant="h5" sx={{mb: 2, color: '#00004F'}}>
          Disclaimer
        </Typography>

        <Box
          sx={{
            p: 3,
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: '0px 0px 0px 1px rgba(0,0,0,0.1)',
            border: '1px solid #e0e0e0',
          }}
        >
          <Typography
            variant="h6"
            color="text.secondary"
            dangerouslySetInnerHTML={{
              __html: productDisclaimerContent
                ? getHtmlContent(productDisclaimerContent)
                : '<p>No legend content available.</p>',
            }}
          />
        </Box>
      </Box>
    </Box>
  );
}
