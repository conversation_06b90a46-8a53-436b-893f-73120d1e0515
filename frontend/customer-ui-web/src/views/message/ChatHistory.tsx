import {Avatar, Box, Stack, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {useEffect, useRef} from 'react';
import {ChatMessage} from 'types/chat';

dayjs.extend(relativeTime);

const ChatHistory: React.FC<{
  messages: ChatMessage[];
  currentUserId: string;
}> = ({messages, currentUserId}) => {
  const theme = useTheme();
  const bottomRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({behavior: 'smooth', block: 'end'});
  }, [messages]);

  return (
    <Stack spacing={2}>
      {messages.map((msg, idx) => {
        const isSender = msg.senderId === currentUserId;
        const time = dayjs(msg.createdOn || msg.ModifiedOn).format('hh:mm A');

        const sender = msg.sender;
        const initials =
          `${sender?.firstName?.[0] ?? ''}${sender?.lastName?.[0] ?? ''}`.toUpperCase();

        const avatarUrl = sender?.presignedPhotoUrl || sender?.photoUrl || '';

        return (
          <Box
            key={msg.id || idx}
            display="flex"
            justifyContent={isSender ? 'flex-end' : 'flex-start'}
          >
            <Stack
              direction={isSender ? 'row-reverse' : 'row'}
              spacing={1}
              sx={{maxWidth: '75%'}}
            >
              {/* Avatar */}
              <Avatar
                src={avatarUrl || undefined}
                sx={{width: 32, height: 32, fontSize: '0.875rem'}}
              >
                {!avatarUrl && initials}
              </Avatar>

              {/* Bubble */}
              <Box
                sx={{
                  px: 1.5,
                  py: 1,
                  bgcolor: isSender ? theme.palette.primary.main : 'grey.100',
                  color: isSender ? '#fff' : theme.palette.text.primary,
                  borderRadius: 2,
                  maxWidth: '100%',
                  position: 'relative', // ✅ for absolute time positioning
                  minWidth: 100, // optional: keeps some space for time
                }}
              >
                <Typography variant="body2" sx={{whiteSpace: 'pre-wrap'}}>
                  {msg.message}
                </Typography>

                {/* Time */}
                <Typography
                  component="span"
                  sx={{
                    fontSize: '0.7rem', // ✅ smaller time
                    display: 'block',
                    textAlign: 'right',
                    color: isSender ? '#e0e0e0' : 'text.secondary',
                    mt: 0.25, // small space from message
                  }}
                >
                  {time}
                </Typography>
              </Box>
            </Stack>
          </Box>
        );
      })}
    </Stack>
  );
};

export default ChatHistory;
