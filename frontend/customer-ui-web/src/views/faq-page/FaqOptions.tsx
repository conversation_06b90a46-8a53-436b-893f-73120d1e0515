'use client';
import {
  Typo<PERSON>,
  Box,
  Card,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
} from '@mui/material';

import {ArrowDown2} from 'iconsax-react';
import draftToHtml from 'draftjs-to-html';
import {useGetFaqsQuery} from 'redux/faq/faqApiSlice';
import {Faq} from 'types/faq';
function FaqPage() {
  const {data: faqs, isLoading} = useGetFaqsQuery();
  const groupedFaqs = faqs?.reduce((acc: Record<string, Faq[]>, faq) => {
    if (!acc[faq.category]) acc[faq.category] = [];
    acc[faq.category].push(faq);
    return acc;
  }, {});

  const renderAnswerHtml = (answer: string) => {
    const isHtml = /<\/?[a-z][\s\S]*>/i.test(answer);
    try {
      if (isHtml) return answer;
      return draftToHtml(JSON.parse(answer));
    } catch {
      return answer;
    }
  };
  return (
    <Grid
      container
      sx={{
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Card
        sx={{
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          width: '100%',
          maxWidth: '100%',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 'bold',
            color: '#4A4A4A',
            p: 1,
          }}
        >
          Frequently asked questions
        </Typography>

        {isLoading ? (
          <Box textAlign="center" py={4}>
            <Typography variant="body1" sx={{mb: 2}}>
              Loading FAQs...
            </Typography>
            <CircularProgress />
          </Box>
        ) : (
          Object.entries(groupedFaqs ?? {}).map(([category, faqs]) => (
            <Accordion
              key={category}
              disableGutters
              sx={{
                mt: 2,
                border: '1px solid #e0e0e0',
                borderRadius: 2,
                boxShadow: 'none',
                '&:before': {display: 'none'},
              }}
            >
              <AccordionSummary
                expandIcon={<ArrowDown2 size="16" />}
                sx={{
                  fontWeight: 600,
                  fontSize: '16px',
                  px: 2,
                  py: 1,
                  backgroundColor: '#f9f9f9',
                  borderRadius: 2,
                  color: ' #00004F',
                }}
              >
                {category}
              </AccordionSummary>
              <AccordionDetails sx={{px: 2, pt: 1, pb: 2}}>
                {faqs.map(faq => (
                  <Accordion
                    key={faq.id}
                    disableGutters
                    sx={{
                      mb: 1,
                      border: '1px solid #f0f0f0',
                      borderRadius: 2,
                      boxShadow: 'none',
                      '&:before': {display: 'none'},
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ArrowDown2 size="14" />}
                      sx={{
                        fontSize: '14px',
                        fontWeight: 500,
                        px: 2,
                        py: 1,
                      }}
                    >
                      {faq.question}
                    </AccordionSummary>
                    <AccordionDetails
                      sx={{backgroundColor: '#FAFAFA', borderRadius: 2}}
                    >
                      <div
                        dangerouslySetInnerHTML={{
                          __html: renderAnswerHtml(faq.answer),
                        }}
                      />
                    </AccordionDetails>
                  </Accordion>
                ))}
              </AccordionDetails>
            </Accordion>
          ))
        )}
      </Card>
    </Grid>
  );
}

export default FaqPage;
