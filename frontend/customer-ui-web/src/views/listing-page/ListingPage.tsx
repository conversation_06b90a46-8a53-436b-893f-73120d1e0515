'use client';

import {useMemo, useEffect, useState, useCallback} from 'react';
import {Grid} from '@mui/material';
import {useSearchParams} from 'next/navigation';
import {getSearchParam} from 'constants/product';
import {useAppSelector} from 'redux/hooks';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {useFiltersQuery, useLazySearchQuery} from 'redux/search/searchApiSlice';
import {useGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';

import {FilterValue, IFilterWithKeyword} from 'types/filter';
import ProductFilters from 'views/products/ProductFilters';
import ProductList from 'views/products/ProductListing';
import FilterSortProducts from 'views/products/QuikFilter';
import ScrollableCategoryList from 'views/products/ProductSubCategory';

interface Category {
  id: string;
  label: string;
  img: string;
  parentId?: string;
}

export default function ListingPage() {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get('search')?.toLowerCase() || '';
  const facetValueIdsParam = searchParams.get('facetValueIds');
  const collectionIdsParam = searchParams.get('collectionIds');

  const facetValueIds = facetValueIdsParam?.split(',').filter(Boolean);
  const collectionIds = collectionIdsParam?.split(',').filter(Boolean);

  const [appliedFilters, setAppliedFilters] = useState<FilterValue[]>([]);
  const [sortOrder, setSortOrder] = useState<
    'featured' | 'lowToHigh' | 'highToLow'
  >('featured');

  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});
  const searchParam = getSearchParam(isLoggedIn, user?.profileId);

  const {data: filters} = useFiltersQuery(
    {
      keyword: searchQuery,
      facetValueIds,
      collectionIds,
    },
    {skip: false},
  );

  const collectionFilter = useMemo(() => {
    return filters?.find(f =>
      f.values?.some(v => v.metadata?.previewUrl && v.metadata?.parentId),
    );
  }, [filters]);

  const baseCategories: Category[] = useMemo(() => {
    const collections = collectionFilter?.values;
    if (!collections) return [];
    return collections.map(col => ({
      id: col.value,
      label: col.label,
      img: col.metadata?.previewUrl ?? '',
      parentId: col.metadata?.parentId ?? undefined,
    }));
  }, [collectionFilter]);

  const uniqueParentIds = useMemo(() => {
    return Array.from(new Set(baseCategories.map(c => c.id).filter(Boolean)));
  }, [baseCategories]);

  const {data: subCollections} = useGetCollectionsQuery({
    where: {
      parentId: {inq: uniqueParentIds},
    },
    include: [
      {
        relation: 'featuredAsset',
        scope: {
          fields: {preview: true, id: true},
        },
      },
    ],
    fields: {
      name: true,
      id: true,
      parentId: true,
      featuredAssetId: true,
    },
  });

  const subCategories: Category[] = useMemo(() => {
    if (!subCollections) return [];
    return subCollections.map(col => ({
      id: col.id,
      label: col.name,
      img: col.featuredAsset?.preview ?? '',
      parentId: col.parentId,
    }));
  }, [subCollections]);

  const allCategories: Category[] = [...baseCategories, ...subCategories];
  const [fetchProductsApi, {data: products, isLoading, isFetching}] =
    useLazySearchQuery();

  useEffect(() => {
    const params: IFilterWithKeyword = {...searchParam};

    if (searchQuery) params.keyword = searchQuery;
    if (facetValueIds?.length) params.facetValueIds = facetValueIds;
    if (collectionIds?.length) params.collectionIds = collectionIds;

    params.where = appliedFilters.length ? prePareFilter() : undefined;
    params.priceRange = getPriceFilter();

    fetchProductsApi(params).unwrap();
  }, [
    searchQuery,
    appliedFilters,
    sortOrder,
    facetValueIdsParam,
    collectionIdsParam,
  ]);

  const sortedProducts = useMemo(() => {
    if (!products) return [];

    if (sortOrder === 'lowToHigh') {
      return [...products].sort(
        (a, b) =>
          parseFloat(a.productVariantPrice?.price ?? '0') -
          parseFloat(b.productVariantPrice?.price ?? '0'),
      );
    }

    if (sortOrder === 'highToLow') {
      return [...products].sort(
        (a, b) =>
          parseFloat(b.productVariantPrice?.price ?? '0') -
          parseFloat(a.productVariantPrice?.price ?? '0'),
      );
    }

    return products;
  }, [products, sortOrder]);

  const totalProducts = sortedProducts.length;

  const toggleFilter = useCallback(
    (selectedFilter: FilterValue, isPrice?: boolean) => {
      setAppliedFilters(prev => {
        const exists = prev.some(
          f =>
            f.label === selectedFilter.label &&
            f.value === selectedFilter.value,
        );

        if (isPrice) {
          const withoutPrice = prev.filter(
            f => f.label !== selectedFilter.label,
          );
          return [...withoutPrice, selectedFilter];
        }

        return exists
          ? prev.filter(
              f =>
                !(
                  f.label === selectedFilter.label &&
                  f.value === selectedFilter.value
                ),
            )
          : [...prev, selectedFilter];
      });
    },
    [],
  );

  const facetFilters = useMemo(() => {
    if (!filters?.length) return [];
    return filters
      .filter(
        item =>
          item.isFacet === true &&
          Array.isArray(item.values) &&
          item.values.length > 0,
      )
      .flatMap(item => item.values ?? []);
  }, [filters]);

  useEffect(() => {
    const params: IFilterWithKeyword = {...searchParam};

    if (searchQuery) params.keyword = searchQuery;
    if (collectionIds?.length) params.collectionIds = collectionIds;

    // ✅ Now this works fine
    const selectedFacetValueIds = appliedFilters
      .filter(f => facetFilters.some(ff => ff.value === f.value))
      .map(f => f.value);

    if (selectedFacetValueIds.length) {
      params.facetValueIds = selectedFacetValueIds;
    }

    params.where = appliedFilters.length ? prePareFilter() : undefined;
    params.priceRange = getPriceFilter();

    fetchProductsApi(params).unwrap();
  }, [
    searchQuery,
    appliedFilters,
    sortOrder,
    facetFilters, // ✅ No issue anymore
    collectionIdsParam,
  ]);

  const prePareFilter = () => {
    const otherFilters = appliedFilters.filter(f => f.label !== 'Price');
    const ids = otherFilters.flatMap(item => item.productVariantIds || []);
    return ids.length ? {and: [{id: {inq: ids}}]} : undefined;
  };

  const getPriceFilter = () => {
    const priceFilter = appliedFilters.find(f => f.label === 'Price');
    return priceFilter?.value?.includes('-')
      ? priceFilter.value.split('-').map(Number)
      : undefined;
  };

  const selectedFacet = useMemo(() => {
    return appliedFilters
      .filter(f => facetFilters.some(ff => ff.value === f.value))
      .map(f => f.value);
  }, [appliedFilters, facetFilters]);

  return (
    <Grid container spacing={2} px={2}>
      <Grid item xs={12}>
        <ScrollableCategoryList categories={allCategories} />
      </Grid>

      <Grid item xs={12}>
        <FilterSortProducts
          facets={facetFilters}
          sortOrder={sortOrder}
          onSortChange={setSortOrder}
          categories={allCategories}
          productCount={totalProducts}
          selectedFacet={selectedFacet}
          onToggleFilter={toggleFilter}
        />
      </Grid>

      <Grid item xs={12} container spacing={2}>
        <Grid item xs={12} sm={4} md={3}>
          <ProductFilters
            filters={filters ?? []}
            onToggleFilter={toggleFilter}
            appliedFilters={appliedFilters}
          />
        </Grid>

        <Grid item xs={12} sm={8} md={9}>
          <ProductList
            products={sortedProducts}
            isLoading={isLoading}
            isFetching={isFetching}
          />
        </Grid>
      </Grid>
    </Grid>
  );
}
