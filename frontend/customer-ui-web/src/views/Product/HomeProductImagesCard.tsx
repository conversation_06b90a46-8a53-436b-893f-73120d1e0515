'use client';
import React, {useMemo} from 'react';
import {
  CardContent,
  CardMedia,
  Typography,
  IconButton,
  Box,
  Rating,
  Grid,
  Button,
  Stack,
  Tooltip,
} from '@mui/material';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import Image from 'next/image';
import {NoProduct} from 'views/products/NoProduct';
import {useAppSelector} from 'redux/hooks';

interface Product {
  id: string;
  image: string;
  title: string;
  description: string;
  rating: number;
  price: number;
  originalPrice: number;
}

interface ViewedProductCardProps {
  productVariants: any[];
  isLoading: boolean;
  isError: boolean;
}

const ProductCard: React.FC<{product: Product}> = ({product}) => {
  const discount =
    product.originalPrice > 0
      ? Math.round(
          ((product.originalPrice - product.price) / product.originalPrice) *
            100,
        )
      : 0;

  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);

  return (
    <Box
      sx={{
        height: '100%',
        minHeight: '380px',
        margin: '0.5rem',
        borderRadius: '1rem',
        overflow: 'hidden',
        boxShadow: 'none',
        backgroundColor: 'none',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        '&:hover': {
          backgroundColor: '#d9dce0',
        },
      }}
    >
      <Box sx={{position: 'relative'}}>
        <CardMedia
          component="img"
          height="180"
          image={product.image}
          alt={product.title}
          sx={{
            width: '100%',
            maxWidth: '600px',
            height: '250px',
            borderRadius: '20px',
            padding: '8px',
            objectFit: 'fill',
          }}
        />

        <IconButton
          aria-label="add to favorites"
          sx={{
            position: 'absolute',
            top: '13px',
            right: '13px',
            backgroundColor: '#fff',
            borderRadius: '50%',
            boxShadow: 1,
            zIndex: 2,
          }}
        >
          <FavoriteBorderIcon />
        </IconButton>
        <IconButton
          aria-label="similar"
          sx={{
            position: 'absolute',
            bottom: '13px',
            right: '13px',
            backgroundColor: 'rgba(255, 255, 255, 1)',
            borderRadius: '50%',
            width: 28,
            height: 28,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 1)',
            },
            zIndex: 2,
          }}
        >
          <Image
            src="/assets/images/icons/similarIcon.svg"
            alt="Similar Icon"
            width={20}
            height={20}
          />
        </IconButton>
      </Box>

      <CardContent
        sx={{
          height: 'calc(100% - 200px)',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Grid container spacing={4} sx={{flexGrow: 1}}>
          <Grid item xs={8}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Tooltip title={product.title}>
                  <Typography
                    component="div"
                    gutterBottom
                    sx={{
                      fontWeight: '600',
                      overflow: 'hidden',
                      fontSize: {xs: '0.8rem', sm: '0.9rem'},
                      lineHeight: '1.2',
                      textAlign: 'left',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                      cursor: 'pointer',
                    }}
                  >
                    {product.title}
                  </Typography>
                </Tooltip>
                <Tooltip title={product.description}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      lineHeight: '1.4',
                      fontSize: {xs: '0.6rem', sm: '0.7rem'},
                      textAlign: 'left',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {product.description}
                  </Typography>
                </Tooltip>
              </Grid>
              <Grid item xs={12}>
                <Stack display={'flex'} direction={'row'} spacing={1}>
                  <Rating
                    name="read-only"
                    value={product.rating}
                    readOnly
                    sx={{fontSize: {xs: '0.8rem', sm: '1rem'}}}
                  />
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{fontSize: {xs: '0.7rem', sm: '0.8rem'}}}
                  >
                    ({product.rating ? (product.rating * 25).toFixed(0) : 0})
                  </Typography>
                </Stack>
              </Grid>
              <Grid container alignItems="flex-start">
                <Button
                  onClick={() => {
                    const path = isLoggedIn ? '/cart' : '/login';
                    window.open(path, '_blank');
                  }}
                  sx={{
                    minWidth: '6.5rem',
                    borderRadius: '1rem',
                    color: 'black',
                    backgroundColor: 'white',
                    border: '0.5px solid black',
                    '&:hover': {backgroundColor: '#f0f0f0'},
                    fontSize: {xs: '0.7rem', sm: '0.8rem'},
                  }}
                >
                  Add to Cart
                </Button>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={4}>
            <Grid container spacing={1}>
              <Grid item xs={12}>
                <Typography variant="body2" color="primary" fontWeight={'bold'}>
                  Deal: -{discount}%
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="body2" color="secondary">
                    M.R.P:
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{textDecoration: 'line-through'}}
                  >
                    ₹{product.originalPrice.toFixed(2)}{' '}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Typography
                  variant="h5"
                  component="div"
                  sx={{fontWeight: 'bold', fontSize: {xs: '1rem', sm: '1rem'}}}
                >
                  ₹ {product.price.toFixed(2)}{' '}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </Box>
  );
};

const ViewedProductCard: React.FC<ViewedProductCardProps> = ({
  productVariants,
  isLoading,
  isError,
}) => {
  const productsToDisplay: Product[] = useMemo(() => {
    if (!productVariants || productVariants.length === 0) {
      return [];
    }

    return productVariants.map(variant => {
      const averageRating =
        variant.reviews && variant.reviews.length > 0
          ? variant.reviews.reduce(
              (acc: number, review: any) => acc + review.rating,
              0,
            ) / variant.reviews.length
          : 0;

      const price = Number(variant.productVariantPrice?.price) || 0;
      const originalPrice = Number(variant.productVariantPrice?.mrp) || 0;

      return {
        id: variant.id,
        image:
          variant.featuredAsset?.previewUrl ||
          'https://via.placeholder.com/250',
        title: variant.name,
        description:
          variant.product?.description || 'No description available.',
        rating: Math.round(averageRating),
        price: price,
        originalPrice: originalPrice,
      };
    });
  }, [productVariants]);

  return (
    <Box
      sx={{
        height: 'calc(2 * 420px + 2 * 0.5rem + 2 * 1rem)',
        overflowY: 'auto',
        paddingX: {xs: 1, sm: 2, md: 3},
        paddingY: 2,
        borderRadius: '1rem',
      }}
    >
      <Grid container spacing={1} justifyContent="flex-start">
        {isLoading ? (
          <Grid item xs={12}>
            <Typography>Loading products...</Typography>
          </Grid>
        ) : isError ? (
          <Grid item xs={12}>
            <Typography color="error">Error loading products.</Typography>
          </Grid>
        ) : !productsToDisplay.length ? (
          <Grid item xs={12}>
            <NoProduct />
          </Grid>
        ) : (
          productsToDisplay.map(product => (
            <Grid item xs={12} sm={6} md={4} key={product.id}>
              <ProductCard product={product} />
            </Grid>
          ))
        )}
      </Grid>
    </Box>
  );
};

export default ViewedProductCard;
