'use client';

import React, {useMemo} from 'react';
import {Box, Grid, Typography, CardMedia, CardContent} from '@mui/material';
import {useRouter} from 'next/navigation';

interface Category {
  id: string;
  label: string;
  img: string;
  parentId?: string;
}

interface ScrollableGroupedCategoryListProps {
  categories: Category[];
}

const ScrollableGroupedCategoryList: React.FC<
  ScrollableGroupedCategoryListProps
> = ({categories}) => {
  const router = useRouter();
  const groupedByParent = useMemo(() => {
    const groups: Record<string, Category[]> = {};
    for (const cat of categories) {
      if (cat.parentId) {
        if (!groups[cat.parentId]) {
          groups[cat.parentId] = [];
        }
        groups[cat.parentId].push(cat);
      }
    }
    return groups;
  }, [categories]);

  const firstParentId = useMemo(() => {
    const keys = Object.keys(groupedByParent);
    return keys.length > 0 ? keys[0] : null;
  }, [groupedByParent]);

  const parentLabel = useMemo(() => {
    if (!firstParentId) return 'Unknown Category';
    const parent = categories.find(cat => cat.id === firstParentId);
    return parent?.label
      ? parent.label.charAt(0).toUpperCase() + parent.label.slice(1)
      : 'Unknown Category';
  }, [firstParentId, categories]);

  // const childCategories = firstParentId ? groupedByParent[firstParentId] : [];
  const childCategories = useMemo(() => {
    if (!firstParentId) return [];

    const seen = new Set<string>();
    return groupedByParent[firstParentId].filter(cat => {
      if (seen.has(cat.id)) return false;
      seen.add(cat.id);
      return true;
    });
  }, [groupedByParent, firstParentId]);

  if (!firstParentId || childCategories.length === 0) return null;

  return (
    <Grid
      container
      direction="column"
      sx={{p: 2, backgroundColor: '#edeff2', borderRadius: 2}}
    >
      <Box sx={{height: 200}}>
        {/* Parent label as heading */}
        <Typography variant="h5" fontWeight="bold" color="primary" gutterBottom>
          {parentLabel} and Accessories
        </Typography>

        {/* Scrollable child list */}
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            overflowX: 'auto',
            paddingBottom: 1,

            // Transparent scroll
            scrollbarWidth: 'thin',
            scrollbarColor: 'transparent transparent', // Firefox
            '&::-webkit-scrollbar': {
              height: '6px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'transparent',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'transparent',
              borderRadius: '10px',
            },
            '&:hover::-webkit-scrollbar-thumb': {
              backgroundColor: '#ccc', // shows thumb on hover
            },
          }}
        >
          {childCategories.map(child => (
            <Box
              key={child.id}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                minWidth: 120,
                mt: 1,
              }}
              onClick={() => router.push(`listing?collectionIds=${child.id}`)}
            >
              <CardMedia
                component="img"
                height="120"
                sx={{
                  borderRadius: '12px',
                  width: '120px',
                  objectFit: 'cover',
                  backgroundColor: '#f0f0f0',
                }}
                image={
                  child.img.startsWith('http')
                    ? child.img
                    : `https://assets.ecomdukes.in/${child.img}`
                }
                alt={child.label}
              />
              <CardContent sx={{textAlign: 'center', padding: 1}}>
                <Typography variant="body2" fontWeight="bold">
                  {child.label}
                </Typography>
              </CardContent>
            </Box>
          ))}
        </Box>
      </Box>
    </Grid>
  );
};

export default ScrollableGroupedCategoryList;
