'use client';

import React, {FC, useMemo, useState} from 'react';
import {
  Box,
  Typography,
  Chip,
  MenuItem,
  Select,
  FormControl,
  Grid,
  SelectChangeEvent,
} from '@mui/material';
import {FilterValue} from 'types/filter';

interface Category {
  id: string;
  label: string;
  img: string;
  parentId?: string;
}

interface Props {
  facets: FilterValue[];
  categories: Category[];
  selectedFacet?: string[];
  productCount: number;
  sortOrder: 'featured' | 'lowToHigh' | 'highToLow';
  onSortChange: (value: 'featured' | 'lowToHigh' | 'highToLow') => void;
  onFacetChange?: (value: string) => void;
  onToggleFilter?: (filter: FilterValue) => void;
}

const FilterSortProducts: FC<Props> = ({
  facets,
  categories,
  selectedFacet = [],
  sortOrder,
  onSortChange,
  onFacetChange,
  productCount,
  onToggleFilter,
}) => {
  const [open, setOpen] = useState(false);

  const handleSortChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value as 'featured' | 'lowToHigh' | 'highToLow';
    onSortChange(value);
    setOpen(false);
  };

  const groupedByParent = useMemo(() => {
    const groups: Record<string, Category[]> = {};
    for (const cat of categories) {
      if (cat.parentId) {
        if (!groups[cat.parentId]) {
          groups[cat.parentId] = [];
        }
        groups[cat.parentId].push(cat);
      }
    }
    return groups;
  }, [categories]);

  const firstParentId = useMemo(() => {
    const keys = Object.keys(groupedByParent);
    return keys.length > 0 ? keys[0] : null;
  }, [groupedByParent]);

  const parentLabel = useMemo(() => {
    if (!firstParentId) return null;
    const parent = categories.find(cat => cat.id === firstParentId);
    return parent?.label
      ? parent.label.charAt(0).toUpperCase() + parent.label.slice(1)
      : null;
  }, [firstParentId, categories]);

  return (
    <Grid container spacing={2} alignItems="center" sx={{px: 2, py: 3}}>
      {/* Title */}
      <Grid item xs={12} sm={6}>
        <Typography variant="h6" fontWeight={700} color="#0C004D">
          {parentLabel || 'All Products'}{' '}
          <Typography
            component="span"
            variant="body2"
            fontWeight={400}
            sx={{color: '#5F6C7B'}}
          >
            ({productCount} Product{productCount !== 1 ? 's' : ''})
          </Typography>
        </Typography>
      </Grid>

      {/* Sort */}
      <Grid
        item
        xs={12}
        sm={6}
        sx={{display: 'flex', justifyContent: 'flex-end'}}
      >
        <FormControl onMouseLeave={() => setOpen(false)}>
          <Select
            value={sortOrder}
            open={open}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            onChange={handleSortChange}
            displayEmpty
            inputProps={{'aria-label': 'Sort by'}}
            onMouseEnter={() => setOpen(true)}
            sx={{
              width: 200,
              borderRadius: '999px',
              px: 2,
              height: 40,
              fontWeight: 600,
              fontSize: '0.9rem',
              border: '1px solid #00004F',
              color: '#0C004D',
              backgroundColor: '#fff',
              '& .MuiSelect-select': {
                display: 'flex',
                alignItems: 'center',
                height: '40px',
                padding: 0,
              },
              '& fieldset': {border: 'none'},
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  backgroundColor: '#fff',
                  color: '#000',
                  borderRadius: '20px',
                  mt: 1,
                  '& .MuiMenuItem-root': {
                    fontSize: '0.9rem',
                    backgroundColor: '#fff',
                    color: '#000',
                    '&.Mui-selected': {
                      backgroundColor: '#00004A !important',
                      color: '#fff !important',
                    },
                    '&.Mui-selected:hover': {
                      backgroundColor: '#00004A !important',
                      color: '#fff !important',
                    },
                    '&:hover': {
                      backgroundColor: '#00004A',
                      color: '#fff',
                    },
                  },
                },
                onMouseLeave: () => setOpen(false), // closes when leaving menu
              },
            }}
            renderValue={() => (
              <Box display="flex" alignItems="center" gap={1}>
                <Typography fontWeight={600} fontSize="0.9rem" color="#0C004D">
                  Sort by :
                </Typography>
                <Typography fontWeight={700} fontSize="0.9rem" color="#0C004D">
                  {sortOrder === 'featured'
                    ? 'Relevancy'
                    : sortOrder === 'lowToHigh'
                      ? 'Price low to high'
                      : 'Price high to low'}
                </Typography>
              </Box>
            )}
          >
            <MenuItem value="featured">Relevancy</MenuItem>
            <MenuItem value="lowToHigh" sx={{mt: 1}}>
              Price low to high
            </MenuItem>
            <MenuItem value="highToLow" sx={{mt: 1}}>
              Price high to low
            </MenuItem>
          </Select>
        </FormControl>
      </Grid>

      {/* Quick Filters */}
      <Grid item xs={12}>
        <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
          <Typography variant="h5" fontWeight={600} sx={{mr: 10}}>
            Quick Filters
          </Typography>
          {facets.slice(0, 6).map(facet => {
            const isSelected = selectedFacet?.includes(facet.value);

            return (
              <Chip
                key={facet.value}
                label={facet.label}
                clickable
                onClick={() => {
                  if (onToggleFilter) {
                    onToggleFilter(facet);
                  } else {
                    onFacetChange?.(facet.value);
                  }
                }}
                sx={{
                  minWidth: '6.5rem',
                  borderRadius: '1rem',
                  fontSize: {xs: '0.7rem', sm: '0.8rem'},
                  fontWeight: 500,
                  px: 2,
                  height: 36,
                  cursor: 'pointer',
                  ml: 1,
                  color: isSelected ? 'white' : 'black',
                  backgroundImage: isSelected
                    ? 'none'
                    : 'linear-gradient(to right, #9A2D8E 50%, white 50%)',
                  backgroundColor: isSelected ? '#9A2D8E' : 'transparent',
                  backgroundSize: isSelected ? 'initial' : '200% 100%',
                  backgroundPosition: isSelected ? 'initial' : 'right bottom',
                  transition:
                    'background-position 0.4s ease-in-out, color 0.4s ease-in-out',
                  border: isSelected ? 'none' : '0.5px solid black',
                  '&:hover': {
                    backgroundPosition: 'left bottom',
                    color: 'white',
                  },
                }}
              />
            );
          })}
        </Box>
      </Grid>
    </Grid>
  );
};

export default FilterSortProducts;
