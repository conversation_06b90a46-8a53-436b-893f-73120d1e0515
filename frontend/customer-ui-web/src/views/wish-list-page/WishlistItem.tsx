import React, {useState} from 'react';
import {
  Box,
  CardMedia,
  Typography,
  Button,
  IconButton,
  Rating,
} from '@mui/material';
import {Trash} from 'iconsax-react';
import {useRouter} from 'next/navigation';
import {Wishlist} from 'types/wishlist';
import {useAddItemToCartMutation} from 'redux/ecom/cartApiSlice';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {CartItem} from 'types/cart';

interface WishlistItemProps {
  item: Wishlist;
  handleRemove: (id: string) => void;
  isRemoving?: boolean;
}

const WishlistItem: React.FC<WishlistItemProps> = ({
  item,
  handleRemove,
  isRemoving,
}) => {
  const router = useRouter();
  const [isAddedToCart, setIsAddedToCart] = useState(false);
  const [addItemToCart, {isLoading: isAddingToCart}] =
    useAddItemToCartMutation();

  const handleAddToCart = async (
    e: React.MouseEvent,
    productVariantId: string,
  ) => {
    e.stopPropagation();
    const cartItem: Partial<CartItem> = {
      productVariantId,
      quantity: 1,
    };

    await addItemToCart(cartItem).unwrap();
    setIsAddedToCart(true);
    openSnackbar({
      open: true,
      message: 'Product added to Cart',
      variant: 'alert',
      alert: {color: 'success'},
    } as SnackbarProps);
  };

  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };

  const pv = item.productVariant;
  if (!pv?.id) return null;

  const totalRatings = item?.productVariant?.reviews?.length ?? 0;

  const avgRating =
    totalRatings > 0
      ? item.productVariant?.reviews?.reduce((sum, r) => sum + r.rating, 0)! /
        totalRatings
      : 0;

  const price = Number(pv.productVariantPrice?.price ?? 100);
  const mrp = Number(pv.productVariantPrice?.mrp ?? 100);
  const showDiscount = mrp > price;
  const discount = showDiscount ? Math.round(((mrp - price) / mrp) * 100) : 0;

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        bgcolor: '#F8F8F8',
        borderRadius: 3,
        p: 2,
        gap: 2,
        width: '100%',
      }}
    >
      {/* IMAGE */}
      <CardMedia
        component="img"
        sx={{
          width: 100,
          height: 100,
          borderRadius: 2,
          objectFit: 'cover',
          objectPosition: 'center',
          cursor: 'pointer',
        }}
        image={pv.featuredAsset?.previewUrl ?? ''}
        alt={pv.name}
        onClick={() => router.push(`/product-details/${pv.id}`)}
      />

      {/* MIDDLE CONTENT */}
      <Box sx={{flex: 1, display: 'flex', flexDirection: 'column', gap: 0.5}}>
        {/* Title */}
        <Typography
          fontWeight="bold"
          fontSize="15px"
          onClick={() => router.push(`/product-details/${pv.id}`)}
          sx={{cursor: 'pointer'}}
        >
          {pv.name}
        </Typography>

        {/* Subtitle */}
        <Typography fontSize="13px" color="textSecondary">
          {pv?.product?.description ?? 'Table with air purifier'}
        </Typography>

        {/* Deal + Price */}
        <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
          <Typography fontSize="13px" color="#9A2D8E">
            Deal: -{discount}%
          </Typography>
          <Typography fontWeight="bold" fontSize="14px" color="#151B54">
            ₹ {price}
          </Typography>
          {showDiscount && (
            <Typography
              sx={{
                textDecoration: 'line-through',
                color: 'gray',
                fontSize: '12px',
              }}
            >
              M.R.P: ₹{mrp}
            </Typography>
          )}
        </Box>
      </Box>

      {/* RIGHT ACTIONS */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        {/* RATING */}
        <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}}>
          <Rating
            name="read-only"
            value={parseFloat(avgRating.toFixed(1))}
            precision={0.1}
            readOnly
            size="small"
          />
          <Typography variant="caption" color="text.secondary">
            ({totalRatings})
          </Typography>
        </Box>

        {isAddedToCart ? (
          <Button
            onClick={handleGoToCart}
            variant="outlined"
            sx={{
              borderRadius: '20px',
              textTransform: 'none',
              padding: '4px 14px',
              fontSize: '13px',
              color: 'black',
              borderColor: 'gray',
              backgroundImage:
                'linear-gradient(to right, #9A2D8E 50%, white 50%)',
              backgroundSize: '200% 100%',
              backgroundPosition: 'right bottom',
              transition:
                'background-position 0.4s ease-in-out, color 0.4s ease-in-out',
              '&:hover': {
                backgroundPosition: 'left bottom',
                color: 'white',
              },
            }}
          >
            Go to cart
          </Button>
        ) : (
          <Button
            onClick={e => handleAddToCart(e, pv.id)}
            disabled={isAddingToCart}
            variant="outlined"
            sx={{
              borderRadius: '20px',
              textTransform: 'none',
              padding: '4px 14px',
              fontSize: '13px',
              color: 'black',
              borderColor: 'gray',
              backgroundImage:
                'linear-gradient(to right, #9A2D8E 50%, white 50%)',
              backgroundSize: '200% 100%',
              backgroundPosition: 'right bottom',
              transition:
                'background-position 0.4s ease-in-out, color 0.4s ease-in-out',
              '&:hover': {
                backgroundPosition: 'left bottom',
                color: 'white',
              },
            }}
          >
            {isAddingToCart ? 'Adding...' : 'Add to cart'}
          </Button>
        )}

        <IconButton
          onClick={() => handleRemove(item?.id ?? '')}
          disabled={isRemoving}
          sx={{
            color: '#151B54',
            '&:hover': {
              color: '#d32f2f',
            },
            '&.Mui-disabled': {
              color: '#c0c0c0',
            },
          }}
        >
          <Trash />
        </IconButton>
      </Box>
    </Box>
  );
};

export default WishlistItem;
