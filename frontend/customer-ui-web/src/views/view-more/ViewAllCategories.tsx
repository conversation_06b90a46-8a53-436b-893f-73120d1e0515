'use client';

import {Box, Grid, Typography, Button, CircularProgress} from '@mui/material';
import {useEffect, useState} from 'react';
import {Collection, ProductVariant} from 'types/product';
import {IFilter} from 'types/api';
import {useGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';
import ProductCard from 'views/products/ProductCard';
import {useGetProductVariantsQuery} from 'redux/ecom/productVariantApiSlice';

export default function ViewAllCategories() {
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);

  const {data: collections, isLoading: isCollectionsLoading} =
    useGetCollectionsQuery({
      fields: {id: true, name: true},
    });

  useEffect(() => {
    if (collections && collections.length > 0 && !selectedCollectionId) {
      setSelectedCollectionId(collections[0].id);
    }
  }, [collections, selectedCollectionId]);

  const productVariantFilter: IFilter | undefined = selectedCollectionId
    ? {
        include: [
          {
            relation: 'product',
            scope: {
              where: {
                collectionId: selectedCollectionId,
              },
            },
            required: true,
          },
          {
            relation: 'productVariantPrice',
          },
          {
            relation: 'featuredAsset',
          },
        ],
      }
    : undefined;

  const {data: productVariants, isLoading: isVariantsLoading} =
    useGetProductVariantsQuery(productVariantFilter);

  return (
    <Box p={3}>
      <Typography variant="h4" mb={3}>
        All Categories
      </Typography>

      {/* Category Chips Horizontal Scroll */}
      {isCollectionsLoading ? (
        <CircularProgress size={24} />
      ) : (
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            mb: 3,
            overflowX: 'auto',
            pb: 1,
            '&::-webkit-scrollbar': {
              height: 6,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'transparent',
            },
            '&:hover::-webkit-scrollbar-thumb': {
              backgroundColor: '#ccc',
              borderRadius: 4,
            },
            scrollbarWidth: 'thin',
            scrollbarColor: 'transparent transparent',
          }}
        >
          {collections?.map((collection: Collection) => (
            <Box key={collection.id} flexShrink={0}>
              <Button
                onClick={() => setSelectedCollectionId(collection.id)}
                sx={{
                  borderRadius: '1rem',
                  minWidth: 120,
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  fontSize: {xs: '0.7rem', sm: '0.8rem'},
                  border: '0.5px solid black',
                  color:
                    selectedCollectionId === collection.id ? 'white' : 'black',
                  backgroundImage:
                    selectedCollectionId === collection.id
                      ? 'linear-gradient(to right, #9A2D8E 100%, #9A2D8E 100%)'
                      : 'linear-gradient(to right, #9A2D8E 50%, white 50%)',
                  backgroundSize: '200% 100%',
                  backgroundPosition: 'right bottom',
                  transition:
                    'background-position 0.4s ease-in-out, color 0.4s ease-in-out',
                  '&:hover': {
                    backgroundPosition: 'left bottom',
                    color: 'white',
                  },
                }}
              >
                {collection.name}
              </Button>
            </Box>
          ))}
        </Box>
      )}

      {/* Product Grid */}
      {isVariantsLoading ? (
        <CircularProgress />
      ) : (
        <Grid container spacing={3}>
          {productVariants?.map((product: ProductVariant) => (
            <Grid item xs={12} sm={6} md={3} key={product.id}>
              <ProductCard productVariant={product} />
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
}
