/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
import {inject} from '@loopback/core';
import {ClickPostRecommendationResponse} from '../interfaces';
import {ShippingRule} from '../models';
import {ClickPost} from './click-post.service';
import {ShippingRuleConditionType} from '@local/core';

export class CarrierSelectionService {
  constructor(
    @inject('services.ClickPost')
    private clickPostService: ClickPost,
  ) {}

  /**
   * Main method to determine the carrier partner
   */
  async determineCarrierPartner(
    orderData: any,
    shippingRules: ShippingRule[],
  ): Promise<number> {
    try {
      // 1. Get recommendations from ClickPost
      const recommendations = await this.getClickPostRecommendations(orderData);

      // 2. Filter active rules and sort by priority
      const activeRules = shippingRules
        .filter(rule => rule.isActive)
        .sort((a, b) => (a?.priority ?? 1) - (b?.priority ?? 1));

      // 3. Apply rules in priority order
      for (const rule of activeRules) {
        const ruleResult = this.applyShippingRule(rule, orderData);
        if (ruleResult) {
          // Check if the rule result is available in recommendations
          const recommendedCarrier = this.findCarrierInRecommendations(
            ruleResult,
            recommendations,
          );
          if (recommendedCarrier) {
            return recommendedCarrier;
          }
        }
      }

      // 4. Fallback to first recommendation if no rules matched or rule results not in recommendations
      const fallbackCarrier = this.getFallbackCarrier(recommendations);
      return fallbackCarrier;
    } catch (error) {
      console.error('Error determining carrier partner:', error);
      // Fallback to default if something goes wrong
      return 105; // Default to Shiprocket
    }
  }

  /**
   * Get recommendations from ClickPost
   */
  private async getClickPostRecommendations(
    orderData: any,
  ): Promise<ClickPostRecommendationResponse> {
    const requestBody = this.prepareRecommendationRequest(orderData);
    return this.clickPostService.getRecommendations(
      requestBody,
      process.env.CLICKPOST_API_KEY ?? '',
    );
  }

  /**
   * Prepare the recommendation request for ClickPost
   */
  private prepareRecommendationRequest(orderData: any): any {
    return {
      pickup_pincode: orderData.pickupPincode,
      drop_pincode: orderData.dropPincode,
      weight: orderData.weight,
      invoice_value: orderData.orderValue,
      order_type: orderData.paymentMethod === 'COD' ? 'COD' : 'PREPAID',
      delivery_type: 'FORWARD',
    };
  }

  /**
   * Apply shipping rule based on condition type
   */
  private applyShippingRule(rule: ShippingRule, orderData: any): string | null {
    try {
      switch (rule.conditionType) {
        case ShippingRuleConditionType.PAYMENT_MODE:
          return this.applyPaymentModeRule(rule, orderData.paymentMethod);
        case ShippingRuleConditionType.WEIGHT:
          return this.applyWeightRule(rule, orderData.weight);
        case ShippingRuleConditionType.ORDER_VALUE:
          return this.applyOrderValueRule(rule, orderData.orderValue);
        case ShippingRuleConditionType.ORDER_QTY:
          return this.applyOrderQtyRule(rule, orderData.quantity);
        case ShippingRuleConditionType.STATE:
          return this.applyStateRule(rule, orderData.dropState);
        case ShippingRuleConditionType.CITY:
          return this.applyCityRule(rule, orderData.dropCity);
        case ShippingRuleConditionType.PINCODE:
          return this.applyPincodeRule(rule, orderData.dropPincode);
        case 'product_category' as ShippingRuleConditionType:
          return this.applyProductCategoryRule(rule, orderData.productCategory);
        default:
          console.warn(`Unknown condition type: ${rule.conditionType}`);
          return null;
      }
    } catch (error) {
      console.error(`Error applying rule ${rule.id}:`, error);
      return null;
    }
  }

  /**
   * Apply payment mode rule
   */
  private applyPaymentModeRule(
    rule: ShippingRule,
    paymentMethod: string,
  ): string | null {
    if (!paymentMethod) {
      console.warn('Payment method is undefined or null');
      return null;
    }

    const normalizedPaymentMethod = paymentMethod.toLowerCase().trim();
    const normalizedConditionValues = rule.conditionValues.map(value =>
      value.toLowerCase().trim(),
    );

    return normalizedConditionValues.includes(normalizedPaymentMethod)
      ? rule.result
      : null;
  }

  /**
   * Apply weight rule
   */
  private applyWeightRule(rule: ShippingRule, weight: number): string | null {
    if (weight === undefined || weight === null || weight < 0) {
      console.warn('Invalid weight value:', weight);
      return null;
    }

    for (const range of rule.conditionValues) {
      if (this.isValueInRange(weight, range)) {
        return rule.result;
      }
    }
    return null;
  }

  /**
   * Apply order value rule
   */
  private applyOrderValueRule(
    rule: ShippingRule,
    orderValue: number,
  ): string | null {
    if (orderValue === undefined || orderValue === null || orderValue < 0) {
      console.warn('Invalid order value:', orderValue);
      return null;
    }

    for (const range of rule.conditionValues) {
      if (this.isValueInRange(orderValue, range)) {
        return rule.result;
      }
    }
    return null;
  }

  /**
   * Apply order quantity rule
   */
  private applyOrderQtyRule(
    rule: ShippingRule,
    quantity: number,
  ): string | null {
    if (quantity === undefined || quantity === null || quantity < 0) {
      console.warn('Invalid quantity value:', quantity);
      return null;
    }

    for (const range of rule.conditionValues) {
      if (this.isValueInRange(quantity, range)) {
        return rule.result;
      }
    }
    return null;
  }

  /**
   * Apply state rule
   */
  private applyStateRule(rule: ShippingRule, state: string): string | null {
    if (!state) {
      console.warn('State is undefined or null');
      return null;
    }

    const normalizedState = state.toLowerCase().trim();
    const normalizedConditionValues = rule.conditionValues.map(value =>
      value.toLowerCase().trim(),
    );

    return normalizedConditionValues.includes(normalizedState)
      ? rule.result
      : null;
  }

  /**
   * Apply city rule
   */
  private applyCityRule(rule: ShippingRule, city: string): string | null {
    if (!city) {
      console.warn('City is undefined or null');
      return null;
    }

    const normalizedCity = city.toLowerCase().trim();
    const normalizedConditionValues = rule.conditionValues.map(value =>
      value.toLowerCase().trim(),
    );

    return normalizedConditionValues.includes(normalizedCity)
      ? rule.result
      : null;
  }

  /**
   * Apply pincode rule
   */
  private applyPincodeRule(rule: ShippingRule, pincode: string): string | null {
    if (!pincode) {
      console.warn('Pincode is undefined or null');
      return null;
    }

    const normalizedPincode = pincode.trim();

    return rule.conditionValues.includes(normalizedPincode)
      ? rule.result
      : null;
  }

  /**
   * Apply product category rule
   */
  private applyProductCategoryRule(
    rule: ShippingRule,
    productCategory: string,
  ): string | null {
    if (!productCategory) {
      console.warn('Product categories is undefined, null, or not an array');
      return null;
    }

    // Check if any of the product categories match the rule condition values
    const hasMatch = rule.conditionValues.includes(productCategory);
    return hasMatch ? rule.result : null;
  }

  /**
   * Check if the rule result is available in recommendations
   */
  private findCarrierInRecommendations(
    ruleResult: string,
    recommendations: ClickPostRecommendationResponse,
  ): number | null {
    // Map rule result to carrier partner ID
    const carrierId = this.getCarrierPartnerId(ruleResult);

    // Check if this carrier is in recommendations
    const recommendedCarriers =
      recommendations?.result?.[0]?.preference_array || [];
    return recommendedCarriers.some(c => c.cp_id === carrierId)
      ? carrierId
      : null;
  }

  /**
   * Get fallback carrier (first recommendation)
   */
  private getFallbackCarrier(
    recommendations: ClickPostRecommendationResponse,
  ): number {
    const recommendedCarriers =
      recommendations?.result?.[0]?.preference_array || [];
    if (recommendedCarriers.length > 0) {
      return recommendedCarriers[0].cp_id;
    }
    return 105; // Default to Shiprocket if no recommendations
  }

  /**
   * Helper method to check if a value is within a range string
   * Handles ranges like "0-100", "100-200", "1000+" etc.
   */
  private isValueInRange(value: number, rangeString: string): boolean {
    try {
      // Handle "+" ranges like "1000+"
      if (rangeString.includes('+')) {
        const minValue = parseFloat(rangeString.replace('+', ''));
        return value >= minValue;
      }

      // Handle normal ranges like "0-100"
      if (rangeString.includes('-')) {
        const [min, max] = rangeString
          .split('-')
          .map(val => parseFloat(val.trim()));
        if (isNaN(min) || isNaN(max)) {
          console.warn(`Invalid range format: ${rangeString}`);
          return false;
        }
        return value >= min && value <= max;
      }

      // Handle exact values
      const exactValue = parseFloat(rangeString.trim());
      if (isNaN(exactValue)) {
        console.warn(`Invalid value format: ${rangeString}`);
        return false;
      }
      return value === exactValue;
    } catch (error) {
      console.error(`Error parsing range ${rangeString}:`, error);
      return false;
    }
  }

  /**
   * Map carrier name to ID with enhanced matching
   */
  private getCarrierPartnerId(carrierName: string): number {
    const normalizedName = carrierName.toLowerCase().trim();
    // Direct name matching
    switch (normalizedName) {
      case 'shipyaari':
        return 108;
      case 'shiprocket':
        return 105;
      default:
        // Try partial matching for flexibility
        if (
          normalizedName.includes('shipyaari') ||
          normalizedName.includes('yaari')
        ) {
          return 108;
        }
        if (
          normalizedName.includes('shiprocket') ||
          normalizedName.includes('rocket')
        ) {
          return 105;
        }

        console.warn(
          `Unknown carrier name: ${carrierName}, defaulting to Shiprocket`,
        );
        return 105; // Default to Shiprocket
    }
  }
}
