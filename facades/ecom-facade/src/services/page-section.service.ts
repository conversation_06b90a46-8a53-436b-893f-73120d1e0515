import {injectable, BindingScope, service} from '@loopback/core';
import {
  Collection,
  PageSectionWithRelations,
  ProductVariant,
  ProductVariantWithRelations,
  SectionItemWithRelations,
} from '../models';
import {SectionType} from '@local/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {ProductVariantProxyType} from '../datasources/configs';
import {ProductVariantService} from './product-variant.service';

@injectable({scope: BindingScope.TRANSIENT})
export class PageSectionService {
  constructor(
    @restService(ProductVariant)
    private readonly productVariantService: ProductVariantProxyType,
    @service(ProductVariantService)
    private readonly productVariantHelperService: ProductVariantService,
    @restService(Collection)
    private readonly collectionproxy: ModifiedRestService<Collection>,
  ) {}
  async attachPreviewUrl(pageSections: PageSectionWithRelations[]) {
    pageSections.map(page => {
      if (
        page.type === SectionType.CAROUSEL &&
        Array.isArray(page.sectionItems)
      ) {
        page.sectionItems.forEach(item => {
          if (item.imageUrl) {
            const s3key = item.imageUrl;
            const previewUrl = `${process.env.CDN_ORIGIN}/${s3key}`;
            item.previewUrl = previewUrl;
          }
        });
      }

      if (
        page.type === SectionType.BANNER &&
        Array.isArray(page.sectionItems)
      ) {
        page.sectionItems.forEach(item => {
          if (item.imageUrl) {
            const s3key = item.imageUrl;
            const previewUrl = `${process.env.CDN_ORIGIN}/${s3key}`;
            item.previewUrl = previewUrl;
          }
        });
      }

      if (
        page.type === SectionType.PRODUCT_FILTER &&
        Array.isArray(page.sectionItems)
      ) {
        page.sectionItems.forEach(item => {
          if (item.imageUrl) {
            const s3key = item.imageUrl;
            const previewUrl = `${process.env.CDN_ORIGIN}/${s3key}`;
            item.previewUrl = previewUrl;
          }
        });
      }

      if (
        page.type === SectionType.FACETS &&
        Array.isArray(page.sectionItems)
      ) {
        page.sectionItems.forEach(item => {
          if (item.imageUrl) {
            const s3key = item.imageUrl;
            const previewUrl = `${process.env.CDN_ORIGIN}/${s3key}`;
            item.previewUrl = previewUrl;
          }
        });
      }
    });
    return pageSections;
  }

  async attachProductVariants(
    pageSections: PageSectionWithRelations[],
    xOrigin: string,
  ) {
    const metaFieldsToExclude = {
      createdOn: false,
      modifiedOn: false,
      createdBy: false,
      modifiedBy: false,
      deleted: false,
      deletedOn: false,
      deletedBy: false,
    };
    await Promise.all(
      pageSections.map(async section => {
        if (!section.sectionItems?.length) return;

        await Promise.all(
          section.sectionItems.map(async (item: SectionItemWithRelations) => {
            const productIds: string[] =
              (item.metadata as {productIds?: string[]})?.productIds ?? [];

            if (productIds.length > 0) {
              const variants = await this.productVariantService.find({
                where: {id: {inq: productIds}},
                fields: metaFieldsToExclude,
                include: [
                  {
                    relation: 'productVariantPrice',
                    scope: {
                      fields: metaFieldsToExclude,
                    },
                  },
                  {
                    relation: 'featuredAsset',
                    scope: {
                      fields: metaFieldsToExclude,
                    },
                  },
                  {
                    relation: 'product',
                    scope: {
                      fields: metaFieldsToExclude,
                    },
                  },
                ],
              });

              let filteredVariants = variants;
              if (xOrigin === 'ecomdukes-customer') {
                filteredVariants =
                  await this.productVariantHelperService.filterOutInactiveSellerProductvariants(
                    variants as ProductVariantWithRelations[],
                  );
              }
              item.productVariants =
                await this.productVariantHelperService.getProductVariantsWithFeaturedPreviewUrls(
                  filteredVariants as ProductVariantWithRelations[],
                );
            }
          }),
        );
      }),
    );
    return pageSections;
  }

  async attachCollections(pageSections: PageSectionWithRelations[]) {
    await Promise.all(
      pageSections.map(async section => {
        if (!section.sectionItems?.length) return;

        await Promise.all(
          section.sectionItems.map(async (item: SectionItemWithRelations) => {
            if (item.entityType === 'collection' && item.entityId) {
              const collections = await this.collectionproxy.find({
                where: {id: item.entityId},
                limit: 1,
                fields: {name: true},
              });

              if (collections.length > 0) {
                item.collection = collections[0];
              }

              if (item.imageUrl) {
                const s3key = item.imageUrl;
                const previewUrl = `${process.env.CDN_ORIGIN}/${s3key}`;
                item.previewUrl = previewUrl;
              }
            }
          }),
        );
      }),
    );

    return pageSections;
  }

  async attachSectionItemRelations(
    pageSections: PageSectionWithRelations[],
    xOrigin: string,
  ): Promise<PageSectionWithRelations[]> {
    const withPreviewUrls = await this.attachPreviewUrl(pageSections);
    const withVariants = await this.attachProductVariants(
      withPreviewUrls,
      xOrigin,
    );
    const withCollections = await this.attachCollections(withVariants);
    return withCollections;
  }
}
