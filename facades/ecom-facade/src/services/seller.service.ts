import {
  injectable,
  BindingScope,
  service,
  inject,
  Getter,
} from '@loopback/core';
import {
  Cash<PERSON>reeVendor,
  FirmDetails,
  IAuthUserWithTenant,
  Payment,
  OrderLineItem,
  Profile,
  SampleProductDto,
  Seller,
  SellerBankDetailsDto,
  SellerDto,
  SellerStatusDto,
  SellerStore,
  VendorRequestDto,
  SellerWithRelations,
  Subscription,
} from '../models';
import {Request, RestBindings, Response, HttpErrors} from '@loopback/rest';
import {FileUploadService} from './file-upload.service';
import {multerMiddleware} from '../middlewares';
import {
  ALLOWED_STORE_FILE_EXTENTIONS,
  OrderStatus,
  UPLOAD_FILE_SIZE,
} from '@local/core';
import {S3HelperService} from './s3-helper.service';
import {AnyObject, Filter} from '@loopback/repository';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  AuthProxyType,
  FirmProxyType,
  PaymentProxyType,
  ProfileProxyType,
  SellerProxyType,
} from '../datasources/configs';
import {NotificationHelperService} from './notification-helper.service';
import {AuthenticationBindings} from 'loopback4-authentication';
import {AuthUser} from '@sourceloop/authentication-service';
import {generateRandomAlphanumeric} from '../utils';
import {SellerStatus} from '../enums';
import {SubscriptionStatus} from '../constants';

const singleFileFields = ['dp', 'logo', 'banner', 'signature'];
interface EmailData {
  productName: string;
  supportId: string;
  brand: string;
  rejectionReason?: string; // Make it optional
  onHoldReason?: string; // Make it optional
}
@injectable({scope: BindingScope.TRANSIENT})
export class SellerService {
  private dto: SellerDto = new SellerDto();
  private token: string;
  constructor(
    @service(FileUploadService)
    private readonly fileUploadService: FileUploadService,
    @service(S3HelperService)
    private readonly s3HelperService: S3HelperService,
    @inject(RestBindings.Http.RESPONSE)
    private readonly response: Response,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @restService(Profile)
    private readonly profileProxyConfig: ProfileProxyType,
    @restService(SellerStore)
    private readonly sellerStoreProxyService: ModifiedRestService<SellerStore>,
    @service(NotificationHelperService)
    private readonly notificationHelper: NotificationHelperService,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @restService(Payment)
    private readonly paymentServiceProxy: PaymentProxyType,
    @restService(FirmDetails)
    private readonly firmDetailsProxy: FirmProxyType,
    @restService(OrderLineItem)
    private readonly orderItemProxy: ModifiedRestService<OrderLineItem>,
    @restService(Subscription)
    private readonly subscriptionProxyService: ModifiedRestService<Subscription>,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async parseSellerDto(request: Request) {
    await new Promise<void>((resolve, reject) => {
      multerMiddleware.any()(request, this.response, (err: unknown) => {
        if (err) {
          reject(new HttpErrors.UnprocessableEntity('File upload error'));
        } else {
          resolve();
        }
      });
    });
  }

  async onboardSeller(): Promise<Seller> {
    await this.parseSellerDto(this.request);
    this.dto = this.request.body as SellerDto;
    await this.prepareSellerDto();
    return this.sellerProxyService.onboard(
      this.dto as SellerDto,
      this.request.headers.authorization ?? '',
    );
  }

  private async prepareSellerDto() {
    const files = this.request.files as Express.Multer.File[];
    const fileMap: Record<string, Express.Multer.File[]> = {};

    for (const file of files) {
      if (!fileMap[file.fieldname]) {
        fileMap[file.fieldname] = [];
      }
      fileMap[file.fieldname].push(file);
    }
    await this.handleSingleFiles(fileMap);
    await this.handleSampleProductFiles(fileMap);
  }

  private async handleSampleProductFiles(
    fileMap: Record<string, Express.Multer.File[]>,
  ): Promise<void> {
    // Step 1: Extract sample product entries from flat dto
    const sampleProductList: AnyObject[] = [];

    Object.keys(this.dto).forEach(key => {
      const match = key.match(/^sampleProductImages\[(\d+)\]\.(\w+)$/);
      if (match) {
        const index = parseInt(match[1], 10);
        const field = match[2];
        sampleProductList[index] = sampleProductList[index] || {};
        sampleProductList[index][field as keyof SampleProductDto] = (
          this.dto as SellerDto
        )[key as keyof SellerDto] as string | string[];
        delete (this.dto as SellerDto)[key as keyof SellerDto];
      }
    });

    // Step 2: Handle thumbnail uploads
    const uploadPromises = sampleProductList.map((product, productIndex) => {
      const thumbnailsKey = `sampleProductImages[${productIndex}].thumbnails`;
      const thumbnailFiles = fileMap[thumbnailsKey] || [];

      thumbnailFiles.forEach(file => {
        this.fileUploadService.validateExtension(
          ALLOWED_STORE_FILE_EXTENTIONS,
          file,
        );
        this.fileUploadService.validateSize(UPLOAD_FILE_SIZE, file);
      });

      if (thumbnailFiles.length > 0) {
        return Promise.all(
          thumbnailFiles.map(file =>
            this.s3HelperService.uploadFileToS3(
              file,
              process.env.AWS_S3_BUCKET!,
            ),
          ),
        ).then(uploadedUrls => {
          product.thumbnails = uploadedUrls;
        });
      }

      return Promise.resolve();
    });

    await Promise.all(uploadPromises);

    // Step 3: Update back to dto
    this.dto.sampleProductImages = sampleProductList as SampleProductDto[];
  }

  private async handleSingleFiles(
    fileMap: Record<string, Express.Multer.File[]>,
  ): Promise<void> {
    // Map top-level files (dp, logo, banner, signature)
    singleFileFields.forEach(field => {
      const [file] = fileMap[field] || [];
      if (file) {
        this.fileUploadService.validateExtension(
          ALLOWED_STORE_FILE_EXTENTIONS,
          file,
        );
        this.fileUploadService.validateSize(UPLOAD_FILE_SIZE, file);
      }
    });

    await Promise.all(
      singleFileFields.map(field => {
        const [file] = fileMap[field] || [];
        if (file) {
          return this.uploadAndMap(file, field);
        }
        return null;
      }),
    );
  }

  private async uploadAndMap(
    files: Express.Multer.File[] | Express.Multer.File,
    field: string,
  ) {
    if (Array.isArray(files)) {
      const response = await Promise.all(
        files.map(file =>
          this.s3HelperService.uploadFileToS3(file, process.env.AWS_S3_BUCKET!),
        ),
      );
      (this.dto[field as keyof SellerStore] as unknown as string[]) =
        response.map(res => res);
    } else {
      const response = await this.s3HelperService.uploadFileToS3(
        files,
        process.env.AWS_S3_BUCKET!,
      );
      (this.dto[field as keyof SellerStore] as unknown as string) = response;
    }
  }

  async updateSellerStoreById(id: string): Promise<void> {
    await this.parseSellerDto(this.request);
    const body = this.request.body as Partial<SellerStore>;
    const files = this.request.files as Express.Multer.File[];
    const fileMap: Record<string, Express.Multer.File[]> = {};

    for (const file of files) {
      if (!fileMap[file.fieldname]) {
        fileMap[file.fieldname] = [];
      }
      fileMap[file.fieldname].push(file);
    }
    await this.handleSingleFiles(fileMap);

    await this.sellerStoreProxyService.updateById(
      id,
      {
        ...this.dto,
        ...body,
        workingHours: Number(body.workingHours) || 0,
        hideWorkingHours: String(body.hideWorkingHours) === 'true',
        allowBulkOrder: String(body.allowBulkOrder) === 'true',
        allowCategorisation: String(body.allowCategorisation) === 'true',
      } as SellerStore,
      this.request.headers.authorization ?? '',
    );
  }

  async updateStatusById(
    id: string,
    sellerPayload: SellerStatusDto,
  ): Promise<void> {
    const seller = await this.sellerProxyService.findById(id);

    const user = await this.profileProxyConfig.getUserTenantById(
      seller.userTenantId,
      this.token,
      {include: [{relation: 'user'}]},
    );

    if (sellerPayload.status === SellerStatus.INACTIVE) {
      const sellerOrders = await this.orderItemProxy.find({
        where: {
          sellerId: id,
          status: {inq: [OrderStatus.Pending, OrderStatus.RefundInitiated]},
        },
      });

      if (sellerOrders && sellerOrders.length > 0) {
        throw HttpErrors.BadRequest(
          'Your account has pending orders or active refund window. Deactivation not allowed at this moment.',
        );
      }
    }

    await this.sellerProxyService.updateSellerStatusById(
      id,
      sellerPayload,
      this.token,
    );

    // Skip email if status is INACTIVE
    if (sellerPayload.status === SellerStatus.INACTIVE) {
      return;
    }

    // Prepare email template data
    const emailData: EmailData = {
      productName: 'Ecomdukes',
      supportId: '<EMAIL>',
      brand: 'Ecomdukes',
    };

    let templateName = '';
    let subject = '';

    if (sellerPayload.status === SellerStatus.APPROVED) {
      templateName = 'seller-approved-email.hbs';
      subject = 'Your Seller Account Has Been Approved!';
    } else if (sellerPayload.status === SellerStatus.REJECTED) {
      emailData['rejectionReason'] = sellerPayload.rejectionReason;
      templateName = 'seller-rejection-email.hbs';
      subject = 'Your Seller Account Application Was Rejected';
    } else if (sellerPayload.status === SellerStatus.ON_HOLD) {
      emailData['onHoldReason'] = sellerPayload.onHoldReason;
      templateName = 'seller-on-hold-email.hbs';
      subject = 'Your Seller Account Application is On Hold';
    }

    const formattedEmailData: {[key: string]: string} = Object.fromEntries(
      Object.entries(emailData).filter(([_, value]) => value !== undefined),
    );

    // Send email notification
    await this.notificationHelper.sendEmail(
      templateName,
      subject,
      formattedEmailData,
      user.user.email ?? '',
      `${user.user.firstName} ${user.user.lastName}`,
    );

    // Send push notification
    await this.sendPushNotificationToSeller(
      user.id!, // Use user.id which is the userTenantId
      sellerPayload.status,
      sellerPayload.rejectionReason,
      `${user.user.firstName} ${user.user.lastName}`,
      sellerPayload.onHoldReason,
    );
  }

  /**
   * Send push notification to seller based on status update using notification helper
   */
  private async sendPushNotificationToSeller(
    userTenantId: string,
    status: SellerStatus,
    rejectionReason?: string,
    userName?: string,
    onHoldReason?: string,
  ): Promise<void> {
    try {
      let notificationSubject = '';
      let notificationBody = '';
      let isCritical = false;

      // Prepare notification content based on status
      if (status === SellerStatus.APPROVED) {
        notificationSubject = 'Seller Account Approved! 🎉';
        notificationBody = `Congratulations ${userName || 'Seller'}! Your seller account has been approved. You can now start selling on Ecomdukes.`;
        isCritical = false;
      } else if (status === SellerStatus.REJECTED) {
        notificationSubject = 'Seller Account Application Update';
        notificationBody = rejectionReason
          ? `Your seller account application was rejected. Reason: ${rejectionReason}. Please contact support for more information.`
          : `Your seller account application was rejected. Please contact support for more information.`;
        isCritical = true; // Mark rejections as critical
      } else if (status === SellerStatus.ON_HOLD) {
        notificationSubject = 'Seller Account Application On Hold ⏳';
        notificationBody = onHoldReason
          ? `Your seller account application has been put on hold. Reason: ${onHoldReason}. We will review your application and get back to you soon.`
          : `Your seller account application has been put on hold. We will review your application and get back to you soon.`;
        isCritical = false;
      } else {
        // For other statuses, don't send push notification
        return;
      }

      // Use notification helper service to send push notification
      await this.notificationHelper.sendPushNotification(
        notificationSubject,
        notificationBody,
        userTenantId,
        userName || 'Seller',
        isCritical,
      );
    } catch (error) {
      console.error(
        `❌ Failed to send push notification to user: ${userTenantId}`,
        error,
      );
      // Don't throw error to avoid breaking the main updateStatusById flow
    }
  }

  async addBankDetails(
    sellerId: string,
    bankDetails: SellerBankDetailsDto,
  ): Promise<void> {
    const user = await this.authProvider.getMe(this.token, 'ecomdukes-seller');
    const seller = await this.sellerProxyService.findById(user.profileId);
    const vendorId = seller.vendorId ?? generateRandomAlphanumeric(10);

    const vendor = new VendorRequestDto({
      ...bankDetails,
      vendorId,
      status: 'ACTIVE',
      name: bankDetails.accountHolder,
      email: user.email ?? '',
      phone: user.phone ?? '',
      verifyAccount: true,
      isUpdate: seller.vendorId ? true : false,
    });
    // Call payment service to create vendor in Cashfree
    await this.paymentServiceProxy.createVendor(vendor, this.token);
    if (!seller.vendorId) {
      await this.sellerProxyService.updateById(sellerId, {
        vendorId,
      });
    }
  }

  async getVendor(): Promise<CashFreeVendor> {
    const user = await this.authProvider.getMe(this.token, 'ecomdukes-seller');

    const seller = await this.sellerProxyService.findById(user.profileId);
    if (!seller.vendorId) {
      throw new HttpErrors.NotFound('Vendor not found');
    }
    return this.paymentServiceProxy.getVendor(seller.vendorId, this.token);
  }

  async findWithPreSignedUrls(filter?: Filter<Seller>): Promise<Seller[]> {
    const sellers = (await this.sellerProxyService.find(
      filter,
    )) as SellerWithRelations[];

    const cdnOrigin = process.env.CDN_ORIGIN!;

    return sellers.map(seller => {
      const preSignedPhotoUrl = seller.userTenant?.user?.photoUrl
        ? `${cdnOrigin}/${seller.userTenant.user.photoUrl}`
        : null;

      return {
        ...seller,
        preSignedPhotoUrl,
      } as unknown as Seller;
    });
  }

  async getSubscription(sellerId: string): Promise<Subscription> {
    const subscriptions = await this.subscriptionProxyService.find(
      {where: {subscriberId: sellerId, status: SubscriptionStatus.ACTIVE}},
      this.token,
    );

    return subscriptions[0];
  }
}
