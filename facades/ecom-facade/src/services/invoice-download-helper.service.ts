import {injectable, BindingScope, inject, service} from '@loopback/core';
import {
  Order,
  OrderLineItem,
  OrderLineItemWithRelations,
  OrderWithRelations,
} from '../models';
import * as Handlebars from 'handlebars';
import {HttpErrors, RestBindings, Request} from '@loopback/rest';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {OrderProxyType} from '../datasources/configs';
import {InvoiceDownload} from './invoice-download.service';
import * as fs from 'fs';
import * as path from 'path';
// import {ToWords} from 'to-words';
import {fieldsExcludeMetaFields} from '../constants';
import {SellerStoreExtendedService} from './seller-store.service';
import {EcomdukeserviceRequest} from '../models/ecom-service/ecomdukeserice-request.model';
import {Ecomdukeservice} from '../models/ecom-service/ecomdukeservice.model';

// const toWords = new ToWords({
//   localeCode: 'en-IN',
//   converterOptions: {
//     currency: true,
//     ignoreDecimal: false,
//     ignoreZeroCurrency: false,
//     doNotAddOnly: false,
//   },
// });

@injectable({scope: BindingScope.TRANSIENT})
export class InvoiceDownloadHelperService {
  private token: string;
  constructor(
    @restService(Order)
    public orderProxy: OrderProxyType,
    @inject('services.InvoiceDownload')
    private readonly invoiceDownloadService: InvoiceDownload,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(SellerStoreExtendedService)
    private readonly sellerStoreService: SellerStoreExtendedService,
    @restService(EcomdukeserviceRequest)
    private readonly requestProxy: ModifiedRestService<EcomdukeserviceRequest>,
    @restService(Ecomdukeservice)
    private readonly ecomdukeservice: ModifiedRestService<Ecomdukeservice>,
    @restService(OrderLineItem)
    private orderItemProxy: ModifiedRestService<OrderLineItem>,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  async generatePdfFromOrder(
    orderId: string,
    xOrigin: string,
  ): Promise<Buffer> {
    if (xOrigin === 'ecomdukes-customer') {
      const order = await this.orderProxy.findById(orderId, {
        include: [
          {
            relation: 'orderLineItems',
            scope: {
              fields: fieldsExcludeMetaFields,
              include: [
                {
                  relation: 'productVariant',
                  scope: {
                    fields: {
                      id: true,
                      name: true,
                    },
                    include: [
                      {
                        relation: 'product',
                        scope: {
                          fields: {
                            description: true,
                            sellerId: true,
                          },
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            relation: 'billingAddress',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
          {
            relation: 'shippingAddress',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
          {
            relation: 'customer',
            scope: {
              fields: fieldsExcludeMetaFields,
              include: [
                {
                  relation: 'userTenant',
                  scope: {
                    fields: fieldsExcludeMetaFields,
                    include: [
                      {
                        relation: 'user',
                        scope: {
                          fields: fieldsExcludeMetaFields,
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      });

      if (!order) throw new HttpErrors.NotFound('Order not found');

      const invoiceData = await this.buildInvoiceTemplateData(order);

      const html = this.renderHtml(invoiceData);

      const response: {pdfUrl: string} =
        await this.invoiceDownloadService.generatePdf({
          html: html,
        });

      if (!response?.pdfUrl) {
        throw new HttpErrors.InternalServerError('PDF generation failed');
      }

      const pdfBuffer = await fetch(response.pdfUrl).then(res =>
        res.arrayBuffer(),
      );

      return Buffer.from(pdfBuffer);
    }

    if (xOrigin === 'ecomdukes-seller') {
      const orderItem = await this.orderItemProxy.findById(
        orderId,
        {
          include: [
            {
              relation: 'productVariant',
              scope: {
                fields: {
                  id: true,
                  name: true,
                },
                include: [
                  {
                    relation: 'product',
                    scope: {
                      fields: {
                        description: true,
                        sellerId: true,
                      },
                    },
                  },
                ],
              },
            },
          ],
        },
        this.token,
      );

      const order = await this.orderProxy.findById(
        orderItem.orderId,
        {
          include: [
            {
              relation: 'billingAddress',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
            {
              relation: 'shippingAddress',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
            {
              relation: 'customer',
              scope: {
                fields: fieldsExcludeMetaFields,
                include: [
                  {
                    relation: 'userTenant',
                    scope: {
                      fields: fieldsExcludeMetaFields,
                      include: [
                        {
                          relation: 'user',
                          scope: {
                            fields: fieldsExcludeMetaFields,
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
          ],
        },
        this.token,
      );

      const invoiceData = await this.buildInvoiceTemplateDataForSeller(
        orderItem,
        order,
      );

      const html = this.renderHtml(invoiceData);

      const response: {pdfUrl: string} =
        await this.invoiceDownloadService.generatePdf({
          html: html,
        });

      if (!response?.pdfUrl) {
        throw new HttpErrors.InternalServerError('PDF generation failed');
      }

      const pdfBuffer = await fetch(response.pdfUrl).then(res =>
        res.arrayBuffer(),
      );

      return Buffer.from(pdfBuffer);
    }

    throw new HttpErrors.BadRequest(`Unsupported x-origin: ${xOrigin}`);
  }

  private async buildInvoiceTemplateData(
    order: OrderWithRelations,
  ): Promise<unknown> {
    const itemsBySeller: Record<string, OrderLineItemWithRelations[]> = {};

    for (const item of order.orderLineItems ?? []) {
      const sellerId = item.sellerId;
      if (!sellerId) continue;
      if (!itemsBySeller[sellerId]) itemsBySeller[sellerId] = [];
      itemsBySeller[sellerId].push(item);
    }

    const sellerPages = await Promise.all(
      Object.entries(itemsBySeller).map(async ([sellerId, items]) => {
        const seller = await this.sellerStoreService.getStoreWithUserDetails(
          this.token,
          sellerId,
          {
            fields: fieldsExcludeMetaFields,
          },
        );

        const fullAddress = `${seller.addressLine1 ?? ''}, ${seller.addressLine2 ?? ''}, ${seller.city}, ${seller.state} ${seller.pincode}, ${seller.country}`;

        const itemList = items.map(item => ({
          name: item.productVariant?.name,
          quantity: item.quantity,
          rate: item.unitPrice,
          amount: item.unitPrice * item.quantity,
        }));

        const total = itemList.reduce((acc, cur) => acc + cur.amount, 0);

        return {
          companyName: 'EcomDukes',
          companyAddress:
            '37/2259,Kollamkudy Tower,Maleppally Road, Thrikkakara, Vazhakkala, Ernakulam, Kerala - 682021',
          companyEmail: '<EMAIL>',
          invoiceNumber: order.invoiceId,
          invoiceDate: new Date(order.createdOn ?? '').toLocaleDateString(),
          orderNumber: order.orderId,
          customerName: `${order.customer?.userTenant?.user.firstName} ${order.customer?.userTenant?.user.lastName}`,
          billingAddress: order.billingAddress,
          shippingAddress: order.shippingAddress,
          items: itemList,
          subTotal: total,
          total: total,
          // amountInWords: this.numberToWords(total),
          paymentReference: order.orderReferenceId,
          seller: {
            name: seller.legalName ?? seller.storeName,
            address: fullAddress,
            signatureUrl: seller.signature
              ? await this.getBase64Image(seller.signature)
              : undefined,
          },
        };
      }),
    );

    return {
      sellerPages,
    };
  }

  private async buildInvoiceTemplateDataForSeller(
    orderItem: OrderLineItemWithRelations,
    order: OrderWithRelations,
  ): Promise<unknown> {
    const sellerId = orderItem.sellerId;
    if (!sellerId) {
      throw new HttpErrors.UnprocessableEntity(
        'Missing sellerId for the line item',
      );
    }

    const seller = await this.sellerStoreService.getStoreWithUserDetails(
      this.token,
      sellerId,
      {
        fields: fieldsExcludeMetaFields,
      },
    );

    const fullAddress = `${seller.addressLine1 ?? ''}, ${seller.addressLine2 ?? ''}, ${seller.city}, ${seller.state} ${seller.pincode}, ${seller.country}`;

    const itemList = [
      {
        name: orderItem.productVariant?.name,
        quantity: orderItem.quantity,
        rate: orderItem.unitPrice,
        amount: orderItem.unitPrice * orderItem.quantity,
      },
    ];

    const total = itemList.reduce((acc, cur) => acc + cur.amount, 0);

    const sellerPage = {
      companyName: 'EcomDukes',
      companyAddress:
        '37/2259,Kollamkudy Tower,Maleppally Road, Thrikkakara, Vazhakkala, Ernakulam, Kerala - 682021',
      companyEmail: '<EMAIL>',
      invoiceNumber: order.invoiceId,
      invoiceDate: new Date(order.createdOn ?? '').toLocaleDateString(),
      orderNumber: order.orderId,
      customerName:
        `${order.customer?.userTenant?.user.firstName ?? ''} ${order.customer?.userTenant?.user.lastName ?? ''}`.trim(),
      billingAddress: order.billingAddress,
      shippingAddress: order.shippingAddress,
      items: itemList,
      subTotal: total,
      total: total,
      // amountInWords: this.numberToWords(total),
      paymentReference: order.orderReferenceId,
      seller: {
        name: seller.legalName ?? seller.storeName,
        address: fullAddress,
        signatureUrl: seller.signature
          ? await this.getBase64Image(seller.signature)
          : undefined,
      },
    };

    return {
      sellerPages: [sellerPage],
    };
  }

  private renderHtml(data: unknown): string {
    const templatePath = path.join(__dirname, '../templates/invoice.hbs');
    const templateSource = fs.readFileSync(templatePath, 'utf-8');
    const template = Handlebars.compile(templateSource);
    Handlebars.registerHelper('inc', (value: number) => value + 1);
    return template(data);
  }

  async generatePdfFromServiceRequest(requestId: string): Promise<Buffer> {
    const request = await this.requestProxy.findById(requestId, {
      include: [
        {
          relation: 'seller',
          scope: {
            include: [
              {
                relation: 'userTenant',
                scope: {
                  include: [{relation: 'user'}],
                },
              },
            ],
          },
        },
      ],
    });

    if (!request) throw new HttpErrors.NotFound('Service request not found');

    const seller = await this.sellerStoreService.getStoreWithUserDetails(
      this.token,
      request.sellerId,
      {fields: fieldsExcludeMetaFields},
    );

    const fullAddress = `${seller.addressLine1 ?? ''}, ${seller.addressLine2 ?? ''}, ${seller.city}, ${seller.state} ${seller.pincode}, ${seller.country}`;

    const invoiceNumber = `INV-${request.id?.slice(0, 8)?.toUpperCase() ?? Date.now()}`;

    const service = await this.ecomdukeservice.findById(
      request.ecomdukeserviceId,
    );

    const invoiceData = {
      companyName: 'EcomDukes',
      companyAddress:
        '37/2259,Kollamkudy Tower,Maleppally Road, Thrikkakara, Vazhakkala, Ernakulam, Kerala - 682021',
      companyEmail: '<EMAIL>',
      invoiceNumber: invoiceNumber,
      invoiceDate: new Date(request.createdOn ?? '').toLocaleDateString(),
      customerName: seller.storeName,
      serviceName: service.name,
      serviceDescription: service.description, // If needed, ensure `description` exists in model
      amount: request.paidAmount ?? 0,
      paymentReference: request.paymentReference ?? '',
      status: request.status,
      seller: {
        name: seller.storeName,
        address: fullAddress, // Ensure this is computed before
        signatureUrl: seller.signature
          ? await this.getBase64Image(seller.signature)
          : undefined,
      },
    };

    const html = this.renderServiceRequestHtml(invoiceData);

    const response = await this.invoiceDownloadService.generatePdf({html});

    if (!response?.pdfUrl) {
      throw new HttpErrors.InternalServerError('PDF generation failed');
    }

    const pdfBuffer = await fetch(response.pdfUrl).then(res =>
      res.arrayBuffer(),
    );

    return Buffer.from(pdfBuffer);
  }

  private renderServiceRequestHtml(data: unknown): string {
    const templatePath = path.join(
      __dirname,
      '../templates/service-request-invoice.hbs',
    );
    const templateSource = fs.readFileSync(templatePath, 'utf-8');
    const template = Handlebars.compile(templateSource);
    return template(data);
  }

  // private numberToWords(amount: number): string {
  //   return toWords.convert(amount);
  // }

  getBase64Image = async (url: string): Promise<string | null> => {
    try {
      const res = await fetch(url);
      const buffer = Buffer.from(await res.arrayBuffer());
      const mimeType = res.headers.get('content-type') ?? 'image/jpeg';
      return `data:${mimeType};base64,${buffer.toString('base64')}`;
    } catch (err) {
      console.error('Failed to fetch image:', err);
      return null;
    }
  };
}
