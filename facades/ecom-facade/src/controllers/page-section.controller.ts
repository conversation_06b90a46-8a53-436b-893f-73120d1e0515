import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  RestBindings,
  Request,
} from '@loopback/rest';
import {PageSection, PageSectionBulkDto, PageSectionDto} from '../models';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {PageSectionProxyType} from '../datasources/configs';
import {inject} from '@loopback/context';
import {service} from '@loopback/core';
import {PageSectionService} from '../services';

const basePath = '/page-sections';

export class PageSectionController {
  private token: string;
  constructor(
    @restService(PageSection)
    public pageSectionProxy: PageSectionProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(PageSectionService)
    private readonly pageSectionService: PageSectionService,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreatePageSection]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(PageSection)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSectionDto, {
            title: 'NewPageSection',
            exclude: ['id'],
          }),
        },
      },
    })
    pageSection: Omit<PageSectionDto, 'id'>,
  ): Promise<PageSection> {
    return this.pageSectionProxy.createPageSection(pageSection, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreatePageSection]})
  @post(`${basePath}/bulk`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model bulk instance',
        content: {
          schema: {
            type: 'array',
            items: getModelSchemaRef(PageSection),
          },
        },
      },
    },
  })
  async bulk(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSectionBulkDto, {
            title: 'NewBulkPageSection',
          }),
        },
      },
    })
    pageSections: PageSectionBulkDto,
  ): Promise<PageSection> {
    return this.pageSectionProxy.createBulkPageSection(
      pageSections,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPageSection]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(PageSection) where?: Where<PageSection>,
  ): Promise<Count> {
    return this.pageSectionProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPageSection]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of PageSection model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(PageSection, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.header.string('x-origin') xOrigin: string,
    @param.filter(PageSection) filter?: Filter<PageSection>,
  ): Promise<PageSection[]> {
    const pageSections = await this.pageSectionProxy.find(filter);
    return this.pageSectionService.attachSectionItemRelations(
      pageSections,
      xOrigin,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPageSection]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(PageSection, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PageSection, {exclude: 'where'})
    filter?: FilterExcludingWhere<PageSection>,
  ): Promise<PageSection> {
    return this.pageSectionProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSection, {partial: true}),
        },
      },
    })
    pageSection: PageSection,
  ): Promise<void> {
    await this.pageSectionProxy.updateById(id, pageSection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @put(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() pageSection: PageSection,
  ): Promise<void> {
    await this.pageSectionProxy.replaceById(id, pageSection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeletePageSection]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.pageSectionProxy.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @patch(`${basePath}/{id}/reorder`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection PATCH success',
      },
    },
  })
  async reorder(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSection, {partial: true}),
        },
      },
    })
    pageSection: PageSection,
  ): Promise<void> {
    await this.pageSectionProxy.reorderPageSection(id, pageSection, this.token);
  }
}
