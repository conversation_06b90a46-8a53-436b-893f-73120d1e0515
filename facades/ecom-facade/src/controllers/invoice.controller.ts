// Uncomment these imports to begin using these cool features!

import {PermissionKeys} from '@local/core';
import {get, param, response, RestBindings, Response} from '@loopback/rest';
import {STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {inject, service} from '@loopback/core';
import {InvoiceDownloadHelperService} from '../services';

const basePath = 'invoices';

export class InvoiceController {
  constructor(
    @service(InvoiceDownloadHelperService)
    private readonly invoiceDownloadHelperService: InvoiceDownloadHelperService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{orderId}/download`)
  @response(STATUS_CODE.OK, {
    description: 'Download Zoho Invoice PDF',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadInvoice(
    @param.header.string('x-origin') xOrigin: string,
    @param.path.string('orderId') orderId: string,
    @inject(RestBindings.Http.RESPONSE) res: Response,
  ): Promise<void> {
    const buffer = await this.invoiceDownloadHelperService.generatePdfFromOrder(
      orderId,
      xOrigin,
    );

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=invoice-${orderId}.pdf`,
    );
    res.status(200).send(buffer);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(`${basePath}/service-request/{requestId}/download`)
  @response(STATUS_CODE.OK, {
    description: 'Download Service Request Invoice PDF',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadServiceRequestInvoice(
    @param.path.string('requestId') requestId: string,
    @inject(RestBindings.Http.RESPONSE) res: Response,
  ): Promise<void> {
    const buffer =
      await this.invoiceDownloadHelperService.generatePdfFromServiceRequest(
        requestId,
      );

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=service-request-invoice-${requestId}.pdf`,
    );
    res.status(200).send(buffer);
  }
}
