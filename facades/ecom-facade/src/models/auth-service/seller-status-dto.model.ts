import {Model, model, property} from '@loopback/repository';
import {SellerStatus} from '../../enums';

@model()
export class SellerStatusDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  status: SellerStatus;

  @property({
    type: 'string',
  })
  verificationCode?: string;

  @property({
    type: 'string',
  })
  rejectionReason?: string;

  @property({
    type: 'string',
  })
  onHoldReason?: string;

  constructor(data?: Partial<SellerStatusDto>) {
    super(data);
  }
}

export interface SellerStatusDtoRelations {
  // describe navigational properties here
}

export type SellerStatusDtoWithRelations = SellerStatusDto &
  SellerStatusDtoRelations;
